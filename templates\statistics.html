<!DOCTYPE html>
<html lang="ar" data-bs-theme="dark" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="theme-color" content="#212529">
    <title>إحصائيات المتاجر - Loacker</title>

    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://cdn.replit.com/agent/bootstrap-agent-dark-theme.min.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">

    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css" />

    <!-- Google Fonts - Tajawal & Playfair Display (Classic European Font) -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&family=Playfair+Display:wght@400;700;900&display=swap">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <!-- Mobile CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/mobile.css') }}">
    <!-- Mobile Buttons CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/mobile-buttons.css') }}">
    <!-- Device Specific CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/device-specific.css') }}">
    <!-- Notification Badge CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/notification-badge.css') }}">
    <!-- Chart.js CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.css">
    <!-- Statistics Fixes CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/statistics-fixes.css') }}">
    <style>
        /* تنسيقات خاصة بصفحة الإحصائيات */
        .stats-card {
            transition: all 0.3s ease;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            background-color: #212529;
            border: 1px solid #2a2a2a;
        }

        .stats-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }

        .stats-icon {
            font-size: 2.5rem;
            opacity: 0.8;
        }

        .stats-header {
            background: linear-gradient(135deg, var(--loacker-red-dark), var(--loacker-red));
            color: white;
            padding: 15px;
            border-radius: 10px 10px 0 0;
        }

        .stats-header.green {
            background: linear-gradient(135deg, #11998e, #38ef7d);
        }

        .stats-header.orange {
            background: linear-gradient(135deg, #FF512F, #DD2476);
        }

        .chart-container {
            position: relative;
            height: 300px;
            margin-bottom: 20px;
        }

        .type-A { color: #4CAF50; }
        .type-B { color: #2196F3; }
        .type-D { color: #FF9800; }

        .region-badge {
            font-size: 0.8rem;
            padding: 5px 10px;
            margin: 2px;
            border-radius: 15px;
        }

        .map-marker {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background-color: var(--loacker-red);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
        }

        /* تنسيقات علامة المتجر المحدد */
        .map-marker.selected {
            width: 40px;
            height: 40px;
            background-color: #4CAF50;
            border: 2px solid white;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }

        .region-marker-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: rgba(213, 0, 0, 0.8);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 16px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
            border: 2px solid white;
        }

        .district-marker-icon {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background-color: rgba(106, 17, 203, 0.8);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 14px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
            border: 2px solid white;
        }

        /* أنماط للمناطق الفرعية في الجدول */
        .main-region {
            background-color: rgba(0, 0, 0, 0.03);
        }

        .toggle-subregions {
            cursor: pointer;
            transition: transform 0.3s;
        }

        .toggle-subregions:hover {
            color: var(--loacker-red);
        }

        .subregion-row {
            background-color: rgba(106, 17, 203, 0.05);
        }

        .store-popup h6 {
            margin-bottom: 5px;
            color: var(--loacker-red);
        }

        .region-popup h5 {
            color: var(--loacker-red);
            margin-bottom: 10px;
        }

        /* تنسيقات البطاقات */
        .card {
            border-radius: 10px;
            border: 1px solid #2a2a2a;
            background-color: #212529;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }

        .card:hover {
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }

        .card-header {
            background-color: #2a2a2a;
            border-bottom: 1px solid #333;
            padding: 15px;
            border-radius: 10px 10px 0 0 !important;
        }

        /* تنسيقات الجدول */
        .table {
            color: #e0e0e0;
        }

        .table-hover tbody tr:hover {
            background-color: rgba(213, 0, 0, 0.1);
        }

        .table thead th {
            background-color: #2a2a2a;
            color: #fff;
            border-bottom: 2px solid var(--loacker-red);
        }

        /* تنسيقات الخريطة */
        #stats-map {
            border: 2px solid #2a2a2a;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        /* تنسيقات الشارات */
        .badge {
            padding: 6px 10px;
            border-radius: 20px;
            font-weight: 500;
        }

        /* تنسيقات شريط التقدم */
        .progress {
            height: 20px;
            background-color: #2a2a2a;
            border-radius: 10px;
            overflow: hidden;
        }

        .progress-bar {
            background: linear-gradient(135deg, var(--loacker-red-dark), var(--loacker-red));
            color: white;
            font-weight: 600;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }

        /* تنسيقات الأزرار */
        .btn-primary {
            background: linear-gradient(135deg, var(--loacker-red-dark), var(--loacker-red));
            border: none;
            color: white;
            font-weight: 500;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, var(--loacker-red), var(--loacker-red-light));
            transform: translateY(-2px);
            box-shadow: 0 4px 10px rgba(213, 0, 0, 0.3);
        }

        /* تنسيقات للشاشات الصغيرة */
        @media (max-width: 768px) {
            .chart-container {
                height: 250px;
            }

            #stats-map {
                height: 350px !important;
            }

            .stats-card .display-4 {
                font-size: 2.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- واجهة الكمبيوتر -->
    <div class="container-fluid py-3">
        <header class="mb-4">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <div></div> <!-- عنصر فارغ للمحاذاة -->
                <div class="text-end">
                    {% if current_user.is_authenticated %}
                        <a href="{{ url_for('profile') }}" class="btn btn-outline-info me-2">
                            <i class="fas fa-user"></i> الملف الشخصي
                        </a>
                        {% if current_user.role_id == 1 %}
                            <a href="{{ url_for('admin_panel') }}" class="btn btn-outline-primary me-2">
                                <i class="fas fa-cog"></i> لوحة التحكم
                            </a>
                            <a href="{{ url_for('admin_panel') }}#pendingStores" class="btn btn-outline-danger me-2 position-relative" id="notificationBell">
                                <i class="fas fa-bell"></i>
                                <span id="pendingStoresBadgeBell" class="notification-badge d-none">0</span>
                            </a>
                        {% endif %}
                        <a href="{{ url_for('logout') }}" class="btn btn-outline-danger">
                            <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
                        </a>
                    {% else %}
                        <a href="{{ url_for('login') }}" class="btn btn-outline-primary me-2">
                            <i class="fas fa-sign-in-alt"></i> تسجيل الدخول
                        </a>
                        <a href="{{ url_for('register') }}" class="btn btn-outline-success">
                            <i class="fas fa-user-plus"></i> إنشاء حساب
                        </a>
                    {% endif %}
                </div>
            </div>

            <div class="d-flex justify-content-between align-items-center">
                <h1 class="h2 mb-0">
                    <a href="{{ url_for('index') }}" class="text-decoration-none text-danger">
                        <i class="fas fa-store me-2"></i>Loacker
                    </a>
                </h1>
                <div>
                    <a href="{{ url_for('index') }}" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-home"></i> الرئيسية
                    </a>
                </div>
            </div>
        </header>

        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <div class="container mt-4">
        <div class="row mb-4">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-body">
                        <h2 class="mb-3 text-center">
                            <i class="fas fa-chart-pie me-2"></i>
                            إحصائيات المتاجر
                        </h2>
                        <p class="text-muted text-center">تحليل شامل لتوزيع المتاجر حسب المناطق والأنواع</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- أدوات التصفية والتحليل -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-filter me-2"></i>
                            خيارات التصفية والتحليل
                        </h5>
                    </div>
                    <div class="card-body">
                        <form id="stats-filter-form" class="row g-3">
                            <div class="col-md-6">
                                <label for="region-filter" class="form-label">المنطقة</label>
                                <select class="form-select" id="region-filter">
                                    <option value="all" selected>جميع المناطق</option>
                                    <!-- سيتم ملؤها ديناميكياً بواسطة JavaScript -->
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="store-type-filter" class="form-label">نوع المتجر</label>
                                <select class="form-select" id="store-type-filter">
                                    <option value="all" selected>جميع الأنواع</option>
                                    <option value="A">نوع A</option>
                                    <option value="B">نوع B</option>
                                    <option value="D">نوع D</option>
                                    <option value="other">أخرى</option>
                                </select>
                            </div>
                            <div class="col-12 mt-3">
                                <div class="d-flex justify-content-between">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search me-1"></i> تطبيق التصفية
                                    </button>
                                    <div>
                                        <button type="button" class="btn btn-outline-secondary me-2" id="save-report-btn">
                                            <i class="fas fa-save me-1"></i> حفظ التقرير
                                        </button>
                                        <div class="btn-group">
                                            <button type="button" class="btn btn-outline-success dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                                <i class="fas fa-download me-1"></i> تصدير
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li><a class="dropdown-item" href="#" id="export-pdf"><i class="fas fa-file-pdf me-1"></i> PDF</a></li>
                                                <li><a class="dropdown-item" href="#" id="export-excel"><i class="fas fa-file-excel me-1"></i> Excel</a></li>
                                                <li><a class="dropdown-item" href="#" id="export-csv"><i class="fas fa-file-csv me-1"></i> CSV</a></li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- بطاقات الإحصائيات الرئيسية -->
        <div class="row mb-4">
            <div class="col-md-3 mb-3">
                <div class="stats-card">
                    <div class="stats-header d-flex justify-content-between align-items-center" style="background: linear-gradient(135deg, var(--loacker-red-dark), var(--loacker-red));">
                        <h5 class="m-0">إجمالي المتاجر</h5>
                        <i class="fas fa-store stats-icon"></i>
                    </div>
                    <div class="card-body text-center">
                        <h2 class="display-4 fw-bold" id="total-stores-count">0</h2>
                        <p class="text-muted">متجر مسجل في النظام</p>
                    </div>
                </div>
            </div>

            <div class="col-md-3 mb-3">
                <div class="stats-card">
                    <div class="stats-header green d-flex justify-content-between align-items-center">
                        <h5 class="m-0">المناطق</h5>
                        <i class="fas fa-map-marker-alt stats-icon"></i>
                    </div>
                    <div class="card-body text-center">
                        <h2 class="display-4 fw-bold" id="regions-count">0</h2>
                        <p class="text-muted">منطقة تحتوي على متاجر</p>
                    </div>
                </div>
            </div>

            <div class="col-md-3 mb-3">
                <div class="stats-card">
                    <div class="stats-header orange d-flex justify-content-between align-items-center">
                        <h5 class="m-0">متوسط المتاجر</h5>
                        <i class="fas fa-calculator stats-icon"></i>
                    </div>
                    <div class="card-body text-center">
                        <h2 class="display-4 fw-bold" id="avg-stores-per-region">0</h2>
                        <p class="text-muted">متجر لكل منطقة</p>
                    </div>
                </div>
            </div>

            <div class="col-md-3 mb-3">
                <div class="stats-card">
                    <div class="stats-header d-flex justify-content-between align-items-center" style="background: linear-gradient(135deg, #6a11cb, #2575fc);">
                        <h5 class="m-0">متوسط الأداء</h5>
                        <i class="fas fa-chart-line stats-icon"></i>
                    </div>
                    <div class="card-body text-center">
                        <h2 class="display-4 fw-bold" id="avg-performance-score">0</h2>
                        <p class="text-muted">درجة من 100</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- توزيع المتاجر حسب النوع -->
        <div class="row mb-4">
            <div class="col-md-6 mb-3">
                <div class="card h-100">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-tags me-2"></i>
                            توزيع المتاجر حسب النوع
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="storeTypeChart"></canvas>
                        </div>
                        <div class="d-flex justify-content-center mt-3">
                            <div class="me-4">
                                <i class="fas fa-circle type-A"></i>
                                <span class="ms-1">نوع A</span>
                            </div>
                            <div class="me-4">
                                <i class="fas fa-circle type-B"></i>
                                <span class="ms-1">نوع B</span>
                            </div>
                            <div>
                                <i class="fas fa-circle type-D"></i>
                                <span class="ms-1">نوع D</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-6 mb-3">
                <div class="card h-100">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-map me-2"></i>
                            توزيع المتاجر حسب المنطقة
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="storeRegionChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- تفاصيل المتاجر حسب المنطقة والنوع -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-table me-2"></i>
                            تفاصيل المتاجر حسب المنطقة والنوع
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive" style="max-height: 500px; overflow-y: auto; border: 1px solid #dee2e6; border-radius: 8px;">
                            <table class="table table-hover mb-0">
                                <thead class="table-light sticky-top">
                                    <tr>
                                        <th style="background-color: #2a2a2a; color: #ffffff; position: sticky; top: 0; z-index: 10;">المنطقة</th>
                                        <th style="background-color: #2a2a2a; color: #ffffff; position: sticky; top: 0; z-index: 10;">تفاصيل</th>
                                        <th style="background-color: #2a2a2a; color: #ffffff; position: sticky; top: 0; z-index: 10;">إجمالي المتاجر</th>
                                        <th style="background-color: #2a2a2a; color: #ffffff; position: sticky; top: 0; z-index: 10;">نوع A</th>
                                        <th style="background-color: #2a2a2a; color: #ffffff; position: sticky; top: 0; z-index: 10;">نوع B</th>
                                        <th style="background-color: #2a2a2a; color: #ffffff; position: sticky; top: 0; z-index: 10;">نوع D</th>
                                        <th style="background-color: #2a2a2a; color: #ffffff; position: sticky; top: 0; z-index: 10;">النسبة المئوية</th>
                                    </tr>
                                </thead>
                                <tbody id="region-stats-table">
                                    <!-- سيتم ملؤها بواسطة JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- تحليل المتاجر حسب وصف العنوان ونوع المتجر -->
        <div class="row mb-4">
            <div class="col-md-6 mb-3">
                <div class="card h-100">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-map-pin me-2"></i>
                            تحليل المتاجر حسب وصف العنوان
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive" style="max-height: 400px; overflow-y: auto;">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>وصف العنوان</th>
                                        <th>عدد المتاجر</th>
                                        <th>نوع A</th>
                                        <th>نوع B</th>
                                        <th>نوع D</th>
                                    </tr>
                                </thead>
                                <tbody id="address-stats-table">
                                    <!-- سيتم ملؤها بواسطة JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-6 mb-3">
                <div class="card h-100">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-tags me-2"></i>
                            تحليل المتاجر حسب النوع ووصف العنوان
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container mb-3">
                            <canvas id="storeTypeAddressChart"></canvas>
                        </div>
                        <div class="table-responsive" style="max-height: 200px; overflow-y: auto;">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>نوع المتجر</th>
                                        <th>عدد المتاجر</th>
                                        <th>أكثر وصف عنوان شائع</th>
                                    </tr>
                                </thead>
                                <tbody id="type-address-stats-table">
                                    <!-- سيتم ملؤها بواسطة JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- خريطة توزيع المتاجر -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-globe me-2"></i>
                            خريطة توزيع المتاجر
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="stats-map" style="height: 500px; border-radius: 10px;"></div>
                    </div>
                </div>
            </div>
        </div>





        <!-- خريطة حرارية للكثافة -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-fire me-2"></i>
                            خريطة حرارية لكثافة المتاجر
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <label class="form-label">نوع العرض</label>
                                    <div class="btn-group">
                                        <button type="button" class="btn btn-sm btn-outline-primary active" data-view="heatmap">خريطة حرارية</button>
                                        <button type="button" class="btn btn-sm btn-outline-primary" data-view="cluster">تجميع</button>
                                    </div>
                                </div>
                                <div>
                                    <label class="form-label">كثافة الخريطة</label>
                                    <div class="d-flex">
                                        <input type="range" class="form-range" id="heatmap-intensity" min="10" max="50" step="5" value="25" style="width: 150px;">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div id="heatmap" style="height: 500px; width: 100%; min-height: 400px; border-radius: 10px; display: block;"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- حفظ التقرير -->
        <div class="modal fade" id="saveReportModal" tabindex="-1" aria-labelledby="saveReportModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="saveReportModalLabel">حفظ التقرير</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <form id="save-report-form">
                            <div class="mb-3">
                                <label for="report-title" class="form-label">عنوان التقرير</label>
                                <input type="text" class="form-control" id="report-title" required>
                            </div>
                            <div class="mb-3">
                                <label for="report-description" class="form-label">وصف التقرير</label>
                                <textarea class="form-control" id="report-description" rows="3"></textarea>
                            </div>
                            <div class="mb-3">
                                <label for="report-format" class="form-label">تنسيق التقرير</label>
                                <select class="form-select" id="report-format">
                                    <option value="json" selected>JSON</option>
                                    <option value="csv">CSV</option>
                                </select>
                            </div>
                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="schedule-report">
                                <label class="form-check-label" for="schedule-report">جدولة التقرير</label>
                            </div>
                            <div class="mb-3 schedule-options d-none">
                                <label for="schedule-frequency" class="form-label">تكرار الجدولة</label>
                                <select class="form-select" id="schedule-frequency">
                                    <option value="daily">يومي</option>
                                    <option value="weekly" selected>أسبوعي</option>
                                    <option value="monthly">شهري</option>
                                </select>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="button" class="btn btn-primary" id="save-report-submit">حفظ التقرير</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

        <footer class="mt-5 pt-3 border-top text-center text-muted">
            <p>Loacker &copy; 2025</p>
        </footer>
    </div>

    <!-- Bootstrap JS Bundle -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js"></script>

    <!-- Leaflet JS -->
    <script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script>

    <!-- Leaflet Plugins - load after Leaflet -->
    <script src="https://unpkg.com/leaflet-rotatedmarker/leaflet.rotatedMarker.js"></script>

    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/libya-regions.js') }}"></script>
    <script src="{{ url_for('static', filename='js/region-details.js') }}"></script>
    <script src="{{ url_for('static', filename='js/createAddressTable.js') }}"></script>

    <script>
        // تعريف وظيفة مساعدة لتصحيح Canvas.getContext لاستخدام willReadFrequently
        function patchCanvasForWillReadFrequently() {
            console.log("Patching Canvas.prototype.getContext for willReadFrequently");

            // حفظ الوظيفة الأصلية
            const originalGetContext = HTMLCanvasElement.prototype.getContext;

            // استبدال الوظيفة بنسخة معدلة
            HTMLCanvasElement.prototype.getContext = function(contextType, contextAttributes) {
                // إضافة willReadFrequently=true لسياق 2d
                if (contextType === '2d') {
                    contextAttributes = contextAttributes || {};
                    contextAttributes.willReadFrequently = true;
                }

                // استدعاء الوظيفة الأصلية مع الخصائص المعدلة
                return originalGetContext.call(this, contextType, contextAttributes);
            };

            console.log("Canvas.prototype.getContext patched successfully");
        }

        // تعريف وظيفة مساعدة لتصحيح L.HeatLayer لتجنب أخطاء عرض الخريطة الحرارية
        function patchLeafletHeatLayer() {
            console.log("Patching Leaflet HeatLayer to prevent IndexSizeError");

            // انتظار تحميل مكتبة Leaflet
            const checkLeaflet = setInterval(() => {
                if (typeof L !== 'undefined' && L.HeatLayer) {
                    clearInterval(checkLeaflet);

                    // تصحيح وظيفة _draw في L.HeatLayer
                    const originalDraw = L.HeatLayer.prototype._draw;
                    if (originalDraw) {
                        L.HeatLayer.prototype._draw = function() {
                            try {
                                // التحقق من أبعاد الخريطة قبل الرسم
                                const canvas = this._canvas;
                                if (!canvas) return;

                                const size = this._map.getSize();
                                if (!size || size.x <= 0 || size.y <= 0) {
                                    console.warn("Map has invalid size, skipping heatmap draw");
                                    return;
                                }

                                // التحقق من أبعاد canvas
                                if (canvas.width <= 0 || canvas.height <= 0) {
                                    console.warn("Canvas has invalid dimensions, skipping heatmap draw");
                                    return;
                                }

                                // استدعاء الوظيفة الأصلية
                                originalDraw.call(this);
                            } catch (error) {
                                console.error("Error in HeatLayer._draw:", error);
                            }
                        };
                    }

                    // تصحيح وظيفة _reset في L.HeatLayer
                    const originalReset = L.HeatLayer.prototype._reset;
                    if (originalReset) {
                        L.HeatLayer.prototype._reset = function() {
                            try {
                                // التحقق من أبعاد الخريطة قبل إعادة الضبط
                                const size = this._map.getSize();
                                if (!size || size.x <= 0 || size.y <= 0) {
                                    console.warn("Map has invalid size, skipping heatmap reset");
                                    return;
                                }

                                // استدعاء الوظيفة الأصلية
                                originalReset.call(this);
                            } catch (error) {
                                console.error("Error in HeatLayer._reset:", error);
                            }
                        };
                    }

                    // تصحيح وظيفة _redraw في L.HeatLayer
                    const originalRedraw = L.HeatLayer.prototype._redraw;
                    if (originalRedraw) {
                        L.HeatLayer.prototype._redraw = function() {
                            try {
                                // التحقق من أبعاد الخريطة قبل إعادة الرسم
                                if (!this._map) return;

                                const size = this._map.getSize();
                                if (!size || size.x <= 0 || size.y <= 0) {
                                    console.warn("Map has invalid size, skipping heatmap redraw");
                                    return;
                                }

                                // التحقق من أبعاد canvas
                                const canvas = this._canvas;
                                if (!canvas || canvas.width <= 0 || canvas.height <= 0) {
                                    console.warn("Canvas has invalid dimensions, skipping heatmap redraw");
                                    return;
                                }

                                // استدعاء الوظيفة الأصلية
                                originalRedraw.call(this);
                            } catch (error) {
                                console.error("Error in HeatLayer._redraw:", error);
                            }
                        };
                    }

                    console.log("Leaflet HeatLayer patched successfully");
                }
            }, 100);
        }

        document.addEventListener('DOMContentLoaded', function() {
            // تطبيق تصحيح Canvas.getContext
            patchCanvasForWillReadFrequently();

            // تطبيق تصحيح Leaflet HeatLayer
            patchLeafletHeatLayer();

            // تهيئة الإحصائيات
            initStatistics();

            // تهيئة أحداث النموذج
            initFormEvents();

            // تأخير تهيئة خريطة الحرارة للتأكد من تحميل الصفحة بالكامل
            setTimeout(() => {
                // تهيئة خريطة الحرارة
                initHeatmap();
            }, 500);




        });

        async function initStatistics() {
            try {
                // جلب بيانات المتاجر
                console.log("Fetching stores data from API...");
                const response = await fetch('/api/stores');

                if (!response.ok) {
                    console.error(`فشل في جلب بيانات المتاجر: ${response.status} ${response.statusText}`);
                    throw new Error(`فشل في جلب بيانات المتاجر: ${response.status} ${response.statusText}`);
                }

                let storesData = await response.json();

                if (!storesData) {
                    console.error('فشل في جلب بيانات المتاجر: البيانات فارغة');
                    throw new Error('فشل في جلب بيانات المتاجر: البيانات فارغة');
                }

                // طباعة البيانات للتشخيص
                console.log("Stores data received:", storesData);
                console.log("Data type:", typeof storesData);
                console.log("Is array:", Array.isArray(storesData));
                console.log("Data length:", Array.isArray(storesData) ? storesData.length : 'N/A');

                // إذا لم تكن البيانات مصفوفة، تحقق مما إذا كانت كائن يحتوي على مصفوفة
                if (!Array.isArray(storesData) && typeof storesData === 'object') {
                    // تحقق من وجود مفتاح يحتوي على مصفوفة
                    for (const key in storesData) {
                        if (Array.isArray(storesData[key])) {
                            console.log(`Found array in key: ${key}`);
                            storesData = storesData[key];
                            break;
                        }
                    }
                }

                // إذا لم تكن البيانات مصفوفة بعد، استخدم بيانات تجريبية
                if (!Array.isArray(storesData)) {
                    console.warn("Data is not an array, using sample data instead");
                    storesData = [];
                }

                // التحقق من وجود حقل العنوان في البيانات
                if (storesData.length > 0) {
                    const sampleStore = storesData[0];
                    console.log("Sample store:", sampleStore);
                    console.log("Has address field:", sampleStore.hasOwnProperty('address'));
                    console.log("Address value:", sampleStore.address);

                    // إحصاء المتاجر التي تحتوي على حقل العنوان
                    const storesWithAddress = storesData.filter(store => store.address).length;
                    console.log(`Stores with address field: ${storesWithAddress} out of ${storesData.length}`);
                }

                window.storesData = storesData;

                // إضافة بيانات تجريبية إذا لم تكن هناك بيانات
                if (!window.storesData || window.storesData.length === 0) {
                    console.log("No store data found, adding sample data...");
                    window.storesData = [
                        { id: 1, name: "متجر 1", address: "شارع رئيسي", type: "A", latitude: 32.8872, longitude: 13.1913 },
                        { id: 2, name: "متجر 2", address: "مركز تجاري", type: "B", latitude: 32.8872, longitude: 13.1913 },
                        { id: 3, name: "متجر 3", address: "بجانب المسجد", type: "A", latitude: 32.8872, longitude: 13.1913 },
                        { id: 4, name: "متجر 4", address: "قرب المدرسة", type: "D", latitude: 32.8872, longitude: 13.1913 },
                        { id: 5, name: "متجر 5", address: "منطقة سكنية", type: "A", latitude: 32.8872, longitude: 13.1913 },
                        { id: 6, name: "متجر 6", address: "شارع فرعي", type: "B", latitude: 32.8872, longitude: 13.1913 },
                        { id: 7, name: "متجر 7", address: "مجمع تجاري", type: "D", latitude: 32.8872, longitude: 13.1913 },
                        { id: 8, name: "متجر 8", address: "سوق شعبي", type: "A", latitude: 32.8872, longitude: 13.1913 },
                        { id: 9, name: "متجر 9", address: "مول تجاري", type: "B", latitude: 32.8872, longitude: 13.1913 },
                        { id: 10, name: "متجر 10", address: "منطقة صناعية", type: "A", latitude: 32.8872, longitude: 13.1913 }
                    ];
                    console.log("Added sample data:", window.storesData.length, "stores");
                }

                // التأكد من أن جميع المتاجر تحتوي على حقل العنوان والنوع
                if (window.storesData && window.storesData.length > 0) {
                    // إضافة حقل العنوان والنوع إذا لم يكن موجوداً
                    window.storesData.forEach(store => {
                        if (!store.address) {
                            store.address = 'غير محدد';
                            console.log(`Added default address to store ${store.id || 'unknown'}`);
                        }
                        if (!store.type) {
                            store.type = 'A';
                            console.log(`Added default type to store ${store.id || 'unknown'}`);
                        }
                    });

                    // طباعة عينة من بيانات المتاجر بعد التعديل
                    console.log("Sample store data after modification:", window.storesData[0]);
                    console.log("Store keys:", Object.keys(window.storesData[0]));
                    console.log("Total stores:", window.storesData.length);
                    console.log("Stores with address:", window.storesData.filter(store => store.address).length);
                    console.log("Stores with type:", window.storesData.filter(store => store.type).length);
                }

                // تحديث العدادات الرئيسية
                updateMainCounters(window.storesData);

                // تحليل البيانات حسب النوع والمنطقة
                const storesByType = analyzeStoresByTypeNew(window.storesData);
                const storesByRegion = analyzeStoresByRegion(window.storesData);

                // تحليل البيانات حسب وصف العنوان ونوع المتجر
                const { addressMap, typeMap } = analyzeStoresByAddressAndType(window.storesData);
                window.addressAnalysis = { addressMap, typeMap };

                // طباعة بيانات تحليل العناوين للتشخيص
                console.log("Address Analysis:", window.addressAnalysis);
                console.log("Address Map Keys:", Object.keys(addressMap));
                console.log("Type Map Keys:", Object.keys(typeMap));

                // التحقق من وجود بيانات العناوين
                if (Object.keys(addressMap).length === 0) {
                    console.warn("No address data available after analysis. This should not happen.");
                } else {
                    console.log("Address data available. Creating address table...");
                }

                // إنشاء الرسوم البيانية
                createTypeChart(storesByType);
                createRegionChart(storesByRegion);
                createTypeAddressChart(window.addressAnalysis);

                // إنشاء تقرير مفصل للإحصائيات
                const detailedReport = generateDetailedReport(window.storesData);
                window.currentReport = detailedReport;

                // إنشاء جدول التفاصيل المحسن
                createDetailTableNew(detailedReport);
                createAddressTable(window.addressAnalysis.addressMap);
                createTypeAddressTable(window.addressAnalysis.typeMap);

                // إنشاء خريطة توزيع المتاجر
                createStoresMap(window.storesData);



                // حساب متوسط الأداء
                calculatePerformanceScore(window.storesData);

            } catch (error) {
                console.error('خطأ في تحميل الإحصائيات:', error);
                alert('حدث خطأ أثناء تحميل الإحصائيات. يرجى المحاولة مرة أخرى.');
            }
        }

        function initFormEvents() {
            // نموذج التصفية
            const filterForm = document.getElementById('stats-filter-form');
            filterForm.addEventListener('submit', function(e) {
                e.preventDefault();
                applyFilters();
            });



            // أزرار التصدير
            document.getElementById('export-pdf').addEventListener('click', function(e) {
                e.preventDefault();
                exportReport('pdf');
            });

            document.getElementById('export-excel').addEventListener('click', function(e) {
                e.preventDefault();
                exportReport('excel');
            });

            document.getElementById('export-csv').addEventListener('click', function(e) {
                e.preventDefault();
                exportReport('csv');
            });

            // زر حفظ التقرير
            const saveReportBtn = document.getElementById('save-report-btn');
            saveReportBtn.addEventListener('click', function() {
                const saveReportModal = new bootstrap.Modal(document.getElementById('saveReportModal'));
                saveReportModal.show();
            });

            // خيار جدولة التقرير
            const scheduleReportCheckbox = document.getElementById('schedule-report');
            scheduleReportCheckbox.addEventListener('change', function() {
                const scheduleOptions = document.querySelector('.schedule-options');
                if (this.checked) {
                    scheduleOptions.classList.remove('d-none');
                } else {
                    scheduleOptions.classList.add('d-none');
                }
            });

            // زر حفظ التقرير في النموذج
            const saveReportSubmit = document.getElementById('save-report-submit');
            saveReportSubmit.addEventListener('click', function() {
                saveReport();
            });



            // أزرار نوع عرض الخريطة الحرارية
            const heatmapViewButtons = document.querySelectorAll('[data-view]');
            heatmapViewButtons.forEach(button => {
                button.addEventListener('click', function() {
                    heatmapViewButtons.forEach(btn => btn.classList.remove('active'));
                    this.classList.add('active');
                    updateHeatmapView(this.dataset.view);
                });
            });

            // شريط تمرير كثافة الخريطة الحرارية
            const heatmapIntensity = document.getElementById('heatmap-intensity');
            heatmapIntensity.addEventListener('input', function() {
                updateHeatmapIntensity(parseInt(this.value));
            });
        }



        function updateMainCounters(stores) {
            // تحديث إجمالي عدد المتاجر
            document.getElementById('total-stores-count').textContent = stores.length;

            // حساب عدد المناطق الفريدة باستخدام الدالة الذكية
            const regions = [...new Set(stores.map(store => LibyaRegions.getSmartRegion(store.latitude, store.longitude, store.address)))];
            document.getElementById('regions-count').textContent = regions.length;

            // حساب متوسط المتاجر لكل منطقة
            const avgStores = regions.length > 0 ? (stores.length / regions.length).toFixed(1) : 0;
            document.getElementById('avg-stores-per-region').textContent = avgStores;
        }

        function calculatePerformanceScore(stores) {
            // حساب متوسط درجة الأداء
            let totalScore = 0;
            let storesWithScore = 0;

            stores.forEach(store => {
                if (store.performance_score !== undefined && store.performance_score !== null) {
                    totalScore += parseFloat(store.performance_score);
                    storesWithScore++;
                }
            });

            const avgScore = storesWithScore > 0 ? (totalScore / storesWithScore).toFixed(1) : 0;
            document.getElementById('avg-performance-score').textContent = avgScore;
        }



        function createTypeChart(storesByType) {
            // إنشاء الرسم البياني لتوزيع المتاجر حسب النوع
            const ctx = document.getElementById('storeTypeChart').getContext('2d');

            // التحقق من وجود بيانات
            if (!storesByType || !storesByType.labels || storesByType.labels.length === 0) {
                console.warn("No data available for store type chart");
                return;
            }

            // إنشاء الرسم البياني
            new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: storesByType.labels,
                    datasets: [{
                        data: storesByType.data,
                        backgroundColor: storesByType.colors,
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                color: '#e0e0e0',
                                font: {
                                    size: 12
                                }
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.label || '';
                                    const value = context.raw || 0;
                                    const percentage = storesByType.percentages[context.dataIndex];
                                    return `${label}: ${value} (${percentage}%)`;
                                }
                            }
                        }
                    }
                }
            });
        }



        function analyzeStoresByTypeNew(stores) {
            // تحليل المتاجر حسب النوع
            const typeCount = {};
            const typeColors = {
                'A': '#4CAF50',
                'B': '#2196F3',
                'D': '#FF9800',
                'other': '#9E9E9E'
            };

            // حساب عدد المتاجر لكل نوع
            stores.forEach(store => {
                const type = store.type ? store.type.toUpperCase() : 'other';
                typeCount[type] = (typeCount[type] || 0) + 1;
            });

            // تحويل البيانات إلى تنسيق مناسب للرسم البياني
            const labels = [];
            const data = [];
            const colors = [];
            const percentages = [];
            const total = stores.length;

            Object.entries(typeCount).forEach(([type, count]) => {
                const percentage = ((count / total) * 100).toFixed(1);
                labels.push(`نوع ${type}`);
                data.push(count);
                colors.push(typeColors[type] || typeColors.other);
                percentages.push(percentage);
            });

            return {
                labels,
                data,
                colors,
                percentages,
                total
            };
        }

        function analyzeStoresByAddressAndType(stores) {
            // تحليل المتاجر حسب العنوان والنوع
            const addressMap = {};
            const typeMap = {};

            // معالجة كل متجر
            stores.forEach(store => {
                if (!store.address) {
                    return;
                }

                // استخراج المنطقة من العنوان (الكلمة الأولى أو الكلمتين الأولى والثانية)
                let addressParts = store.address.split(' ');
                let addressKey = '';

                if (addressParts.length >= 2) {
                    // استخدام الكلمتين الأولى والثانية كمفتاح للمنطقة
                    addressKey = addressParts.slice(0, 2).join(' ');
                } else if (addressParts.length === 1) {
                    // استخدام الكلمة الأولى فقط
                    addressKey = addressParts[0];
                } else {
                    // إذا كان العنوان فارغاً، استخدم "غير محدد"
                    addressKey = 'غير محدد';
                }

                // تنظيف المفتاح
                addressKey = addressKey.trim();
                if (!addressKey) {
                    addressKey = 'غير محدد';
                }

                // إضافة المتجر إلى خريطة العناوين
                if (!addressMap[addressKey]) {
                    addressMap[addressKey] = {
                        total: 0,
                        typeA: 0,
                        typeB: 0,
                        typeD: 0,
                        other: 0
                    };
                }

                addressMap[addressKey].total++;

                // تحديث عدد المتاجر حسب النوع
                const storeType = store.type ? store.type.toUpperCase() : '';
                if (storeType === 'A') {
                    addressMap[addressKey].typeA++;
                } else if (storeType === 'B') {
                    addressMap[addressKey].typeB++;
                } else if (storeType === 'D') {
                    addressMap[addressKey].typeD++;
                } else {
                    addressMap[addressKey].other++;
                }

                // إضافة المتجر إلى خريطة الأنواع
                if (!store.type) {
                    return;
                }

                const typeKey = store.type.toUpperCase();
                if (!typeMap[typeKey]) {
                    typeMap[typeKey] = {
                        total: 0,
                        addresses: {}
                    };
                }

                typeMap[typeKey].total++;

                // إضافة العنوان إلى خريطة الأنواع
                if (!typeMap[typeKey].addresses[addressKey]) {
                    typeMap[typeKey].addresses[addressKey] = 0;
                }

                typeMap[typeKey].addresses[addressKey]++;
            });

            return { addressMap, typeMap };
        }

        function createTypeAddressChart(addressAnalysis) {
            // إنشاء الرسم البياني لتوزيع المتاجر حسب النوع والعنوان
            const ctx = document.getElementById('typeAddressChart');

            if (!ctx) {
                console.warn("Type-Address chart element not found");
                return;
            }

            // التحقق من وجود بيانات
            if (!addressAnalysis || !addressAnalysis.addressMap) {
                console.warn("No data available for type-address chart");
                return;
            }

            const addressMap = addressAnalysis.addressMap;

            // تحويل البيانات إلى تنسيق مناسب للرسم البياني
            const labels = [];
            const typeAData = [];
            const typeBData = [];
            const typeDData = [];
            const otherData = [];

            // الحصول على أعلى 10 مناطق من حيث عدد المتاجر
            const sortedAddresses = Object.entries(addressMap)
                .map(([address, data]) => ({ address, total: data.total }))
                .sort((a, b) => b.total - a.total)
                .slice(0, 10);

            // إضافة البيانات للرسم البياني
            sortedAddresses.forEach(item => {
                const address = item.address;
                labels.push(address);
                typeAData.push(addressMap[address].typeA);
                typeBData.push(addressMap[address].typeB);
                typeDData.push(addressMap[address].typeD);
                otherData.push(addressMap[address].other);
            });

            // إنشاء الرسم البياني
            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: labels,
                    datasets: [
                        {
                            label: 'نوع A',
                            data: typeAData,
                            backgroundColor: '#4CAF50',
                            borderWidth: 1
                        },
                        {
                            label: 'نوع B',
                            data: typeBData,
                            backgroundColor: '#2196F3',
                            borderWidth: 1
                        },
                        {
                            label: 'نوع D',
                            data: typeDData,
                            backgroundColor: '#FF9800',
                            borderWidth: 1
                        },
                        {
                            label: 'أخرى',
                            data: otherData,
                            backgroundColor: '#9E9E9E',
                            borderWidth: 1
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                            labels: {
                                color: '#e0e0e0',
                                font: {
                                    size: 12
                                }
                            }
                        }
                    },
                    scales: {
                        x: {
                            ticks: {
                                color: '#e0e0e0'
                            },
                            grid: {
                                color: 'rgba(255, 255, 255, 0.1)'
                            }
                        },
                        y: {
                            ticks: {
                                color: '#e0e0e0'
                            },
                            grid: {
                                color: 'rgba(255, 255, 255, 0.1)'
                            }
                        }
                    }
                }
            });
        }

        function createAddressTable(addressMap) {
            // إنشاء جدول توزيع المتاجر حسب العنوان
            const tableBody = document.getElementById('address-table-body');

            if (!tableBody) {
                console.warn("Address table body element not found");
                return;
            }

            // التحقق من وجود بيانات
            if (!addressMap || Object.keys(addressMap).length === 0) {
                console.warn("No data available for address table");
                tableBody.innerHTML = '<tr><td colspan="6" class="text-center">لا توجد بيانات متاحة</td></tr>';
                return;
            }

            // تحويل البيانات إلى مصفوفة وترتيبها تنازلياً حسب عدد المتاجر
            const sortedAddresses = Object.entries(addressMap)
                .map(([address, data]) => ({ address, ...data }))
                .sort((a, b) => b.total - a.total);

            // حساب إجمالي عدد المتاجر
            const totalStores = sortedAddresses.reduce((sum, item) => sum + item.total, 0);

            // إنشاء صفوف الجدول
            let tableHTML = '';

            sortedAddresses.forEach(item => {
                const percentage = ((item.total / totalStores) * 100).toFixed(1);

                tableHTML += `
                    <tr>
                        <td>${item.address}</td>
                        <td>${item.total}</td>
                        <td><span class="badge bg-success">${item.typeA}</span></td>
                        <td><span class="badge bg-primary">${item.typeB}</span></td>
                        <td><span class="badge bg-warning text-dark">${item.typeD}</span></td>
                        <td>
                            <div class="progress" style="height: 20px;">
                                <div class="progress-bar" role="progressbar" style="width: ${percentage}%; background: linear-gradient(135deg, var(--loacker-red-dark), var(--loacker-red));"
                                    aria-valuenow="${percentage}" aria-valuemin="0" aria-valuemax="100">
                                    ${percentage}%
                                </div>
                            </div>
                        </td>
                    </tr>
                `;
            });

            // إضافة صف الإجمالي
            tableHTML += `
                <tr class="table-dark">
                    <td><strong>الإجمالي</strong></td>
                    <td><strong>${totalStores}</strong></td>
                    <td><strong>${sortedAddresses.reduce((sum, item) => sum + item.typeA, 0)}</strong></td>
                    <td><strong>${sortedAddresses.reduce((sum, item) => sum + item.typeB, 0)}</strong></td>
                    <td><strong>${sortedAddresses.reduce((sum, item) => sum + item.typeD, 0)}</strong></td>
                    <td>
                        <div class="progress" style="height: 20px;">
                            <div class="progress-bar" role="progressbar" style="width: 100%; background: linear-gradient(135deg, var(--loacker-red-dark), var(--loacker-red));"
                                aria-valuenow="100" aria-valuemin="0" aria-valuemax="100">
                                100%
                            </div>
                        </div>
                    </td>
                </tr>
            `;

            // تحديث الجدول
            tableBody.innerHTML = tableHTML;
        }

        function createTypeAddressTable(typeMap) {
            // إنشاء جدول توزيع المتاجر حسب النوع والعنوان
            const tableBody = document.getElementById('type-address-table-body');

            if (!tableBody) {
                console.warn("Type-Address table body element not found");
                return;
            }

            // التحقق من وجود بيانات
            if (!typeMap || Object.keys(typeMap).length === 0) {
                console.warn("No data available for type-address table");
                tableBody.innerHTML = '<tr><td colspan="3" class="text-center">لا توجد بيانات متاحة</td></tr>';
                return;
            }

            // تحويل البيانات إلى مصفوفة وترتيبها حسب النوع
            const sortedTypes = Object.entries(typeMap)
                .map(([type, data]) => ({ type, ...data }))
                .sort((a, b) => {
                    // ترتيب الأنواع: A، B، D، ثم الأخرى
                    const typeOrder = { 'A': 1, 'B': 2, 'D': 3 };
                    return (typeOrder[a.type] || 999) - (typeOrder[b.type] || 999);
                });

            // إنشاء صفوف الجدول
            let tableHTML = '';

            sortedTypes.forEach(typeData => {
                // الحصول على أعلى 5 مناطق لهذا النوع
                const topAddresses = Object.entries(typeData.addresses)
                    .map(([address, count]) => ({ address, count }))
                    .sort((a, b) => b.count - a.count)
                    .slice(0, 5);

                // إنشاء قائمة المناطق
                const addressList = topAddresses.map(item =>
                    `<div class="d-flex justify-content-between align-items-center mb-1">
                        <span>${item.address}</span>
                        <span class="badge bg-secondary">${item.count}</span>
                    </div>`
                ).join('');

                // تحديد لون الشارة حسب النوع
                let badgeClass = 'bg-secondary';
                if (typeData.type === 'A') badgeClass = 'bg-success';
                else if (typeData.type === 'B') badgeClass = 'bg-primary';
                else if (typeData.type === 'D') badgeClass = 'bg-warning text-dark';

                tableHTML += `
                    <tr>
                        <td>
                            <span class="badge ${badgeClass} me-2">نوع ${typeData.type}</span>
                        </td>
                        <td>${typeData.total}</td>
                        <td>
                            <div class="address-list">
                                ${addressList || '<span class="text-muted">لا توجد بيانات</span>'}
                            </div>
                        </td>
                    </tr>
                `;
            });

            // تحديث الجدول
            tableBody.innerHTML = tableHTML;
        }

        function createDetailTable(storesByRegion, storesByType) {
            // إنشاء جدول تفاصيل المتاجر حسب المنطقة والنوع
            const tableBody = document.getElementById('detail-table-body');

            if (!tableBody) {
                console.warn("Detail table body element not found");
                return;
            }

            // التحقق من وجود بيانات
            if (!storesByRegion || !storesByRegion.labels || storesByRegion.labels.length === 0) {
                console.warn("No data available for detail table");
                tableBody.innerHTML = '<tr><td colspan="7" class="text-center">لا توجد بيانات متاحة</td></tr>';
                return;
            }

            // حساب إجمالي عدد المتاجر
            const totalStores = storesByRegion.data.reduce((sum, count) => sum + count, 0);

            // حساب عدد المتاجر حسب النوع
            const storesByTypeObj = {};
            storesByType.labels.forEach((label, index) => {
                storesByTypeObj[label] = storesByType.data[index];
            });

            // إنشاء كائن يحتوي على تفاصيل المناطق
            const detailedRegions = {};

            // إنشاء صفوف الجدول
            tableBody.innerHTML = '';

            // إنشاء صف للإجمالي
            const totalRow = document.createElement('tr');
            totalRow.innerHTML = `
                <td>
                    <span class="fw-bold">الإجمالي</span>
                </td>
                <td class="text-center">
                    <button class="btn btn-sm btn-outline-info details-btn" data-region="الإجمالي">
                        <i class="fas fa-exclamation-circle"></i>
                    </button>
                </td>
                <td>${totalStores}</td>
                <td><span class="badge bg-success">${storesByTypeObj['نوع A'] || 0}</span></td>
                <td><span class="badge bg-primary">${storesByTypeObj['نوع B'] || 0}</span></td>
                <td><span class="badge bg-warning text-dark">${storesByTypeObj['نوع D'] || 0}</span></td>
                <td>
                    <div class="progress" style="height: 20px;">
                        <div class="progress-bar" role="progressbar" style="width: 100%; background: linear-gradient(135deg, var(--loacker-red-dark), var(--loacker-red));"
                            aria-valuenow="100" aria-valuemin="0" aria-valuemax="100">
                            100%
                        </div>
                    </div>
                </td>
            `;
            tableBody.appendChild(totalRow);

            // إضافة صفوف للمناطق
            storesByRegion.labels.forEach((region, index) => {
                const count = storesByRegion.data[index];
                const percentage = storesByRegion.percentages[index];

                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>
                        <span class="fw-bold">${region}</span>
                    </td>
                    <td class="text-center">
                        <button class="btn btn-sm btn-outline-info details-btn" data-region="${region}">
                            <i class="fas fa-exclamation-circle"></i>
                        </button>
                    </td>
                    <td>${count}</td>
                    <td><span class="badge bg-success">0</span></td>
                    <td><span class="badge bg-primary">0</span></td>
                    <td><span class="badge bg-warning text-dark">0</span></td>
                    <td>
                        <div class="progress" style="height: 20px;">
                            <div class="progress-bar" role="progressbar" style="width: ${percentage}%; background: linear-gradient(135deg, var(--loacker-red-dark), var(--loacker-red));"
                                aria-valuenow="${percentage}" aria-valuemin="0" aria-valuemax="100">
                                ${percentage}%
                            </div>
                        </div>
                    </td>
                `;
                tableBody.appendChild(row);
            });

            // إضافة مستمعي الأحداث لأزرار التفاصيل
            document.querySelectorAll('.details-btn').forEach(button => {
                button.addEventListener('click', function() {
                    const region = this.getAttribute('data-region');
                    showRegionDetails(region, window.storesData);
                });
            });
        }

        function updateMobileCounters(stores) {
            // تحديث عدادات واجهة الهاتف المحمول
            document.getElementById('mobile-total-stores-count').textContent = stores.length;

            // حساب عدد المناطق الفريدة باستخدام الدالة الذكية
            const regions = [...new Set(stores.map(store => LibyaRegions.getSmartRegion(store.latitude, store.longitude, store.address)))];
            document.getElementById('mobile-regions-count').textContent = regions.length;

            // حساب متوسط المتاجر لكل منطقة
            const avgStores = regions.length > 0 ? (stores.length / regions.length).toFixed(1) : 0;
            document.getElementById('mobile-avg-stores-per-region').textContent = avgStores;
        }

        function createMobileCharts(storesByType, storesByRegion) {
            // إنشاء الرسوم البيانية لواجهة الهاتف المحمول

            // إنشاء رسم بياني لتوزيع المتاجر حسب النوع
            const typeCtx = document.getElementById('mobileStoreTypeChart');
            if (typeCtx && storesByType && storesByType.labels && storesByType.labels.length > 0) {
                new Chart(typeCtx, {
                    type: 'doughnut',
                    data: {
                        labels: storesByType.labels,
                        datasets: [{
                            data: storesByType.data,
                            backgroundColor: storesByType.colors,
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom',
                                labels: {
                                    color: '#e0e0e0',
                                    font: {
                                        size: 12
                                    }
                                }
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        const label = context.label || '';
                                        const value = context.raw || 0;
                                        const percentage = storesByType.percentages[context.dataIndex];
                                        return `${label}: ${value} (${percentage}%)`;
                                    }
                                }
                            }
                        }
                    }
                });
            }

            // إنشاء رسم بياني لتوزيع المتاجر حسب المنطقة
            const regionCtx = document.getElementById('mobileStoreRegionChart');
            if (regionCtx && storesByRegion && storesByRegion.labels && storesByRegion.labels.length > 0) {
                // الحد من عدد المناطق المعروضة في الرسم البياني للجوال (أخذ أعلى 5 مناطق)
                const labels = storesByRegion.labels.slice(0, 5);
                const data = storesByRegion.data.slice(0, 5);
                const colors = storesByRegion.colors.slice(0, 5);
                const percentages = storesByRegion.percentages.slice(0, 5);

                new Chart(regionCtx, {
                    type: 'bar',
                    data: {
                        labels: labels,
                        datasets: [{
                            label: 'عدد المتاجر',
                            data: data,
                            backgroundColor: colors,
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        indexAxis: 'y',
                        plugins: {
                            legend: {
                                display: false
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        const label = context.dataset.label || '';
                                        const value = context.raw || 0;
                                        const percentage = percentages[context.dataIndex];
                                        return `${label}: ${value} (${percentage}%)`;
                                    }
                                }
                            }
                        },
                        scales: {
                            x: {
                                ticks: {
                                    color: '#e0e0e0'
                                },
                                grid: {
                                    color: 'rgba(255, 255, 255, 0.1)'
                                }
                            },
                            y: {
                                ticks: {
                                    color: '#e0e0e0'
                                },
                                grid: {
                                    color: 'rgba(255, 255, 255, 0.1)'
                                }
                            }
                        }
                    }
                });
            }
        }

        function createMobileMap(stores) {
            // إنشاء خريطة للهاتف المحمول
            const mapElement = document.getElementById('mobile-map');
            if (!mapElement) {
                console.warn("Mobile map element not found");
                return;
            }

            // التأكد من أن عنصر الخريطة مرئي وله أبعاد
            if (mapElement.offsetWidth === 0 || mapElement.offsetHeight === 0) {
                mapElement.style.width = '100%';
                mapElement.style.height = '300px';
                mapElement.style.display = 'block';
            }

            // إنشاء خريطة
            const map = L.map('mobile-map', {
                preferCanvas: true,
                zoomControl: true,
                attributionControl: true,
                dragging: true,
                scrollWheelZoom: true,
                doubleClickZoom: true,
                touchZoom: true
            }).setView([32.8872, 13.1913], 6); // مركز ليبيا (طرابلس)

            // إضافة طبقة الخريطة
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
            }).addTo(map);

            // إضافة المتاجر إلى الخريطة
            stores.forEach(store => {
                if (store.latitude && store.longitude) {
                    const marker = L.marker([store.latitude, store.longitude], {
                        icon: L.divIcon({
                            className: 'store-marker',
                            html: `<i class="fas fa-map-marker-alt map-marker-alt"></i>`,
                            iconSize: [24, 24],
                            iconAnchor: [12, 24],
                            popupAnchor: [0, -24]
                        })
                    }).addTo(map);

                    const region = LibyaRegions.getRegionFromCoordinates(store.latitude, store.longitude);
                    marker.bindPopup(`
                        <div class="store-popup">
                            <h6>${store.name}</h6>
                            <p class="text-muted small mb-1">${region}</p>
                            <p class="mb-1">${store.phone || 'لا يوجد رقم هاتف'}</p>
                            <button class="btn btn-sm btn-primary" onclick="window.location.href='/?store=${store.id}'" style="background: linear-gradient(135deg, var(--loacker-red-dark), var(--loacker-red)); border: none;">
                                <i class="fas fa-eye"></i> عرض التفاصيل
                            </button>
                        </div>
                    `);
                }
            });

            // إعادة ضبط حجم الخريطة بعد إنشائها
            setTimeout(() => {
                map.invalidateSize();
            }, 300);
        }



        function applyFilters() {
            // تطبيق التصفية على البيانات
            const region = document.getElementById('region-filter').value;
            const storeType = document.getElementById('store-type-filter').value;

            // بناء كائن التصفية
            const filters = {};

            if (region !== 'all') {
                filters.region = region;

                // إضافة معلومات إضافية للتصفية حسب المنطقة
                const regionInfo = document.createElement('div');
                regionInfo.className = 'alert alert-info mt-2';
                regionInfo.innerHTML = `<i class="fas fa-info-circle me-2"></i> تصفية حسب المنطقة: <strong>${region}</strong>`;

                // إضافة زر لعرض المنطقة على الخريطة
                const showOnMapBtn = document.createElement('button');
                showOnMapBtn.className = 'btn btn-sm btn-outline-primary ms-2';
                showOnMapBtn.innerHTML = '<i class="fas fa-map-marker-alt"></i> عرض على الخريطة';
                showOnMapBtn.onclick = function() {
                    // الحصول على إحداثيات المنطقة
                    const coordinates = LibyaRegions.getRegionCoordinates(region);
                    if (coordinates) {
                        // تحريك الخريطة إلى المنطقة
                        heatmapMap.setView([coordinates.lat, coordinates.lng], 10);
                        // التمرير إلى قسم الخريطة الحرارية
                        document.getElementById('heatmap').scrollIntoView({ behavior: 'smooth' });
                    }
                };

                regionInfo.appendChild(showOnMapBtn);

                // إضافة العنصر إلى النموذج
                const filterForm = document.getElementById('stats-filter-form');
                // إزالة أي تنبيهات سابقة
                const previousAlerts = filterForm.querySelectorAll('.alert');
                previousAlerts.forEach(alert => alert.remove());

                filterForm.appendChild(regionInfo);
            }

            if (storeType !== 'all') {
                filters.store_type = storeType;
            }

            // استدعاء API للحصول على البيانات المصفاة
            fetchFilteredData(filters);
        }

        async function fetchFilteredData(filters) {
            try {
                // بناء سلسلة الاستعلام
                const queryParams = new URLSearchParams();

                for (const [key, value] of Object.entries(filters)) {
                    queryParams.append(key, value);
                }

                // استدعاء API
                const response = await fetch(`/api/statistics?${queryParams.toString()}`);
                const data = await response.json();

                // تحديث واجهة المستخدم بالبيانات المصفاة
                updateUIWithFilteredData(data);

            } catch (error) {
                console.error('خطأ في تطبيق التصفية:', error);
                alert('حدث خطأ أثناء تطبيق التصفية. يرجى المحاولة مرة أخرى.');
            }
        }

        function updateUIWithFilteredData(statistics) {
            // تحديث واجهة المستخدم بالبيانات المصفاة
            // هذه الدالة ستقوم بتحديث جميع العناصر المرئية بناءً على البيانات المصفاة

            if (!statistics || !Array.isArray(statistics)) {
                console.error("Invalid statistics data:", statistics);
                return;
            }

            // تحديث العدادات
            document.getElementById('total-stores-count').textContent = statistics.length;

            // حساب المناطق الفريدة
            const regions = [...new Set(statistics.filter(stat => stat.region).map(stat => stat.region))];
            document.getElementById('regions-count').textContent = regions.length;

            // حساب متوسط المتاجر لكل منطقة
            const avgStores = regions.length > 0 ? (statistics.length / regions.length).toFixed(1) : 0;
            document.getElementById('avg-stores-per-region').textContent = avgStores;

            // حساب متوسط الأداء
            let totalScore = 0;
            let storesWithScore = 0;
            statistics.forEach(stat => {
                if (stat.performance_score) {
                    totalScore += parseFloat(stat.performance_score || 0);
                    storesWithScore++;
                }
            });
            const avgScore = storesWithScore > 0 ? (totalScore / storesWithScore).toFixed(1) : 0;
            document.getElementById('avg-performance-score').textContent = avgScore;

            // تحديث الرسوم البيانية والجداول الأخرى
            // ...
        }

        function saveReport() {
            // حفظ التقرير
            const title = document.getElementById('report-title').value;
            const description = document.getElementById('report-description').value;
            const format = document.getElementById('report-format').value;
            const scheduled = document.getElementById('schedule-report').checked;
            const scheduleFrequency = document.getElementById('schedule-frequency').value;

            // الحصول على التصفية الحالية
            const region = document.getElementById('region-filter').value;
            const storeType = document.getElementById('store-type-filter').value;

            // بناء كائن التصفية
            const filters = {};

            if (region !== 'all') {
                filters.region = region;
            }

            if (storeType !== 'all') {
                filters.store_type = storeType;
            }

            // بناء كائن البيانات
            const data = {
                title,
                description,
                filters,
                format,
                scheduled,
                schedule_frequency: scheduled ? scheduleFrequency : null
            };

            // استدعاء API لحفظ التقرير
            saveReportToServer(data);
        }

        async function saveReportToServer(data) {
            try {
                const response = await fetch('/api/statistics/save-report', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                });

                const result = await response.json();

                if (result.error) {
                    throw new Error(result.error || 'فشل في حفظ التقرير');
                }

                // إغلاق النموذج
                const saveReportModal = bootstrap.Modal.getInstance(document.getElementById('saveReportModal'));
                saveReportModal.hide();

                // عرض رسالة نجاح
                alert('تم حفظ التقرير بنجاح');

            } catch (error) {
                console.error('خطأ في حفظ التقرير:', error);
                alert('حدث خطأ أثناء حفظ التقرير. يرجى المحاولة مرة أخرى.');
            }
        }

        function exportReport(format) {
            // تصدير التقرير
            const region = document.getElementById('region-filter').value;
            const storeType = document.getElementById('store-type-filter').value;

            // بناء كائن التصفية
            const filters = {};

            if (region !== 'all') {
                filters.region = region;
            }

            if (storeType !== 'all') {
                filters.store_type = storeType;
            }

            // بناء سلسلة الاستعلام
            const queryParams = new URLSearchParams();

            for (const [key, value] of Object.entries(filters)) {
                queryParams.append(key, value);
            }

            queryParams.append('format', format);

            // فتح نافذة جديدة للتصدير
            window.open(`/api/statistics/report?${queryParams.toString()}`, '_blank');
        }

        // متغيرات عالمية للخريطة الحرارية
        let heatmapLayer;
        let markerClusterGroup;
        let heatmapMap;
        let heatmapStores = [];
        let currentHeatmapView = 'heatmap';
        let currentHeatmapIntensity = 25;

        function initHeatmap() {
            try {
                console.log("Initializing heatmap...");

                // التحقق من وجود عنصر الخريطة
                const heatmapElement = document.getElementById('heatmap');
                if (!heatmapElement) {
                    console.error("Heatmap element not found");
                    return;
                }

                // التأكد من أن عنصر الخريطة مرئي وله أبعاد
                if (heatmapElement.offsetWidth === 0 || heatmapElement.offsetHeight === 0) {
                    console.warn("Heatmap container has zero width or height. Setting minimum dimensions.");
                    heatmapElement.style.width = '100%';
                    heatmapElement.style.height = '500px';
                    heatmapElement.style.minHeight = '400px';
                    heatmapElement.style.display = 'block';

                    // إعادة المحاولة بعد تعيين الأبعاد
                    setTimeout(() => {
                        console.log("Retrying heatmap initialization after setting dimensions");
                        initHeatmap();
                    }, 200);
                    return;
                }

                // التحقق من تحميل مكتبة Leaflet
                if (typeof L === 'undefined') {
                    console.error("Leaflet library is not loaded");

                    // محاولة تحميل مكتبة Leaflet
                    const leafletCSS = document.createElement('link');
                    leafletCSS.rel = 'stylesheet';
                    leafletCSS.href = 'https://unpkg.com/leaflet@1.7.1/dist/leaflet.css';
                    document.head.appendChild(leafletCSS);

                    const leafletScript = document.createElement('script');
                    leafletScript.src = 'https://unpkg.com/leaflet@1.7.1/dist/leaflet.js';
                    leafletScript.onload = function() {
                        console.log("Leaflet library loaded successfully");
                        initHeatmapAfterLibraryLoad();
                    };
                    document.head.appendChild(leafletScript);
                    return;
                }

                // تحميل مكتبة leaflet-heat.js إذا لم تكن موجودة
                if (typeof L.heatLayer === 'undefined') {
                    console.log("Loading leaflet-heat.js library...");
                    const script = document.createElement('script');
                    script.src = 'https://unpkg.com/leaflet.heat@0.2.0/dist/leaflet-heat.js';
                    script.onload = function() {
                        console.log("leaflet-heat.js loaded successfully");
                        initHeatmapAfterLibraryLoad();
                    };
                    script.onerror = function() {
                        console.error("Failed to load leaflet-heat.js");
                    };
                    document.head.appendChild(script);
                    return;
                }

                // تحميل مكتبة leaflet.markercluster إذا لم تكن موجودة
                if (typeof L.markerClusterGroup === 'undefined') {
                    console.log("Loading leaflet.markercluster library...");

                    // تحميل CSS
                    const markerClusterCSS = document.createElement('link');
                    markerClusterCSS.rel = 'stylesheet';
                    markerClusterCSS.href = 'https://unpkg.com/leaflet.markercluster@1.4.1/dist/MarkerCluster.css';
                    document.head.appendChild(markerClusterCSS);

                    const markerClusterDefaultCSS = document.createElement('link');
                    markerClusterDefaultCSS.rel = 'stylesheet';
                    markerClusterDefaultCSS.href = 'https://unpkg.com/leaflet.markercluster@1.4.1/dist/MarkerCluster.Default.css';
                    document.head.appendChild(markerClusterDefaultCSS);

                    // تحميل JavaScript
                    const script = document.createElement('script');
                    script.src = 'https://unpkg.com/leaflet.markercluster@1.4.1/dist/leaflet.markercluster.js';
                    script.onload = function() {
                        console.log("leaflet.markercluster loaded successfully");
                        initHeatmapAfterLibraryLoad();
                    };
                    script.onerror = function() {
                        console.error("Failed to load leaflet.markercluster");
                    };
                    document.head.appendChild(script);
                    return;
                }

                // التحقق من أن عنصر الخريطة لا يزال مرئيًا وله أبعاد
                if (heatmapElement.offsetWidth === 0 || heatmapElement.offsetHeight === 0) {
                    console.warn("Heatmap container still has zero width or height after loading libraries. Retrying...");

                    // إعادة المحاولة بعد فترة
                    setTimeout(() => {
                        console.log("Retrying heatmap initialization");
                        initHeatmap();
                    }, 500);
                    return;
                }

                // طباعة أبعاد حاوية الخريطة للتشخيص
                console.log(`Heatmap container dimensions: ${heatmapElement.offsetWidth}x${heatmapElement.offsetHeight}`);

                // إذا تم تحميل جميع المكتبات، قم بتهيئة الخريطة
                initHeatmapAfterLibraryLoad();
            } catch (error) {
                console.error("Error initializing heatmap:", error);
            }
        }

        function initHeatmapAfterLibraryLoad() {
            try {
                console.log("Initializing heatmap after library load...");

                // التحقق من وجود عنصر الخريطة مرة أخرى
                const heatmapElement = document.getElementById('heatmap');
                if (!heatmapElement) {
                    console.error("Heatmap element not found");
                    return;
                }

                // التأكد من أن عنصر الخريطة مرئي وله أبعاد
                if (heatmapElement.offsetWidth === 0 || heatmapElement.offsetHeight === 0) {
                    console.warn("Heatmap container has zero width or height in initHeatmapAfterLibraryLoad. Setting minimum dimensions.");
                    heatmapElement.style.width = '100%';
                    heatmapElement.style.height = '500px';
                    heatmapElement.style.minHeight = '400px';
                    heatmapElement.style.display = 'block';

                    // إعادة المحاولة بعد تعيين الأبعاد
                    setTimeout(() => {
                        console.log("Retrying heatmap initialization after setting dimensions in initHeatmapAfterLibraryLoad");
                        initHeatmapAfterLibraryLoad();
                    }, 200);
                    return;
                }

                // طباعة أبعاد حاوية الخريطة للتشخيص
                console.log(`Heatmap container dimensions in initHeatmapAfterLibraryLoad: ${heatmapElement.offsetWidth}x${heatmapElement.offsetHeight}`);

                // التحقق مما إذا كانت الخريطة مهيأة بالفعل
                if (heatmapMap) {
                    console.log("Heatmap already initialized, reusing existing map");

                    // إعادة ضبط حجم الخريطة
                    try {
                        heatmapMap.invalidateSize();
                        console.log("Invalidated size of existing heatmap");
                    } catch (e) {
                        console.warn("Error invalidating size of existing heatmap:", e);
                    }

                    return;
                }

                // إنشاء حاوية جديدة للخريطة إذا كانت الحالية غير صالحة
                if (heatmapElement._leaflet_id) {
                    console.warn("Heatmap element already has a Leaflet map associated with it. Creating a new container.");

                    // إزالة الحاوية الحالية
                    heatmapElement.remove();

                    // إنشاء حاوية جديدة
                    const newHeatmapElement = document.createElement('div');
                    newHeatmapElement.id = 'heatmap';
                    newHeatmapElement.style.width = '100%';
                    newHeatmapElement.style.height = '500px';
                    newHeatmapElement.style.minHeight = '400px';
                    newHeatmapElement.style.borderRadius = '10px';
                    newHeatmapElement.style.display = 'block';

                    // إضافة الحاوية الجديدة إلى الصفحة
                    const cardBody = document.querySelector('.card-body:has(#heatmap)') || document.querySelector('.card:has(.card-header:contains("خريطة حرارية")) .card-body');
                    if (cardBody) {
                        cardBody.appendChild(newHeatmapElement);
                    } else {
                        console.error("Could not find card body to append new heatmap container");
                        return;
                    }
                }

                // إنشاء خريطة
                try {
                    console.log("Creating new Leaflet map for heatmap");

                    heatmapMap = L.map('heatmap', {
                        preferCanvas: true,
                        zoomControl: true,
                        attributionControl: true,
                        dragging: true,
                        scrollWheelZoom: true,
                        doubleClickZoom: true,
                        touchZoom: true,
                        renderer: L.canvas({ padding: 0.5 })
                    });

                    // تعيين مركز الخريطة
                    heatmapMap.setView([32.8872, 13.1913], 6); // مركز ليبيا (طرابلس)

                    console.log("Leaflet map created successfully");
                } catch (e) {
                    console.error("Error creating Leaflet map:", e);
                    return;
                }

                // إضافة طبقة الخريطة
                try {
                    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                        attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                    }).addTo(heatmapMap);

                    console.log("Tile layer added to map");
                } catch (e) {
                    console.error("Error adding tile layer:", e);
                }

                // إضافة أزرار التحكم في الزوم
                try {
                    L.control.zoom({
                        position: 'topright'
                    }).addTo(heatmapMap);

                    console.log("Zoom control added to map");
                } catch (e) {
                    console.error("Error adding zoom control:", e);
                }

                // إضافة مقياس
                try {
                    L.control.scale({
                        position: 'bottomright',
                        imperial: false
                    }).addTo(heatmapMap);

                    console.log("Scale control added to map");
                } catch (e) {
                    console.error("Error adding scale control:", e);
                }

                // إعادة ضبط حجم الخريطة بعد إنشائها
                try {
                    heatmapMap.invalidateSize();
                    console.log("Invalidated size of new heatmap");
                } catch (e) {
                    console.warn("Error invalidating size of new heatmap:", e);
                }

                // جلب بيانات المتاجر بعد تأخير قصير
                setTimeout(() => {
                    try {
                        fetchStoresForHeatmap();
                        console.log("Fetching stores for heatmap");
                    } catch (e) {
                        console.error("Error fetching stores for heatmap:", e);
                    }
                }, 300);

                console.log("Heatmap initialized successfully");

                // إعادة ضبط حجم الخريطة بعد فترة أطول للتأكد من عرضها بشكل صحيح
                setTimeout(() => {
                    if (heatmapMap) {
                        try {
                            heatmapMap.invalidateSize();
                            console.log("Invalidated size of heatmap after delay");
                        } catch (e) {
                            console.warn("Error invalidating size of heatmap after delay:", e);
                        }
                    }
                }, 1000);
            } catch (error) {
                console.error("Error initializing heatmap after library load:", error);
            }
        }

        async function fetchStoresForHeatmap() {
            try {
                const response = await fetch('/api/stores');
                const data = await response.json();

                if (!data) {
                    throw new Error('فشل في جلب بيانات المتاجر للخريطة الحرارية');
                }

                // طباعة البيانات للتشخيص
                console.log("Heatmap stores data:", data);

                heatmapStores = data;

                // إنشاء طبقة الخريطة الحرارية
                createHeatmapLayer();

                // إنشاء طبقة تجميع العلامات
                createMarkerClusterLayer();

                // عرض الطبقة المناسبة حسب الإعداد الحالي
                updateHeatmapView(currentHeatmapView);

            } catch (error) {
                console.error('خطأ في جلب بيانات المتاجر للخريطة الحرارية:', error);
            }
        }

        function createHeatmapLayer() {
            try {
                console.log("Creating heatmap layer...");
                // تحويل بيانات المتاجر إلى تنسيق مناسب للخريطة الحرارية
                const heatmapData = heatmapStores
                    .filter(store => store.latitude && store.longitude)
                    .map(store => {
                        try {
                            const lat = parseFloat(store.latitude);
                            const lng = parseFloat(store.longitude);

                            if (isNaN(lat) || isNaN(lng)) {
                                console.warn(`Invalid coordinates for store ${store.id || 'unknown'}: ${store.latitude}, ${store.longitude}`);
                                return null;
                            }

                            return [lat, lng, 1];
                        } catch (error) {
                            console.error(`Error processing store coordinates: ${error.message}`);
                            return null;
                        }
                    })
                    .filter(item => item !== null); // إزالة العناصر الفارغة

                console.log(`Processed ${heatmapData.length} valid store locations for heatmap`);

                // إنشاء طبقة الخريطة الحرارية
                if (heatmapLayer) {
                    try {
                        heatmapMap.removeLayer(heatmapLayer);
                    } catch (error) {
                        console.warn("Error removing existing heatmap layer:", error);
                    }
                }

                // تحميل مكتبة leaflet-heat.js إذا لم تكن موجودة
                if (typeof L.heatLayer === 'undefined') {
                    console.log("Loading leaflet-heat.js library...");
                    // إضافة مكتبة leaflet-heat.js
                    const script = document.createElement('script');
                    script.src = 'https://unpkg.com/leaflet.heat@0.2.0/dist/leaflet-heat.js';
                    script.onload = function() {
                        console.log("leaflet-heat.js loaded successfully");
                        // إنشاء طبقة الخريطة الحرارية بعد تحميل المكتبة
                        createHeatmapLayerAfterLoad(heatmapData);
                    };
                    script.onerror = function() {
                        console.error("Failed to load leaflet-heat.js");
                    };
                    document.head.appendChild(script);
                } else {
                    // إنشاء طبقة الخريطة الحرارية مباشرة
                    createHeatmapLayerAfterLoad(heatmapData);
                }
            } catch (error) {
                console.error("Error in createHeatmapLayer:", error);
            }
        }

        function createHeatmapLayerAfterLoad(heatmapData) {
            try {
                console.log("Creating heatmap layer with data:", heatmapData.length, "points");

                if (!heatmapData || heatmapData.length === 0) {
                    console.warn("No valid data for heatmap layer");
                    return;
                }

                if (typeof L.heatLayer === 'undefined') {
                    console.error("L.heatLayer is not defined. The leaflet-heat.js library may not be loaded correctly.");
                    return;
                }

                // التأكد من أن الخريطة مرئية وذات أبعاد غير صفرية
                const heatmapElement = document.getElementById('heatmap');
                if (heatmapElement) {
                    // التأكد من أن عنصر الخريطة مرئي وله أبعاد
                    if (heatmapElement.offsetWidth === 0 || heatmapElement.offsetHeight === 0) {
                        console.warn("Heatmap container has zero width or height. Setting minimum dimensions.");
                        heatmapElement.style.width = '100%';
                        heatmapElement.style.height = '400px';
                        heatmapElement.style.display = 'block';

                        // إعادة ضبط حجم الخريطة
                        if (heatmapMap) {
                            setTimeout(() => {
                                heatmapMap.invalidateSize();
                            }, 100);
                        }
                    }
                }

                // تعريف وظيفة مخصصة لإنشاء سياق Canvas مع willReadFrequently
                if (!L.heatLayer._patchApplied) {
                    console.log("Patching L.heatLayer to use willReadFrequently=true");

                    // حفظ الوظيفة الأصلية
                    const originalCreateCanvas = L.DomUtil.create;

                    // استبدال الوظيفة بنسخة معدلة
                    L.DomUtil.create = function(tagName, className, container) {
                        const element = originalCreateCanvas.call(this, tagName, className, container);

                        // إذا كان العنصر هو canvas، قم بتعيين willReadFrequently
                        if (tagName.toLowerCase() === 'canvas') {
                            const context = element.getContext('2d', { willReadFrequently: true });
                            // تخزين سياق معدل في العنصر
                            element._getContext = function() {
                                return context;
                            };
                        }

                        return element;
                    };

                    // تعديل وظيفة getContext في L.Canvas إذا كانت موجودة
                    if (L.Canvas && L.Canvas.prototype) {
                        const originalGetContext = L.Canvas.prototype._initContext;
                        if (originalGetContext) {
                            L.Canvas.prototype._initContext = function() {
                                originalGetContext.call(this);
                                if (this._ctx) {
                                    // إعادة إنشاء السياق مع willReadFrequently
                                    this._ctx = this._canvas.getContext('2d', { willReadFrequently: true });
                                }
                            };
                        }
                    }

                    // تعديل وظيفة getContext في L.HeatLayer إذا كانت موجودة
                    if (L.HeatLayer && L.HeatLayer.prototype) {
                        const originalOnAdd = L.HeatLayer.prototype.onAdd;
                        if (originalOnAdd) {
                            L.HeatLayer.prototype.onAdd = function(map) {
                                const result = originalOnAdd.call(this, map);

                                // إعادة إنشاء السياق مع willReadFrequently إذا كان موجودًا
                                if (this._canvas) {
                                    this._ctx = this._canvas.getContext('2d', { willReadFrequently: true });
                                }

                                return result;
                            };
                        }
                    }

                    // تعيين علامة لتجنب تطبيق التصحيح مرة أخرى
                    L.heatLayer._patchApplied = true;
                }

                // إنشاء طبقة الخريطة الحرارية مع معالجة الأخطاء
                try {
                    heatmapLayer = L.heatLayer(heatmapData, {
                        radius: currentHeatmapIntensity || 25,
                        blur: 15,
                        maxZoom: 10,
                        minOpacity: 0.05,
                        max: 1.0,
                        gradient: {
                            0.4: 'blue',
                            0.6: 'lime',
                            0.8: 'yellow',
                            1.0: 'red'
                        }
                    });

                    // إضافة الطبقة إلى الخريطة مباشرة إذا كان العرض الحالي هو الخريطة الحرارية
                    if (currentHeatmapView === 'heatmap' && heatmapMap) {
                        // التأكد من أن الخريطة جاهزة قبل إضافة الطبقة
                        setTimeout(() => {
                            try {
                                heatmapMap.addLayer(heatmapLayer);
                                console.log("Heatmap layer added to map");

                                // تحريك الخريطة لتظهر جميع النقاط
                                if (heatmapData.length > 0) {
                                    try {
                                        const bounds = L.latLngBounds(heatmapData.map(point => [point[0], point[1]]));
                                        heatmapMap.fitBounds(bounds, { padding: [50, 50] });
                                    } catch (error) {
                                        console.warn("Error fitting map to bounds:", error);
                                    }
                                }
                            } catch (error) {
                                console.error("Error adding heatmap layer to map:", error);
                            }
                        }, 300);
                    }
                } catch (error) {
                    console.error("Error creating heatmap layer:", error);
                }
            } catch (error) {
                console.error("Error in createHeatmapLayerAfterLoad:", error);
            }
        }

        function createMarkerClusterLayer() {
            // إنشاء طبقة تجميع العلامات
            // تحميل مكتبة MarkerCluster إذا لم تكن موجودة
            if (typeof L.MarkerClusterGroup === 'undefined') {
                // إضافة CSS لمكتبة MarkerCluster
                const link = document.createElement('link');
                link.rel = 'stylesheet';
                link.href = 'https://unpkg.com/leaflet.markercluster@1.4.1/dist/MarkerCluster.css';
                document.head.appendChild(link);

                const link2 = document.createElement('link');
                link2.rel = 'stylesheet';
                link2.href = 'https://unpkg.com/leaflet.markercluster@1.4.1/dist/MarkerCluster.Default.css';
                document.head.appendChild(link2);

                // إضافة مكتبة MarkerCluster
                const script = document.createElement('script');
                script.src = 'https://unpkg.com/leaflet.markercluster@1.4.1/dist/leaflet.markercluster.js';
                script.onload = function() {
                    // إنشاء طبقة تجميع العلامات بعد تحميل المكتبة
                    createMarkerClusterLayerAfterLoad();
                };
                document.head.appendChild(script);
            } else {
                // إنشاء طبقة تجميع العلامات مباشرة
                createMarkerClusterLayerAfterLoad();
            }
        }

        function createMarkerClusterLayerAfterLoad() {
            // إنشاء طبقة تجميع العلامات
            if (markerClusterGroup) {
                heatmapMap.removeLayer(markerClusterGroup);
            }

            markerClusterGroup = L.markerClusterGroup({
                showCoverageOnHover: false,
                maxClusterRadius: 50,
                iconCreateFunction: function(cluster) {
                    const count = cluster.getChildCount();
                    let size = 40;
                    if (count > 50) size = 60;
                    else if (count > 20) size = 50;

                    return L.divIcon({
                        html: `<div class="cluster-icon" style="width: ${size}px; height: ${size}px; background-color: rgba(213, 0, 0, 0.8); color: white; display: flex; align-items: center; justify-content: center; border-radius: 50%; font-weight: bold; border: 2px solid white;">${count}</div>`,
                        className: 'custom-cluster-icon',
                        iconSize: L.point(size, size)
                    });
                }
            });

            // إضافة علامات المتاجر إلى طبقة التجميع
            heatmapStores
                .filter(store => store.latitude && store.longitude)
                .forEach(store => {
                    const marker = L.marker([store.latitude, store.longitude], {
                        icon: L.divIcon({
                            className: 'store-marker',
                            html: `<i class="fas fa-map-marker-alt map-marker-alt"></i>`,
                            iconSize: [24, 24],
                            iconAnchor: [12, 24],
                            popupAnchor: [0, -24]
                        })
                    });

                    const region = LibyaRegions.getRegionFromCoordinates(store.latitude, store.longitude);
                    marker.bindPopup(`
                        <div class="store-popup">
                            <h6>${store.name}</h6>
                            <p class="text-muted small mb-1">${region}</p>
                            <p class="mb-1">${store.phone || 'لا يوجد رقم هاتف'}</p>
                            <button class="btn btn-sm btn-primary" onclick="window.location.href='/?store=${store.id}'" style="background: linear-gradient(135deg, var(--loacker-red-dark), var(--loacker-red)); border: none;">
                                <i class="fas fa-eye"></i> عرض التفاصيل
                            </button>
                        </div>
                    `);

                    markerClusterGroup.addLayer(marker);
                });
        }

        function updateHeatmapView(view) {
            try {
                console.log(`Updating heatmap view to: ${view}`);

                // التحقق من وجود الخريطة
                if (!heatmapMap) {
                    console.error("Heatmap map is not initialized");
                    return;
                }

                // تحديث نوع عرض الخريطة الحرارية
                currentHeatmapView = view;

                // إزالة الطبقات الحالية
                try {
                    if (heatmapLayer) {
                        heatmapMap.removeLayer(heatmapLayer);
                        console.log("Removed existing heatmap layer");
                    }
                } catch (e) {
                    console.warn("Error removing heatmap layer:", e);
                }

                try {
                    if (markerClusterGroup) {
                        heatmapMap.removeLayer(markerClusterGroup);
                        console.log("Removed existing marker cluster group");
                    }
                } catch (e) {
                    console.warn("Error removing marker cluster group:", e);
                }

                // إضافة الطبقة المناسبة
                if (view === 'heatmap') {
                    if (heatmapLayer) {
                        try {
                            heatmapMap.addLayer(heatmapLayer);
                            console.log("Added existing heatmap layer to map");
                        } catch (e) {
                            console.warn("Error adding existing heatmap layer:", e);
                            heatmapLayer = null; // إعادة تعيين الطبقة لإنشائها من جديد
                        }
                    }

                    if (!heatmapLayer) {
                        console.log("Creating new heatmap layer...");
                        // محاولة إعادة إنشاء طبقة الخريطة الحرارية
                        createHeatmapLayer();
                    }
                } else if (view === 'cluster') {
                    if (markerClusterGroup) {
                        try {
                            heatmapMap.addLayer(markerClusterGroup);
                            console.log("Added existing marker cluster layer to map");
                        } catch (e) {
                            console.warn("Error adding existing marker cluster group:", e);
                            markerClusterGroup = null; // إعادة تعيين المجموعة لإنشائها من جديد
                        }
                    }

                    if (!markerClusterGroup) {
                        console.log("Creating new marker cluster layer...");
                        // محاولة إعادة إنشاء طبقة تجميع العلامات
                        createMarkerClusterLayer();
                    }
                }

                // تحديث الخريطة
                try {
                    heatmapMap.invalidateSize();
                    console.log("Map size invalidated");

                    // إعادة ضبط حجم الخريطة بعد فترة قصيرة للتأكد من عرضها بشكل صحيح
                    setTimeout(() => {
                        heatmapMap.invalidateSize();
                    }, 500);
                } catch (e) {
                    console.warn("Error invalidating map size:", e);
                }

                // تحديث حالة الأزرار
                document.querySelectorAll('[data-view]').forEach(button => {
                    if (button.dataset.view === view) {
                        button.classList.add('active');
                    } else {
                        button.classList.remove('active');
                    }
                });
            } catch (error) {
                console.error("Error updating heatmap view:", error);
            }
        }

        function updateHeatmapIntensity(intensity) {
            // تحديث كثافة الخريطة الحرارية
            currentHeatmapIntensity = intensity;

            // إعادة إنشاء طبقة الخريطة الحرارية بالكثافة الجديدة
            if (currentHeatmapView === 'heatmap') {
                createHeatmapLayer();
                updateHeatmapView('heatmap');
            }
        }







        function createAddressTable(addressMap) {
            // إنشاء جدول تحليل المتاجر حسب وصف العنوان
            const tableBody = document.getElementById('address-stats-table');
            if (!tableBody) return;

            tableBody.innerHTML = '';

            // ترتيب العناوين حسب عدد المتاجر (تنازلياً)
            const sortedAddresses = Object.entries(addressMap)
                .sort((a, b) => b[1].count - a[1].count);

            sortedAddresses.forEach(([address, data]) => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${address}</td>
                    <td>${data.count}</td>
                    <td><span class="badge bg-success">${data.types.A}</span></td>
                    <td><span class="badge bg-primary">${data.types.B}</span></td>
                    <td><span class="badge bg-warning text-dark">${data.types.D}</span></td>
                    <td><span class="badge bg-secondary">${data.types['أخرى']}</span></td>
                `;
                tableBody.appendChild(row);
            });
        }

        function createTypeAddressTable(typeMap) {
            // إنشاء جدول تحليل المتاجر حسب النوع والمنطقة
            const tableBody = document.getElementById('type-address-stats-table');
            if (!tableBody) return;

            tableBody.innerHTML = '';

            // ترتيب الأنواع حسب عدد المتاجر (تنازلياً)
            const sortedTypes = Object.entries(typeMap)
                .sort((a, b) => b[1].count - a[1].count);

            sortedTypes.forEach(([type, data]) => {
                // العثور على أكثر منطقة شائعة لهذا النوع
                let mostCommonRegion = 'غير محدد';
                let maxCount = 0;

                // استخدام المناطق بدلاً من وصف العنوان
                if (data.regions && Object.keys(data.regions).length > 0) {
                    Object.entries(data.regions).forEach(([region, count]) => {
                        if (count > maxCount) {
                            maxCount = count;
                            mostCommonRegion = region;
                        }
                    });
                } else {
                    // استخدام وصف العنوان كاحتياطي إذا لم تكن هناك مناطق
                    Object.entries(data.addressDescriptions).forEach(([address, count]) => {
                        if (count > maxCount) {
                            maxCount = count;
                            mostCommonRegion = address;
                        }
                    });
                }

                const row = document.createElement('tr');
                row.innerHTML = `
                    <td><span class="badge ${type === 'A' ? 'bg-success' : type === 'B' ? 'bg-primary' : type === 'D' ? 'bg-warning text-dark' : 'bg-secondary'}">${type}</span></td>
                    <td>${data.count}</td>
                    <td>${mostCommonRegion} (${maxCount})</td>
                `;
                tableBody.appendChild(row);
            });
        }

        function createTypeAddressChart(analysis) {
            // إنشاء رسم بياني لتحليل المتاجر حسب النوع والمنطقة
            const ctx = document.getElementById('storeTypeAddressChart');
            if (!ctx) {
                console.error("Could not find storeTypeAddressChart element");
                return;
            }

            try {
                // التحقق من وجود بيانات
                if (!analysis || !analysis.addressMap || Object.keys(analysis.addressMap).length === 0) {
                    console.warn("No address data available for chart");
                    // عرض رسالة بدلاً من استخدام بيانات تجريبية
                    const noDataMessage = document.createElement('div');
                    noDataMessage.className = 'text-center text-muted my-5';
                    noDataMessage.innerHTML = '<i class="fas fa-exclamation-circle fa-3x mb-3"></i><br>لا توجد بيانات متاحة لعرض الرسم البياني';

                    // إضافة الرسالة بدلاً من الرسم البياني
                    const chartContainer = ctx.parentElement;
                    chartContainer.innerHTML = '';
                    chartContainer.appendChild(noDataMessage);
                    return;
                }

                // التحقق من وجود رسم بياني سابق وحذفه
                if (window.typeAddressChart instanceof Chart) {
                    window.typeAddressChart.destroy();
                }

                // الحصول على أكثر 8 مناطق شيوعاً
                const maxRegionsToShow = 8;
                const topRegions = Object.entries(analysis.addressMap)
                    .sort((a, b) => b[1].count - a[1].count)
                    .slice(0, maxRegionsToShow)
                    .map(([region]) => region);

                // التحقق من وجود مناطق
                if (topRegions.length === 0) {
                    console.warn("No regions found");
                    const noDataMessage = document.createElement('div');
                    noDataMessage.className = 'text-center text-muted my-5';
                    noDataMessage.innerHTML = '<i class="fas fa-exclamation-circle fa-3x mb-3"></i><br>لا توجد مناطق متاحة لعرض الرسم البياني';

                    const chartContainer = ctx.parentElement;
                    chartContainer.innerHTML = '';
                    chartContainer.appendChild(noDataMessage);
                    return;
                }

                // تعريف الألوان الثابتة للأنواع
                const typeColors = {
                    'A': '#4CAF50',
                    'B': '#2196F3',
                    'D': '#FF9800',
                    'أخرى': '#9E9E9E'
                };

                // تصفية البيانات للمناطق الفرعية فقط
                const filteredRegions = topRegions.filter(region => region.includes(' - '));

                // تحضير عناوين للعرض - إظهار المناطق فقط إن وجدت
                const shortLabels = filteredRegions.map(region => {
                    // عرض اسم المنطقة الفرعية فقط (الجزء الثاني بعد " - ")
                    return region.split(' - ')[1];
                });

                // التحقق من وجود مناطق فرعية
                if (filteredRegions.length === 0) {
                    console.warn("No sub-regions found for chart display");
                    const noDataMessage = document.createElement('div');
                    noDataMessage.className = 'text-center text-muted my-5';
                    noDataMessage.innerHTML = '<i class="fas fa-info-circle fa-3x mb-3"></i><br>لا توجد مناطق فرعية لعرضها في الرسم البياني<br><small>يتم عرض المدن الرئيسية فقط في الجداول</small>';

                    const chartContainer = ctx.parentElement;
                    chartContainer.innerHTML = '';
                    chartContainer.appendChild(noDataMessage);
                    return;
                }

                // إعداد البيانات للرسم البياني
                const datasets = [
                    {
                        label: 'نوع A',
                        data: filteredRegions.map(region => analysis.addressMap[region].types.A || 0),
                        backgroundColor: typeColors.A,
                        borderColor: 'rgba(0, 0, 0, 0.1)',
                        borderWidth: 1
                    },
                    {
                        label: 'نوع B',
                        data: filteredRegions.map(region => analysis.addressMap[region].types.B || 0),
                        backgroundColor: typeColors.B,
                        borderColor: 'rgba(0, 0, 0, 0.1)',
                        borderWidth: 1
                    },
                    {
                        label: 'نوع D',
                        data: filteredRegions.map(region => analysis.addressMap[region].types.D || 0),
                        backgroundColor: typeColors.D,
                        borderColor: 'rgba(0, 0, 0, 0.1)',
                        borderWidth: 1
                    },
                    {
                        label: 'أخرى',
                        data: filteredRegions.map(region => analysis.addressMap[region].types['أخرى'] || 0),
                        backgroundColor: typeColors['أخرى'],
                        borderColor: 'rgba(0, 0, 0, 0.1)',
                        borderWidth: 1
                    }
                ];

                // إنشاء الرسم البياني
                window.typeAddressChart = new Chart(ctx.getContext('2d'), {
                    type: 'bar',
                    data: {
                        labels: shortLabels,
                        datasets: datasets
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'top',
                                labels: {
                                    color: '#e0e0e0',
                                    font: {
                                        family: 'Tajawal, sans-serif',
                                        size: 12
                                    },
                                    usePointStyle: true,
                                    pointStyle: 'circle'
                                }
                            },
                            title: {
                                display: true,
                                text: 'توزيع أنواع المتاجر حسب المنطقة',
                                color: '#e0e0e0',
                                font: {
                                    family: 'Tajawal, sans-serif',
                                    size: 16,
                                    weight: 'bold'
                                }
                            },
                            tooltip: {
                                callbacks: {
                                    title: function(tooltipItems) {
                                        // عرض اسم المنطقة الكامل في التلميح
                                        const index = tooltipItems[0].dataIndex;
                                        return filteredRegions[index];
                                    },
                                    label: function(context) {
                                        const label = context.dataset.label || '';
                                        const value = context.raw || 0;
                                        const region = filteredRegions[context.dataIndex];
                                        const regionTotal = analysis.addressMap[region].count;
                                        const percentage = regionTotal > 0 ? ((value / regionTotal) * 100).toFixed(1) : 0;
                                        return `${label}: ${value} (${percentage}%)`;
                                    }
                                }
                            }
                        },
                        scales: {
                            x: {
                                stacked: true,
                                ticks: {
                                    color: '#e0e0e0',
                                    font: {
                                        family: 'Tajawal, sans-serif'
                                    }
                                },
                                grid: {
                                    color: 'rgba(255, 255, 255, 0.1)'
                                }
                            },
                            y: {
                                stacked: true,
                                ticks: {
                                    color: '#e0e0e0',
                                    font: {
                                        family: 'Tajawal, sans-serif'
                                    },
                                    precision: 0
                                },
                                grid: {
                                    color: 'rgba(255, 255, 255, 0.1)'
                                }
                            }
                        },
                        animation: {
                            duration: 1000
                        }
                    }
                });
            } catch (error) {
                console.error("Error creating type-address chart:", error);
                // عرض رسالة خطأ للمستخدم
                const errorMessage = document.createElement('div');
                errorMessage.className = 'alert alert-danger';
                errorMessage.innerHTML = '<i class="fas fa-exclamation-triangle me-2"></i>حدث خطأ أثناء إنشاء الرسم البياني';

                const chartContainer = ctx.parentElement;
                chartContainer.innerHTML = '';
                chartContainer.appendChild(errorMessage);
            }
        }

        function analyzeStoresByRegion(stores) {
            // تحليل المتاجر حسب المنطقة باستخدام استخراج البيانات من عنوان المتجر واسم المتجر
            const regionMap = {};

            if (!stores || !Array.isArray(stores)) {
                console.error("Invalid stores data in analyzeStoresByRegion:", stores);
                return regionMap;
            }

            // التحقق من وجود بيانات المتاجر
            if (stores.length === 0) {
                console.warn("No stores data available for region analysis");
                return regionMap;
            }

            try {
                console.log(`بدء تحليل ${stores.length} متجر حسب المنطقة...`);

                stores.forEach((store, index) => {
                    // استخراج معلومات العنوان من المتجر
                    const addressInfo = LibyaRegions.parseStoreAddress(store);

                    // إذا لم نحصل على عنوان صالح، حاول استخراجه من اسم المتجر
                    let regionKey = addressInfo.fullAddress;
                    let mainCity = addressInfo.mainCity;
                    let subRegion = addressInfo.subRegion;
                    let isSubRegion = addressInfo.isSubRegion;

                    // إذا لم نحصل على عنوان من الحقول المخصصة، حاول استخراجه من اسم المتجر
                    if (!regionKey && store.name) {
                        const nameInfo = LibyaRegions.parseAddress(store.name);
                        if (nameInfo.fullAddress) {
                            regionKey = nameInfo.fullAddress;
                            mainCity = nameInfo.mainCity;
                            subRegion = nameInfo.subRegion;
                            isSubRegion = nameInfo.isSubRegion;
                        }
                    }

                    // تجاهل المتاجر التي تحتوي على "المنطقة غير محددة" فقط
                    if (regionKey === 'المنطقة غير محددة') {
                        console.log(`تجاهل المتجر ${store.name || 'غير معروف'} - يحتوي على رسالة تنبيه`);
                        return;
                    }

                    // إذا لم نحصل على أي معلومات، استخدم "غير محدد"
                    if (!regionKey) {
                        regionKey = "غير محدد";
                        mainCity = "غير محدد";
                        subRegion = null;
                        isSubRegion = false;
                    }

                    // إنشاء مفتاح المنطقة في الخريطة إذا لم يكن موجوداً
                    if (!regionMap[regionKey]) {
                        regionMap[regionKey] = {
                            total: 0,
                            typeA: 0,
                            typeB: 0,
                            typeD: 0,
                            other: 0,
                            mainCity: mainCity,
                            subRegion: subRegion,
                            isSubRegion: isSubRegion
                        };
                    }

                    // زيادة العدد الإجمالي
                    regionMap[regionKey].total++;

                    // تصنيف حسب نوع المتجر
                    const storeType = store.type ? store.type.toUpperCase() : '';
                    if (storeType === 'A') {
                        regionMap[regionKey].typeA++;
                    } else if (storeType === 'B') {
                        regionMap[regionKey].typeB++;
                    } else if (storeType === 'D') {
                        regionMap[regionKey].typeD++;
                    } else {
                        regionMap[regionKey].other++;
                    }
                });

                const totalRegions = Object.keys(regionMap).length;
                const totalStores = Object.values(regionMap).reduce((sum, region) => sum + region.total, 0);

                console.log(`تم تحليل ${totalStores} متجر في ${totalRegions} منطقة`);
                console.log('تفاصيل المناطق:');
                Object.keys(regionMap).forEach(regionName => {
                    const regionData = regionMap[regionName];
                    console.log(`- ${regionName}: ${regionData.total} متجر (مدينة: ${regionData.mainCity})`);
                });

                return regionMap;
            } catch (error) {
                console.error("Error in analyzeStoresByRegion:", error);
                return regionMap;
            }
        }

        function analyzeStoresByAddressAndType(stores) {
            // تحليل المتاجر حسب المنطقة ونوع المتجر مع التركيز على المدن الرئيسية
            const addressMap = {};
            const typeMap = {
                'A': { count: 0, addressDescriptions: {}, regions: {} },
                'B': { count: 0, addressDescriptions: {}, regions: {} },
                'D': { count: 0, addressDescriptions: {}, regions: {} },
                'أخرى': { count: 0, addressDescriptions: {}, regions: {} }
            };

            if (!stores || !Array.isArray(stores)) {
                console.error("Invalid stores data in analyzeStoresByAddressAndType:", stores);
                return { addressMap, typeMap };
            }

            console.log(`بدء تحليل ${stores.length} متجر حسب العنوان والنوع...`);

            stores.forEach(store => {
                // استخراج معلومات العنوان من المتجر باستخدام الدالة المحدثة
                const addressInfo = LibyaRegions.parseStoreAddress(store);

                // تجاهل المتاجر التي تحتوي على "المنطقة غير محددة" فقط
                if (addressInfo.fullAddress === 'المنطقة غير محددة') {
                    return;
                }

                // تحديد المفتاح للعرض في الرسم البياني
                let displayKey = null;

                if (addressInfo.mainCity && addressInfo.subRegion) {
                    // إذا كان هناك مدينة ومنطقة: "المدينة - المنطقة"
                    displayKey = addressInfo.fullAddress;
                } else if (addressInfo.mainCity) {
                    // إذا كان هناك مدينة فقط: "المدينة"
                    displayKey = addressInfo.mainCity;
                } else {
                    // إذا لم يكن هناك عنوان صالح، تجاهل
                    return;
                }

                // إضافة إلى addressMap
                if (!addressMap[displayKey]) {
                    addressMap[displayKey] = {
                        count: 0,
                        types: { 'A': 0, 'B': 0, 'D': 0, 'أخرى': 0 },
                        regions: {},
                        mainCity: addressInfo.mainCity,
                        subRegion: addressInfo.subRegion,
                        isSubRegion: addressInfo.isSubRegion
                    };
                }

                addressMap[displayKey].count++;

                // تحديث عدد المتاجر حسب النوع
                const storeType = ['A', 'B', 'D'].includes(store.type) ? store.type : 'أخرى';
                addressMap[displayKey].types[storeType]++;

                // إضافة إلى regions
                if (!addressMap[displayKey].regions[displayKey]) {
                    addressMap[displayKey].regions[displayKey] = 0;
                }
                addressMap[displayKey].regions[displayKey]++;
            });

            // تحديث typeMap بناءً على البيانات المجمعة
            Object.keys(addressMap).forEach(displayKey => {
                const addressData = addressMap[displayKey];

                // إضافة بيانات كل نوع إلى typeMap
                ['A', 'B', 'D', 'أخرى'].forEach(type => {
                    const count = addressData.types[type];
                    if (count > 0) {
                        typeMap[type].count += count;

                        // إضافة وصف العنوان
                        if (!typeMap[type].addressDescriptions[displayKey]) {
                            typeMap[type].addressDescriptions[displayKey] = 0;
                        }
                        typeMap[type].addressDescriptions[displayKey] += count;

                        // إضافة المنطقة (استخدام المدينة الرئيسية للتجميع)
                        const regionKey = addressData.mainCity || displayKey;
                        if (!typeMap[type].regions[regionKey]) {
                            typeMap[type].regions[regionKey] = 0;
                        }
                        typeMap[type].regions[regionKey] += count;
                    }
                });
            });

            console.log(`تم تحليل ${Object.keys(addressMap).length} منطقة/مدينة`);
            console.log('تفاصيل addressMap:', addressMap);
            console.log('تفاصيل typeMap:', typeMap);

            return { addressMap, typeMap };
        }

        /**
         * تحديث قوائم التصفية بناءً على البيانات المستخرجة من عنوان المتجر واسم المتجر
         * @param {Array} stores - مصفوفة المتاجر
         */
        function updateFilterOptions(stores) {
            if (!stores || !Array.isArray(stores)) {
                console.error("Invalid stores data for filter update");
                return;
            }

            console.log(`بدء تحديث قوائم التصفية لـ ${stores.length} متجر...`);

            // استخراج المدن والمناطق من المتاجر باستخدام النظام المحسن
            const citiesSet = new Set();
            const regionsSet = new Set();
            const cityRegionsMap = {};

            stores.forEach(store => {
                // استخراج معلومات العنوان من المتجر
                const addressInfo = LibyaRegions.parseStoreAddress(store);

                // إذا لم نحصل على عنوان صالح، حاول استخراجه من اسم المتجر
                let regionKey = addressInfo.fullAddress;
                let mainCity = addressInfo.mainCity;
                let subRegion = addressInfo.subRegion;

                // إذا لم نحصل على عنوان من الحقول المخصصة، حاول استخراجه من اسم المتجر
                if (!regionKey && store.name) {
                    const nameInfo = LibyaRegions.parseAddress(store.name);
                    if (nameInfo.fullAddress) {
                        regionKey = nameInfo.fullAddress;
                        mainCity = nameInfo.mainCity;
                        subRegion = nameInfo.subRegion;
                    }
                }

                // تجاهل المتاجر التي تحتوي على "المنطقة غير محددة" فقط
                if (regionKey === 'المنطقة غير محددة') {
                    return;
                }

                // إضافة المدينة الرئيسية إذا وجدت
                if (mainCity && mainCity !== "غير محدد") {
                    citiesSet.add(mainCity);

                    // إنشاء خريطة للمناطق الفرعية لكل مدينة
                    if (!cityRegionsMap[mainCity]) {
                        cityRegionsMap[mainCity] = new Set();
                    }

                    // إضافة المنطقة الفرعية إذا وجدت
                    if (subRegion && subRegion !== mainCity) {
                        cityRegionsMap[mainCity].add(subRegion);
                    }
                }

                // إضافة المنطقة الكاملة إلى قائمة المناطق
                if (regionKey && regionKey !== "غير محدد") {
                    regionsSet.add(regionKey);
                }
            });

            // تحويل المجموعات إلى مصفوفات مرتبة
            const cities = Array.from(citiesSet).sort();
            const regions = Array.from(regionsSet).sort();

            // تحديث قائمة المناطق في التصفية
            const regionFilter = document.getElementById('region-filter');
            if (regionFilter) {
                // حفظ القيمة المحددة حالياً
                const currentValue = regionFilter.value;

                // مسح الخيارات الحالية (عدا الخيار الأول)
                regionFilter.innerHTML = '<option value="all" selected>جميع المناطق</option>';

                // إضافة المدن الرئيسية
                cities.forEach(city => {
                    const option = document.createElement('option');
                    option.value = city;
                    option.textContent = `${city} (مدينة رئيسية)`;
                    option.style.fontWeight = 'bold';
                    option.style.color = '#d50000';
                    regionFilter.appendChild(option);

                    // إضافة المناطق الفرعية لهذه المدينة
                    if (cityRegionsMap[city]) {
                        const subRegions = Array.from(cityRegionsMap[city]).sort();
                        subRegions.forEach(subRegion => {
                            const subOption = document.createElement('option');
                            subOption.value = `${city} - ${subRegion}`;
                            subOption.textContent = `  └─ ${subRegion}`;
                            subOption.style.paddingLeft = '20px';
                            subOption.style.color = '#666';
                            regionFilter.appendChild(subOption);
                        });
                    }
                });

                // إضافة فاصل
                if (regions.length > 0) {
                    const separator = document.createElement('option');
                    separator.disabled = true;
                    separator.textContent = '──────────────';
                    regionFilter.appendChild(separator);

                    // إضافة المناطق الأخرى التي لا تتبع نمط "مدينة - منطقة"
                    regions.forEach(region => {
                        if (!region.includes(' - ') && !cities.includes(region)) {
                            const option = document.createElement('option');
                            option.value = region;
                            option.textContent = region;
                            regionFilter.appendChild(option);
                        }
                    });
                }

                // استعادة القيمة المحددة إذا كانت لا تزال موجودة
                if (currentValue && Array.from(regionFilter.options).some(opt => opt.value === currentValue)) {
                    regionFilter.value = currentValue;
                }
            }

            console.log(`تم تحديث قائمة التصفية: ${cities.length} مدينة رئيسية، ${regions.length} منطقة إجمالية`);
            console.log('المدن الرئيسية:', cities);
            console.log('المناطق الفرعية:', cityRegionsMap);
        }

        /**
         * إنشاء تقرير مفصل للإحصائيات
         * @param {Array} stores - مصفوفة المتاجر
         * @returns {Object} - كائن يحتوي على تقرير مفصل
         */
        function generateDetailedReport(stores) {
            if (!stores || !Array.isArray(stores)) {
                return {
                    error: "بيانات المتاجر غير صالحة",
                    totalStores: 0,
                    cities: {},
                    regions: {},
                    summary: {}
                };
            }

            const groupedData = LibyaRegions.groupStoresByRegions(stores);
            const reportDate = new Date().toLocaleDateString('ar-EG');

            // إنشاء ملخص التقرير
            const summary = {
                reportDate: reportDate,
                totalStores: groupedData.totalStores,
                storesWithValidAddress: groupedData.storesWithValidAddress,
                storesWithoutAddress: groupedData.storesWithoutAddress,
                totalCities: Object.keys(groupedData.cities).length,
                totalRegions: Object.keys(groupedData.regions).length,
                preparationEntity: "نظام Loacker لإدارة المتاجر"
            };

            // العثور على المدينة الأكثر والأقل متاجر
            let mostStoresCity = null;
            let leastStoresCity = null;
            let maxStores = 0;
            let minStores = Infinity;

            Object.keys(groupedData.cities).forEach(cityName => {
                const cityData = groupedData.cities[cityName];
                if (cityData.totalStores > maxStores) {
                    maxStores = cityData.totalStores;
                    mostStoresCity = { name: cityName, count: cityData.totalStores };
                }
                if (cityData.totalStores < minStores) {
                    minStores = cityData.totalStores;
                    leastStoresCity = { name: cityName, count: cityData.totalStores };
                }
            });

            summary.mostStoresCity = mostStoresCity;
            summary.leastStoresCity = leastStoresCity;

            return {
                summary: summary,
                cities: groupedData.cities,
                regions: groupedData.regions,
                totalStores: groupedData.totalStores,
                storesWithValidAddress: groupedData.storesWithValidAddress,
                storesWithoutAddress: groupedData.storesWithoutAddress
            };
        }

        // تم نقل دالة analyzeStoresByDetailedRegion إلى ملف region-details.js

        // تم نقل دالة getRegionFromCoordinates إلى ملف region-details.js

        function createTypeChart(storesByType) {
            // إنشاء الرسم البياني لتوزيع المتاجر حسب النوع
            const ctx = document.getElementById('storeTypeChart');
            if (!ctx) {
                console.error("Could not find storeTypeChart element");
                return;
            }

            try {
                // التحقق من وجود بيانات
                if (!storesByType || typeof storesByType !== 'object' || Object.keys(storesByType).length === 0) {
                    console.warn("No type data available for chart");
                    // عرض رسالة بدلاً من استخدام بيانات تجريبية
                    const noDataMessage = document.createElement('div');
                    noDataMessage.className = 'text-center text-muted my-5';
                    noDataMessage.innerHTML = '<i class="fas fa-exclamation-circle fa-3x mb-3"></i><br>لا توجد بيانات متاحة لعرض الرسم البياني';

                    // إضافة الرسالة بدلاً من الرسم البياني
                    const chartContainer = ctx.parentElement;
                    chartContainer.innerHTML = '';
                    chartContainer.appendChild(noDataMessage);
                    return;
                }

                // تنظيم البيانات للتأكد من وجود الأنواع الرئيسية
                const organizedData = {
                    'نوع A': storesByType['نوع A'] || storesByType['A'] || 0,
                    'نوع B': storesByType['نوع B'] || storesByType['B'] || 0,
                    'نوع D': storesByType['نوع D'] || storesByType['D'] || 0
                };

                // إضافة الأنواع الأخرى إذا وجدت
                if (storesByType['أخرى'] || storesByType['other']) {
                    organizedData['أخرى'] = storesByType['أخرى'] || storesByType['other'] || 0;
                }

                // التحقق من وجود رسم بياني سابق وحذفه
                if (window.typeChart instanceof Chart) {
                    window.typeChart.destroy();
                }

                // تعريف الألوان الثابتة للأنواع
                const typeColors = {
                    'نوع A': '#4CAF50',
                    'نوع B': '#2196F3',
                    'نوع D': '#FF9800',
                    'أخرى': '#9E9E9E'
                };

                // إنشاء مصفوفات البيانات والألوان
                const labels = Object.keys(organizedData);
                const data = Object.values(organizedData);
                const backgroundColor = labels.map(label => typeColors[label] || '#9E9E9E');

                // التحقق من وجود بيانات بعد التنظيم
                if (data.reduce((sum, val) => sum + val, 0) === 0) {
                    console.warn("No data available after organization");
                    const noDataMessage = document.createElement('div');
                    noDataMessage.className = 'text-center text-muted my-5';
                    noDataMessage.innerHTML = '<i class="fas fa-exclamation-circle fa-3x mb-3"></i><br>لا توجد بيانات متاحة لعرض الرسم البياني';

                    const chartContainer = ctx.parentElement;
                    chartContainer.innerHTML = '';
                    chartContainer.appendChild(noDataMessage);
                    return;
                }

                // إنشاء الرسم البياني
                window.typeChart = new Chart(ctx.getContext('2d'), {
                    type: 'doughnut',
                    data: {
                        labels: labels,
                        datasets: [{
                            data: data,
                            backgroundColor: backgroundColor,
                            borderWidth: 1,
                            borderColor: '#212529'
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom',
                                labels: {
                                    padding: 15,
                                    usePointStyle: true,
                                    pointStyle: 'circle',
                                    color: '#e0e0e0',
                                    font: {
                                        family: 'Tajawal, sans-serif',
                                        size: 12
                                    }
                                }
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        const label = context.label || '';
                                        const value = context.raw || 0;
                                        const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                        const percentage = total > 0 ? Math.round((value / total) * 100) : 0;
                                        return `${label}: ${value} (${percentage}%)`;
                                    }
                                }
                            },
                            title: {
                                display: true,
                                text: 'توزيع المتاجر حسب النوع',
                                color: '#e0e0e0',
                                font: {
                                    family: 'Tajawal, sans-serif',
                                    size: 16,
                                    weight: 'bold'
                                }
                            }
                        },
                        animation: {
                            animateScale: true,
                            animateRotate: true,
                            duration: 1000
                        }
                    }
                });
            } catch (error) {
                console.error("Error creating type chart:", error);
                // عرض رسالة خطأ للمستخدم
                const errorMessage = document.createElement('div');
                errorMessage.className = 'alert alert-danger';
                errorMessage.innerHTML = '<i class="fas fa-exclamation-triangle me-2"></i>حدث خطأ أثناء إنشاء الرسم البياني';

                const chartContainer = ctx.parentElement;
                chartContainer.innerHTML = '';
                chartContainer.appendChild(errorMessage);
            }
        }

        function createRegionChart(storesByRegion) {
            const ctx = document.getElementById('storeRegionChart');
            if (!ctx) {
                console.error("Could not find storeRegionChart element");
                return;
            }

            try {
                // التحقق من وجود بيانات
                if (!storesByRegion || typeof storesByRegion !== 'object' || Object.keys(storesByRegion).length === 0) {
                    console.warn("No region data available for chart");
                    // عرض رسالة بدلاً من استخدام بيانات تجريبية
                    const noDataMessage = document.createElement('div');
                    noDataMessage.className = 'text-center text-muted my-5';
                    noDataMessage.innerHTML = '<i class="fas fa-exclamation-circle fa-3x mb-3"></i><br>لا توجد بيانات متاحة لعرض الرسم البياني';

                    // إضافة الرسالة بدلاً من الرسم البياني
                    const chartContainer = ctx.parentElement;
                    chartContainer.innerHTML = '';
                    chartContainer.appendChild(noDataMessage);
                    return;
                }

                // التحقق من وجود رسم بياني سابق وحذفه
                if (window.regionChart instanceof Chart) {
                    window.regionChart.destroy();
                }

                // تجميع المتاجر حسب المدن الرئيسية فقط
                const mainCitiesData = {};

                // تجميع البيانات حسب المدن الرئيسية من storesByRegion
                Object.keys(storesByRegion).forEach(regionName => {
                    const regionData = storesByRegion[regionName];
                    let mainCity = regionData.mainCity;

                    // إذا لم تكن هناك مدينة رئيسية محددة، استخرجها من اسم المنطقة
                    if (!mainCity && regionName.includes(' - ')) {
                        mainCity = regionName.split(' - ')[0];
                    } else if (!mainCity) {
                        mainCity = regionName;
                    }

                    // تجميع البيانات حسب المدينة الرئيسية
                    if (!mainCitiesData[mainCity]) {
                        mainCitiesData[mainCity] = {
                            total: 0,
                            typeA: 0,
                            typeB: 0,
                            typeD: 0,
                            other: 0
                        };
                    }

                    mainCitiesData[mainCity].total += regionData.total || 0;
                    mainCitiesData[mainCity].typeA += regionData.typeA || 0;
                    mainCitiesData[mainCity].typeB += regionData.typeB || 0;
                    mainCitiesData[mainCity].typeD += regionData.typeD || 0;
                    mainCitiesData[mainCity].other += regionData.other || 0;
                });

                // الحصول على المدن الرئيسية
                const mainCities = Object.keys(mainCitiesData);

                // التحقق من وجود مدن رئيسية
                if (mainCities.length === 0) {
                    console.warn("No main cities found");
                    const noDataMessage = document.createElement('div');
                    noDataMessage.className = 'text-center text-muted my-5';
                    noDataMessage.innerHTML = '<i class="fas fa-exclamation-circle fa-3x mb-3"></i><br>لا توجد مدن رئيسية متاحة لعرض الرسم البياني';

                    const chartContainer = ctx.parentElement;
                    chartContainer.innerHTML = '';
                    chartContainer.appendChild(noDataMessage);
                    return;
                }

                // ترتيب المدن حسب عدد المتاجر (تنازلياً)
                const sortedCities = mainCities.sort((a, b) =>
                    mainCitiesData[b].total - mainCitiesData[a].total
                );

                console.log('المدن الرئيسية المستخرجة:', mainCitiesData);
                console.log('ترتيب المدن حسب عدد المتاجر:', sortedCities.map(city => `${city}: ${mainCitiesData[city].total} متجر`));

                // استخدام جميع المدن الرئيسية (عادة لن تكون كثيرة)
                const topCities = sortedCities;
                const storeCounts = topCities.map(city => mainCitiesData[city].total);

                // إنشاء ألوان متدرجة للرسم البياني
                const colors = topCities.map((_, index) => {
                    const intensity = 0.5 + (0.5 * (index / topCities.length));
                    return `rgba(213, 0, 0, ${intensity})`;
                });

                // إنشاء ألوان الحدود
                const borderColors = topCities.map((_, index) => {
                    const intensity = 0.7 + (0.3 * (index / topCities.length));
                    return `rgba(213, 0, 0, ${intensity})`;
                });

                // استخدام أسماء المدن مباشرة (لن تكون طويلة)
                const cityLabels = topCities;

                // إنشاء الرسم البياني
                window.regionChart = new Chart(ctx.getContext('2d'), {
                    type: 'bar',
                    data: {
                        labels: cityLabels,
                        datasets: [{
                            label: 'عدد المتاجر',
                            data: storeCounts,
                            backgroundColor: colors,
                            borderColor: borderColors,
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            },
                            title: {
                                display: true,
                                text: 'توزيع المتاجر حسب المدن الرئيسية',
                                color: '#e0e0e0',
                                font: {
                                    family: 'Tajawal, sans-serif',
                                    size: 16,
                                    weight: 'bold'
                                }
                            },
                            tooltip: {
                                callbacks: {
                                    title: function(tooltipItems) {
                                        // عرض اسم المدينة في التلميح
                                        const index = tooltipItems[0].dataIndex;
                                        return topCities[index];
                                    },
                                    label: function(context) {
                                        const city = topCities[context.dataIndex];
                                        const cityData = mainCitiesData[city];
                                        const count = context.raw;

                                        // حساب النسب المئوية
                                        const totalStores = Object.values(mainCitiesData).reduce((sum, c) => sum + (c.total || 0), 0);
                                        const percentage = totalStores > 0 ? ((cityData.total / totalStores) * 100).toFixed(1) : 0;

                                        return [
                                            `إجمالي المتاجر: ${count} (${percentage}%)`,
                                            `نوع A: ${cityData.typeA}`,
                                            `نوع B: ${cityData.typeB}`,
                                            `نوع D: ${cityData.typeD}`,
                                            `أخرى: ${cityData.other}`
                                        ];
                                    }
                                }
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                ticks: {
                                    precision: 0,
                                    color: '#e0e0e0',
                                    font: {
                                        family: 'Tajawal, sans-serif'
                                    }
                                },
                                title: {
                                    display: true,
                                    text: 'عدد المتاجر',
                                    color: '#e0e0e0',
                                    font: {
                                        family: 'Tajawal, sans-serif',
                                        size: 14
                                    }
                                },
                                grid: {
                                    color: 'rgba(255, 255, 255, 0.1)'
                                }
                            },
                            x: {
                                ticks: {
                                    color: '#e0e0e0',
                                    font: {
                                        family: 'Tajawal, sans-serif'
                                    },
                                    maxRotation: 45,
                                    minRotation: 45
                                },
                                title: {
                                    display: true,
                                    text: 'المدن الرئيسية',
                                    color: '#e0e0e0',
                                    font: {
                                        family: 'Tajawal, sans-serif',
                                        size: 14
                                    }
                                },
                                grid: {
                                    color: 'rgba(255, 255, 255, 0.1)'
                                }
                            }
                        },
                        animation: {
                            duration: 1000
                        }
                    }
                });
            } catch (error) {
                console.error("Error creating region chart:", error);
                // عرض رسالة خطأ للمستخدم
                const errorMessage = document.createElement('div');
                errorMessage.className = 'alert alert-danger';
                errorMessage.innerHTML = '<i class="fas fa-exclamation-triangle me-2"></i>حدث خطأ أثناء إنشاء الرسم البياني';

                const chartContainer = ctx.parentElement;
                chartContainer.innerHTML = '';
                chartContainer.appendChild(errorMessage);
            }
        }

        function createDetailTable(storesByRegion, storesByType) {
            const tableBody = document.getElementById('region-stats-table');
            tableBody.innerHTML = '';

            // استخدام التحليل التفصيلي للمناطق
            const detailedRegions = analyzeStoresByDetailedRegion(window.storesData);
            const totalStores = Object.values(detailedRegions).reduce((sum, region) => sum + region.total, 0);

            // إنشاء صف للإجمالي
            const totalRow = document.createElement('tr');
            totalRow.innerHTML = `
                <td>
                    <span class="fw-bold">الإجمالي</span>
                </td>
                <td class="text-center">
                    <button class="btn btn-sm btn-outline-info details-btn" data-region="الإجمالي">
                        <i class="fas fa-exclamation-circle"></i>
                    </button>
                </td>
                <td>${totalStores}</td>
                <td><span class="badge bg-success">${storesByType['نوع A'] || 0}</span></td>
                <td><span class="badge bg-primary">${storesByType['نوع B'] || 0}</span></td>
                <td><span class="badge bg-warning text-dark">${storesByType['نوع D'] || 0}</span></td>
                <td>
                    <div class="progress" style="height: 20px;">
                        <div class="progress-bar" role="progressbar" style="width: 100%; background: linear-gradient(135deg, var(--loacker-red-dark), var(--loacker-red));"
                            aria-valuenow="100" aria-valuemin="0" aria-valuemax="100">
                            100%
                        </div>
                    </div>
                </td>
            `;
            tableBody.appendChild(totalRow);

            // إنشاء صف لطرابلس مع سهم لإظهار المناطق الفرعية
            const tripoliStores = LibyaRegions.tripoliDistricts.reduce((sum, district) => {
                const districtName = district.name;
                const storesInDistrict = detailedRegions[districtName] ? detailedRegions[districtName].total : 0;
                return sum + storesInDistrict;
            }, 0);

            // حساب عدد المتاجر حسب النوع في طرابلس
            let tripoliTypeA = 0, tripoliTypeB = 0, tripoliTypeD = 0;
            LibyaRegions.tripoliDistricts.forEach(district => {
                const districtName = district.name;
                if (detailedRegions[districtName]) {
                    tripoliTypeA += detailedRegions[districtName].typeA || 0;
                    tripoliTypeB += detailedRegions[districtName].typeB || 0;
                    tripoliTypeD += detailedRegions[districtName].typeD || 0;
                }
            });

            const tripoliPercentage = ((tripoliStores / totalStores) * 100).toFixed(1);

            const tripoliRow = document.createElement('tr');
            tripoliRow.className = 'main-region';
            tripoliRow.innerHTML = `
                <td>
                    <span class="fw-bold">طرابلس</span>
                    <i class="fas fa-chevron-down ms-2 toggle-subregions" data-region="tripoli"></i>
                </td>
                <td class="text-center">
                    <button class="btn btn-sm btn-outline-info details-btn" data-region="طرابلس">
                        <i class="fas fa-exclamation-circle"></i>
                    </button>
                </td>
                <td>${tripoliStores}</td>
                <td><span class="badge bg-success">${tripoliTypeA}</span></td>
                <td><span class="badge bg-primary">${tripoliTypeB}</span></td>
                <td><span class="badge bg-warning text-dark">${tripoliTypeD}</span></td>
                <td>
                    <div class="progress" style="height: 20px;">
                        <div class="progress-bar" role="progressbar" style="width: ${tripoliPercentage}%; background: linear-gradient(135deg, var(--loacker-red-dark), var(--loacker-red));"
                            aria-valuenow="${tripoliPercentage}" aria-valuemin="0" aria-valuemax="100">
                            ${tripoliPercentage}%
                        </div>
                    </div>
                </td>
            `;
            tableBody.appendChild(tripoliRow);

            // إضافة المناطق الفرعية لطرابلس
            const tripoliDistricts = [
                "باب بن غشير", "حي الأندلس", "أبو سليم", "سوق الجمعة", "تاجوراء", "عين زارة", "الفرناج",
                "قصر بن غشير", "الهضبة الخضراء", "غوط الشعال", "السياحية", "زاوية الدهماني", "فشلوم",
                "زاوية المحجوب", "سيدي المصري", "السراج", "المدينة القديمة", "قرقارش", "الكريمية",
                "السبعة", "الحشان", "طريق المطار", "مشروع الهضبة", "عرادة", "الحي الإسلامي",
                "أبوسليم المشروع", "الظهرة", "حي دمشق", "النوفليين", "السياحية الغربية",
                "الكريمية الشرقية", "جنزور", "السواني", "القره بوللي", "الماية", "الساعدية",
                "العزيزية", "الكريمية طريق الساحلي", "النجيلة", "الطويشة", "بوسليم بئر الأسطى ميلاد"
            ];

            tripoliDistricts.forEach(district => {
                const fullDistrictName = "طرابلس - " + district;
                const districtStats = detailedRegions[fullDistrictName] || {
                    total: 0,
                    typeA: 0,
                    typeB: 0,
                    typeD: 0
                };

                const districtPercentage = districtStats.total > 0 ? ((districtStats.total / totalStores) * 100).toFixed(1) : "0.0";

                const districtRow = document.createElement('tr');
                districtRow.className = 'subregion-row subregion-tripoli d-none';
                districtRow.innerHTML = `
                    <td>
                        <span class="ms-3">- ${district}</span>
                    </td>
                    <td class="text-center">
                        <button class="btn btn-sm btn-outline-info details-btn" data-region="طرابلس - ${district}">
                            <i class="fas fa-exclamation-circle"></i>
                        </button>
                    </td>
                    <td>${districtStats.total}</td>
                    <td><span class="badge bg-success">${districtStats.typeA}</span></td>
                    <td><span class="badge bg-primary">${districtStats.typeB}</span></td>
                    <td><span class="badge bg-warning text-dark">${districtStats.typeD}</span></td>
                    <td>
                        <div class="progress" style="height: 15px;">
                            <div class="progress-bar" role="progressbar" style="width: ${districtPercentage}%; background: linear-gradient(135deg, #6a11cb, #2575fc);"
                                aria-valuenow="${districtPercentage}" aria-valuemin="0" aria-valuemax="100">
                                ${districtPercentage}%
                            </div>
                        </div>
                    </td>
                `;

                tableBody.appendChild(districtRow);
            });

            // إنشاء صف لبنغازي مع سهم لإظهار المناطق الفرعية
            const benghaziStores = LibyaRegions.benghaziDistricts.reduce((sum, district) => {
                const districtName = district.name;
                const storesInDistrict = detailedRegions[districtName] ? detailedRegions[districtName].total : 0;
                return sum + storesInDistrict;
            }, 0);

            // حساب عدد المتاجر حسب النوع في بنغازي
            let benghaziTypeA = 0, benghaziTypeB = 0, benghaziTypeD = 0;
            LibyaRegions.benghaziDistricts.forEach(district => {
                const districtName = district.name;
                if (detailedRegions[districtName]) {
                    benghaziTypeA += detailedRegions[districtName].typeA || 0;
                    benghaziTypeB += detailedRegions[districtName].typeB || 0;
                    benghaziTypeD += detailedRegions[districtName].typeD || 0;
                }
            });

            const benghaziPercentage = ((benghaziStores / totalStores) * 100).toFixed(1);

            const benghaziRow = document.createElement('tr');
            benghaziRow.className = 'main-region';
            benghaziRow.innerHTML = `
                <td>
                    <span class="fw-bold">بنغازي</span>
                    <i class="fas fa-chevron-down ms-2 toggle-subregions" data-region="benghazi"></i>
                </td>
                <td class="text-center">
                    <button class="btn btn-sm btn-outline-info details-btn" data-region="بنغازي">
                        <i class="fas fa-exclamation-circle"></i>
                    </button>
                </td>
                <td>${benghaziStores}</td>
                <td><span class="badge bg-success">${benghaziTypeA}</span></td>
                <td><span class="badge bg-primary">${benghaziTypeB}</span></td>
                <td><span class="badge bg-warning text-dark">${benghaziTypeD}</span></td>
                <td>
                    <div class="progress" style="height: 20px;">
                        <div class="progress-bar" role="progressbar" style="width: ${benghaziPercentage}%; background: linear-gradient(135deg, var(--loacker-red-dark), var(--loacker-red));"
                            aria-valuenow="${benghaziPercentage}" aria-valuemin="0" aria-valuemax="100">
                            ${benghaziPercentage}%
                        </div>
                    </div>
                </td>
            `;
            tableBody.appendChild(benghaziRow);

            // إضافة المناطق الفرعية لبنغازي
            const benghaziDistricts = [
                "البلاد", "الصابري", "البركة", "السلماني الشرقي", "السلماني الغربي", "سيدي حسين", "الكيش",
                "الفويهات", "اللثامة", "الماجوري", "المحيشي", "الرويسات", "الكويفية", "قنفودة", "جروثة",
                "بو فاخرة", "بوعطني", "الهواري", "بنينا", "سيدي خليفة", "قمينس", "سلوق", "حي الحدائق",
                "حي الفاتح", "رأس اعبيدة", "الزريريعية"
            ];

            benghaziDistricts.forEach(district => {
                const fullDistrictName = "بنغازي - " + district;
                const districtStats = detailedRegions[fullDistrictName] || {
                    total: 0,
                    typeA: 0,
                    typeB: 0,
                    typeD: 0
                };

                const districtPercentage = districtStats.total > 0 ? ((districtStats.total / totalStores) * 100).toFixed(1) : "0.0";

                const districtRow = document.createElement('tr');
                districtRow.className = 'subregion-row subregion-benghazi d-none';
                districtRow.innerHTML = `
                    <td>
                        <span class="ms-3">- ${district}</span>
                    </td>
                    <td class="text-center">
                        <button class="btn btn-sm btn-outline-info details-btn" data-region="بنغازي - ${district}">
                            <i class="fas fa-exclamation-circle"></i>
                        </button>
                    </td>
                    <td>${districtStats.total}</td>
                    <td><span class="badge bg-success">${districtStats.typeA}</span></td>
                    <td><span class="badge bg-primary">${districtStats.typeB}</span></td>
                    <td><span class="badge bg-warning text-dark">${districtStats.typeD}</span></td>
                    <td>
                        <div class="progress" style="height: 15px;">
                            <div class="progress-bar" role="progressbar" style="width: ${districtPercentage}%; background: linear-gradient(135deg, #6a11cb, #2575fc);"
                                aria-valuenow="${districtPercentage}" aria-valuemin="0" aria-valuemax="100">
                                ${districtPercentage}%
                            </div>
                        </div>
                    </td>
                `;

                tableBody.appendChild(districtRow);
            });

            // إضافة مستمعي الأحداث لأزرار التبديل
            document.querySelectorAll('.toggle-subregions').forEach(button => {
                button.addEventListener('click', function() {
                    const region = this.getAttribute('data-region');
                    const subregionRows = document.querySelectorAll(`.subregion-${region}`);

                    // تبديل حالة العرض
                    const isHidden = subregionRows[0].classList.contains('d-none');

                    subregionRows.forEach(row => {
                        if (isHidden) {
                            row.classList.remove('d-none');
                        } else {
                            row.classList.add('d-none');
                        }
                    });

                    // تغيير أيقونة السهم
                    if (isHidden) {
                        this.classList.remove('fa-chevron-down');
                        this.classList.add('fa-chevron-up');
                    } else {
                        this.classList.remove('fa-chevron-up');
                        this.classList.add('fa-chevron-down');
                    }
                });
            });

            // إضافة مستمعي الأحداث لأزرار التفاصيل
            document.querySelectorAll('.details-btn').forEach(button => {
                button.addEventListener('click', function() {
                    const region = this.getAttribute('data-region');
                    showRegionDetails(region, window.storesData);
                });
            });
        }

        function createStoresMap(stores) {
            // إنشاء خريطة
            const map = L.map('stats-map', {
                preferCanvas: true,
                zoomControl: true,
                attributionControl: true,
                dragging: true,
                scrollWheelZoom: true,
                doubleClickZoom: true,
                touchZoom: true
            }).setView([32.8872, 13.1913], 6); // مركز ليبيا (طرابلس)

            // إضافة طبقة الخريطة
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
            }).addTo(map);

            // تخزين كائن الخريطة في متغير عام للوصول إليه من أي مكان
            window.statsMapInstance = map;

            // إضافة المتاجر إلى الخريطة
            stores.forEach(store => {
                if (store.latitude && store.longitude) {
                    const region = LibyaRegions.getRegionFromCoordinates(store.latitude, store.longitude);
                    const marker = L.marker([store.latitude, store.longitude], {
                        icon: L.divIcon({
                            className: 'store-marker',
                            html: `<i class="fas fa-map-marker-alt map-marker-alt"></i>`,
                            iconSize: [24, 24],
                            iconAnchor: [12, 24],
                            popupAnchor: [0, -24]
                        })
                    }).addTo(map);

                    marker.bindPopup(`
                        <div class="store-popup">
                            <h6>${store.name}</h6>
                            <p class="text-muted small mb-1">${region}</p>
                            <p class="mb-1">${store.phone || 'لا يوجد رقم هاتف'}</p>
                            <div class="d-flex justify-content-between">
                                <button class="btn btn-sm btn-primary" onclick="window.location.href='/?store=${store.id}'" style="background: linear-gradient(135deg, var(--loacker-red-dark), var(--loacker-red)); border: none;">
                                    <i class="fas fa-eye"></i> عرض التفاصيل
                                </button>
                                <button class="btn btn-sm btn-outline-info" onclick="showRegionDetails('${region}', window.storesData)">
                                    <i class="fas fa-info-circle"></i> المنطقة
                                </button>
                            </div>
                        </div>
                    `);
                }
            });

            // إضافة تجميع المتاجر حسب المنطقة
            const regionMarkers = {};

            // إنشاء علامات للمناطق الرئيسية
            LibyaRegions.regions.forEach(region => {
                const storesInRegion = stores.filter(store => {
                    const storeRegion = LibyaRegions.getRegionFromCoordinates(store.latitude, store.longitude);
                    // تحقق مما إذا كانت المنطقة هي المنطقة الرئيسية أو منطقة فرعية تابعة لها
                    return storeRegion === region.name || storeRegion.startsWith(region.name + ' - ');
                });

                if (storesInRegion.length > 0) {
                    // إنشاء علامة للمنطقة مع عدد المتاجر
                    const marker = L.marker([region.lat, region.lng], {
                        icon: L.divIcon({
                            className: 'region-marker',
                            html: `<div class="region-marker-icon">${storesInRegion.length}</div>`,
                            iconSize: [40, 40],
                            iconAnchor: [20, 20]
                        })
                    }).addTo(map);

                    // إضافة نافذة منبثقة تعرض معلومات المنطقة
                    marker.bindPopup(`
                        <div class="region-popup">
                            <h5>${region.name}</h5>
                            <p>عدد المتاجر: <strong>${storesInRegion.length}</strong></p>
                            <div class="d-flex justify-content-between">
                                <button class="btn btn-sm btn-outline-danger" onclick="filterStoresByRegion('${region.name}')">
                                    <i class="fas fa-filter"></i> تصفية
                                </button>
                                <button class="btn btn-sm btn-outline-info" onclick="showRegionDetails('${region.name}', window.storesData)">
                                    <i class="fas fa-info-circle"></i> تفاصيل
                                </button>
                            </div>
                        </div>
                    `);

                    regionMarkers[region.name] = marker;
                }
            });

            // إضافة علامات للمناطق الفرعية في طرابلس وبنغازي
            const subRegions = [...LibyaRegions.tripoliDistricts, ...LibyaRegions.benghaziDistricts];
            subRegions.forEach(district => {
                const storesInDistrict = stores.filter(store => {
                    const storeRegion = LibyaRegions.getRegionFromCoordinates(store.latitude, store.longitude);
                    return storeRegion === district.name;
                });

                if (storesInDistrict.length > 0) {
                    // إنشاء علامة للمنطقة الفرعية مع عدد المتاجر
                    const marker = L.marker([district.lat, district.lng], {
                        icon: L.divIcon({
                            className: 'district-marker',
                            html: `<div class="district-marker-icon">${storesInDistrict.length}</div>`,
                            iconSize: [30, 30],
                            iconAnchor: [15, 15]
                        })
                    }).addTo(map);

                    // استخراج اسم المنطقة الفرعية
                    const districtName = district.name.split(' - ')[1];

                    // إضافة نافذة منبثقة تعرض معلومات المنطقة الفرعية
                    marker.bindPopup(`
                        <div class="region-popup">
                            <h5>${district.name}</h5>
                            <p>عدد المتاجر: <strong>${storesInDistrict.length}</strong></p>
                            <div class="d-flex justify-content-between">
                                <button class="btn btn-sm btn-outline-primary" onclick="filterStoresByRegion('${district.name}')">
                                    <i class="fas fa-filter"></i> تصفية
                                </button>
                                <button class="btn btn-sm btn-outline-info" onclick="showRegionDetails('${district.name}', window.storesData)">
                                    <i class="fas fa-info-circle"></i> تفاصيل
                                </button>
                            </div>
                        </div>
                    `);
                }
            });

            // إضافة دالة لتصفية المتاجر حسب المنطقة
            window.filterStoresByRegion = function(regionName) {
                // يمكن تنفيذ هذه الدالة لتصفية المتاجر في الصفحة الرئيسية
                console.log(`تصفية المتاجر حسب المنطقة: ${regionName}`);
                // يمكن إضافة رمز لتوجيه المستخدم إلى الصفحة الرئيسية مع تصفية المتاجر
                window.location.href = `/?region=${encodeURIComponent(regionName)}`;
            }
        }
    </script>

    <!-- واجهة الهاتف المحمول -->
    <div class="mobile-view">
        <div class="mobile-header">
            <div class="mobile-logo-wrapper">
                <h1>Loacker</h1>
            </div>
            <div class="mobile-header-actions">
                <a href="{{ url_for('index') }}" class="mobile-header-btn">
                    <i class="fas fa-home"></i>
                </a>
            </div>
        </div>

        <div class="mobile-content" style="padding-top: 70px; padding-bottom: 70px;">
            <div class="p-3">
                <div class="card shadow-sm border-0 rounded-4 mb-4">
                    <div class="card-body text-center p-4">
                        <h2 class="mb-3">
                            <i class="fas fa-chart-pie me-2"></i>
                            إحصائيات المتاجر
                        </h2>
                        <p class="text-muted">تحليل شامل لتوزيع المتاجر حسب المناطق والأنواع</p>
                    </div>
                </div>

                <!-- بطاقات الإحصائيات الرئيسية للجوال -->
                <div class="row g-3 mb-4">
                    <div class="col-4">
                        <div class="stats-card">
                            <div class="stats-header d-flex justify-content-between align-items-center" style="background: linear-gradient(135deg, var(--loacker-red-dark), var(--loacker-red)); padding: 10px;">
                                <h6 class="m-0 small">المتاجر</h6>
                                <i class="fas fa-store" style="font-size: 1.2rem;"></i>
                            </div>
                            <div class="card-body text-center p-2">
                                <h3 class="fw-bold" id="mobile-total-stores-count">0</h3>
                            </div>
                        </div>
                    </div>

                    <div class="col-4">
                        <div class="stats-card">
                            <div class="stats-header green d-flex justify-content-between align-items-center" style="padding: 10px;">
                                <h6 class="m-0 small">المناطق</h6>
                                <i class="fas fa-map-marker-alt" style="font-size: 1.2rem;"></i>
                            </div>
                            <div class="card-body text-center p-2">
                                <h3 class="fw-bold" id="mobile-regions-count">0</h3>
                            </div>
                        </div>
                    </div>

                    <div class="col-4">
                        <div class="stats-card">
                            <div class="stats-header orange d-flex justify-content-between align-items-center" style="padding: 10px;">
                                <h6 class="m-0 small">المتوسط</h6>
                                <i class="fas fa-calculator" style="font-size: 1.2rem;"></i>
                            </div>
                            <div class="card-body text-center p-2">
                                <h3 class="fw-bold" id="mobile-avg-stores-per-region">0</h3>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الرسوم البيانية للجوال -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-tags me-2"></i>
                            توزيع المتاجر حسب النوع
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container" style="height: 250px;">
                            <canvas id="mobileStoreTypeChart"></canvas>
                        </div>
                    </div>
                </div>

                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-map me-2"></i>
                            توزيع المتاجر حسب المنطقة
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container" style="height: 250px;">
                            <canvas id="mobileStoreRegionChart"></canvas>
                        </div>
                    </div>
                </div>

                <!-- خريطة توزيع المتاجر للجوال -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-globe me-2"></i>
                            خريطة توزيع المتاجر
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        <div id="mobile-stats-map" style="height: 300px; border-radius: 0 0 10px 10px;"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- شريط التنقل السفلي للجوال -->
        <div class="mobile-tabs">
            <a href="{{ url_for('index') }}" class="mobile-tab-btn">
                <i class="fas fa-home"></i>
                <span>الرئيسية</span>
            </a>
            <a href="{{ url_for('profile') }}" class="mobile-tab-btn">
                <i class="fas fa-user"></i>
                <span>الملف</span>
            </a>
            <a href="{{ url_for('statistics') }}" class="mobile-tab-btn active">
                <i class="fas fa-chart-pie"></i>
                <span>الإحصائيات</span>
            </a>
            <a href="{{ url_for('admin_panel') }}" class="mobile-tab-btn">
                <i class="fas fa-cog"></i>
                <span>الإدارة</span>
            </a>
        </div>
    </div>

    <!-- كاشف نوع الجهاز - نقله إلى أعلى الصفحة -->
    <script>
        // تعريف دالة مساعدة للتعامل مع أخطاء device-detector
        function handleDeviceDetectorError() {
            console.log("تم التعامل مع خطأ في كاشف الأجهزة");

            // إظهار واجهة سطح المكتب وإخفاء واجهة الجوال في حالة حدوث خطأ
            const containerFluid = document.querySelector('.container-fluid');
            const mobileView = document.querySelector('.mobile-view');

            if (containerFluid) containerFluid.style.display = 'block';
            if (mobileView) mobileView.style.display = 'none';
        }

        // محاولة تحميل كاشف الأجهزة مع التعامل مع الأخطاء
        try {
            // سيتم تحميل كاشف الأجهزة من الملف الخارجي
            console.log("جاري تحميل كاشف الأجهزة...");
        } catch (error) {
            console.error("خطأ في تهيئة كاشف الأجهزة:", error);
            handleDeviceDetectorError();
        }
    </script>
    <script src="{{ url_for('static', filename='js/device-detector.js') }}" onerror="handleDeviceDetectorError()"></script>

    <script>
        // تحديث عدادات الإحصائيات للجوال
        function updateMobileCounters(stores) {
            // تحديث إجمالي عدد المتاجر
            document.getElementById('mobile-total-stores-count').textContent = stores.length;

            // حساب عدد المناطق الفريدة
            const regions = [...new Set(stores.map(store => LibyaRegions.getRegionFromCoordinates(store.latitude, store.longitude)))];
            document.getElementById('mobile-regions-count').textContent = regions.length;

            // حساب متوسط المتاجر لكل منطقة
            const avgStores = regions.length > 0 ? (stores.length / regions.length).toFixed(1) : 0;
            document.getElementById('mobile-avg-stores-per-region').textContent = avgStores;
        }

        // إنشاء الرسوم البيانية للجوال
        function createMobileCharts(storesByType, storesByRegion) {
            // رسم بياني للأنواع
            const typeCtx = document.getElementById('mobileStoreTypeChart').getContext('2d');
            new Chart(typeCtx, {
                type: 'doughnut',
                data: {
                    labels: Object.keys(storesByType),
                    datasets: [{
                        data: Object.values(storesByType),
                        backgroundColor: [
                            '#4CAF50',  // نوع A
                            '#2196F3',  // نوع B
                            '#FF9800',  // نوع D
                            '#9E9E9E'   // أخرى
                        ],
                        borderColor: '#212529',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                font: {
                                    family: 'Tajawal, sans-serif',
                                    size: 12
                                }
                            }
                        }
                    }
                }
            });

            // رسم بياني للمناطق
            const regionCtx = document.getElementById('mobileStoreRegionChart').getContext('2d');
            const regions = Object.keys(storesByRegion);
            const storeCounts = regions.map(region => storesByRegion[region].total);

            new Chart(regionCtx, {
                type: 'bar',
                data: {
                    labels: regions,
                    datasets: [{
                        label: 'عدد المتاجر',
                        data: storeCounts,
                        backgroundColor: 'rgba(213, 0, 0, 0.7)',
                        borderColor: 'rgba(213, 0, 0, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                precision: 0,
                                font: {
                                    size: 10
                                }
                            }
                        },
                        x: {
                            ticks: {
                                font: {
                                    family: 'Tajawal, sans-serif',
                                    size: 10
                                }
                            }
                        }
                    }
                }
            });
        }

        // إنشاء خريطة للجوال
        function createMobileMap(stores) {
            const mapElement = document.getElementById('mobile-stats-map');
            if (!mapElement) {
                console.warn('Mobile stats map element not found');
                return;
            }

            // التحقق مما إذا كانت الخريطة مهيأة بالفعل
            let map;
            try {
                // التحقق من وجود كائن خريطة Leaflet مرتبط بالعنصر
                if (mapElement._leaflet_id) {
                    console.log('Mobile stats map container already has a Leaflet map, reusing existing map');
                    // استخدام الخريطة الموجودة
                    map = L.map._maps[mapElement._leaflet_id];

                    // مسح العلامات الحالية
                    map.eachLayer(layer => {
                        if (layer instanceof L.Marker) {
                            map.removeLayer(layer);
                        }
                    });
                } else {
                    // إنشاء خريطة جديدة
                    console.log('Creating new mobile stats map');
                    map = L.map('mobile-stats-map').setView([32.8872, 13.1913], 6); // مركز ليبيا (طرابلس)

                    // إضافة طبقة الخريطة
                    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                        attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                    }).addTo(map);
                }
            } catch (error) {
                console.error('Error initializing mobile stats map:', error);

                // محاولة إنشاء خريطة جديدة في حالة حدوث خطأ
                try {
                    // إزالة أي خريطة موجودة
                    if (mapElement._leaflet_id) {
                        try {
                            L.map._maps[mapElement._leaflet_id].remove();
                        } catch (e) {
                            console.warn('Error removing existing map:', e);
                        }
                    }

                    // إنشاء خريطة جديدة
                    map = L.map('mobile-stats-map').setView([32.8872, 13.1913], 6);

                    // إضافة طبقة الخريطة
                    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                        attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                    }).addTo(map);
                } catch (e) {
                    console.error('Failed to create mobile stats map:', e);
                    return;
                }
            }

            // إضافة المتاجر إلى الخريطة
            stores.forEach(store => {
                if (store.latitude && store.longitude) {
                    const region = LibyaRegions.getRegionFromCoordinates(store.latitude, store.longitude);
                    const marker = L.marker([store.latitude, store.longitude], {
                        icon: L.divIcon({
                            className: 'store-marker',
                            html: `<i class="fas fa-map-marker-alt map-marker-alt"></i>`,
                            iconSize: [24, 24],
                            iconAnchor: [12, 24],
                            popupAnchor: [0, -24]
                        })
                    }).addTo(map);

                    marker.bindPopup(`
                        <div class="store-popup">
                            <h6>${store.name}</h6>
                            <p class="text-muted small mb-1">${region}</p>
                            <button class="btn btn-sm btn-primary w-100" onclick="window.location.href='/?store=${store.id}'" style="background: linear-gradient(135deg, var(--loacker-red-dark), var(--loacker-red)); border: none;">
                                <i class="fas fa-eye"></i> عرض
                            </button>
                        </div>
                    `);
                }
            });

            // إعادة ضبط حجم الخريطة
            setTimeout(() => {
                map.invalidateSize();
            }, 100);
        }



        // وظيفة تطبيق التصفية
        function applyFilters() {
            // الحصول على قيم التصفية
            const region = document.getElementById('region-filter').value;
            const storeType = document.getElementById('store-type-filter').value;

            // إنشاء كائن التصفية
            const filters = {};

            // إضافة تصفية المنطقة
            if (region !== 'all') {
                filters.region = region;
            }

            // إضافة تصفية نوع المتجر
            if (storeType !== 'all') {
                filters.store_type = storeType;
            }

            // تطبيق التصفية على البيانات
            applyFiltersToData(filters);
        }

        // وظيفة تطبيق التصفية على البيانات باستخدام استخراج البيانات من عنوان المتجر واسم المتجر
        function applyFiltersToData(filters) {
            console.log('تطبيق التصفية:', filters);

            // تصفية البيانات
            let filteredStores = [...window.storesData];

            console.log(`بدء تطبيق التصفية على ${filteredStores.length} متجر...`);

            // تصفية حسب المنطقة
            if (filters.region) {
                filteredStores = filteredStores.filter(store => {
                    // استخراج معلومات العنوان من المتجر
                    const addressInfo = LibyaRegions.parseStoreAddress(store);

                    // إذا لم نحصل على عنوان صالح، حاول استخراجه من اسم المتجر
                    let regionKey = addressInfo.fullAddress;
                    let mainCity = addressInfo.mainCity;

                    // إذا لم نحصل على عنوان من الحقول المخصصة، حاول استخراجه من اسم المتجر
                    if (!regionKey && store.name) {
                        const nameInfo = LibyaRegions.parseAddress(store.name);
                        if (nameInfo.fullAddress) {
                            regionKey = nameInfo.fullAddress;
                            mainCity = nameInfo.mainCity;
                        }
                    }

                    // تجاهل المتاجر التي تحتوي على "المنطقة غير محددة" فقط
                    if (regionKey === 'المنطقة غير محددة') {
                        return false;
                    }

                    // التحقق من تطابق التصفية
                    // إذا كان التصفية هو مدينة رئيسية، تحقق من المدينة الرئيسية
                    if (mainCity === filters.region) {
                        return true;
                    }

                    // إذا كان التصفية هو منطقة كاملة، تحقق من المنطقة الكاملة
                    if (regionKey === filters.region) {
                        return true;
                    }

                    // تحقق من التطابق الجزئي في العنوان
                    if (store.address && store.address.includes(filters.region)) {
                        return true;
                    }

                    return false;
                });
            }

            // تصفية حسب نوع المتجر
            if (filters.store_type) {
                if (filters.store_type === 'other') {
                    filteredStores = filteredStores.filter(store =>
                        !['A', 'B', 'D'].includes(store.type)
                    );
                } else {
                    filteredStores = filteredStores.filter(store =>
                        store.type === filters.store_type
                    );
                }
            }

            console.log(`تم تطبيق التصفية: ${filteredStores.length} متجر متبقي من أصل ${window.storesData.length}`);

            // تحديث العرض بالبيانات المصفاة
            updateDisplayWithFilteredData(filteredStores);

            // إظهار رسالة تأكيد
            const alertDiv = document.createElement('div');
            alertDiv.className = 'alert alert-success alert-dismissible fade show';
            alertDiv.setAttribute('role', 'alert');
            alertDiv.innerHTML = `
                تم تطبيق التصفية. تم العثور على ${filteredStores.length} متجر.
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            `;

            // إضافة التنبيه في أعلى الصفحة
            const container = document.querySelector('.container');
            container.insertBefore(alertDiv, container.firstChild);

            // إزالة التنبيه تلقائيًا بعد 3 ثوانٍ
            setTimeout(() => {
                alertDiv.classList.remove('show');
                setTimeout(() => alertDiv.remove(), 300);
            }, 3000);
        }

        // وظيفة تحديث العرض بالبيانات المصفاة
        function updateDisplayWithFilteredData(filteredStores) {
            // إضافة مؤشر تصفية نشطة
            addActiveFilterIndicator(filteredStores.length);

            // تحديث العدادات
            updateMainCounters(filteredStores);

            // حساب متوسط الأداء
            calculatePerformanceScore(filteredStores);

            // تحليل البيانات المصفاة
            const storesByType = analyzeStoresByTypeNew(filteredStores);
            const storesByRegion = analyzeStoresByRegion(filteredStores);

            // تحديث الرسوم البيانية
            createTypeChart(storesByType);
            createRegionChart(storesByRegion);

            // إنشاء تقرير مفصل للبيانات المصفاة
            const detailedReport = generateDetailedReport(filteredStores);

            // تحديث جدول التفاصيل المحسن
            createDetailTableNew(detailedReport);

            // تحديث تحليل العناوين
            const { addressMap, typeMap } = analyzeStoresByAddressAndType(filteredStores);
            window.addressAnalysis = { addressMap, typeMap };
            createAddressTable(addressMap);
            createTypeAddressTable(typeMap);
            createTypeAddressChart(window.addressAnalysis);

            // تحديث الخريطة
            updateMapWithFilteredStores(filteredStores);

            // تحديث الخريطة الحرارية إذا كانت موجودة
            if (window.heatmapLayer && window.statsMapInstance) {
                updateHeatmapWithFilteredStores(filteredStores);
            }

            // التمرير إلى أعلى الصفحة
            window.scrollTo({ top: 0, behavior: 'smooth' });
        }

        // إضافة مؤشر التصفية النشطة
        function addActiveFilterIndicator(storeCount) {
            // إزالة أي مؤشرات سابقة
            const existingIndicators = document.querySelectorAll('.filter-indicator');
            existingIndicators.forEach(indicator => indicator.remove());

            // إنشاء مؤشر جديد
            const indicator = document.createElement('div');
            indicator.className = 'filter-indicator alert alert-info alert-dismissible fade show';
            indicator.innerHTML = `
                <i class="fas fa-filter me-2"></i>
                <strong>تصفية نشطة:</strong> تم العثور على ${storeCount} متجر.
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close" onclick="resetFilters()"></button>
            `;

            // إضافة المؤشر في أعلى قسم الإحصائيات
            const statsSection = document.querySelector('.statistics-section');
            if (statsSection) {
                statsSection.insertBefore(indicator, statsSection.firstChild);
            }
        }

        // إعادة تعيين التصفية
        function resetFilters() {
            // إعادة تعيين قيم نموذج التصفية
            document.getElementById('region-filter').value = 'all';
            document.getElementById('store-type-filter').value = 'all';

            // تحديث العرض بجميع المتاجر
            updateDisplayWithFilteredData(window.storesData);
        }

        // تحديث الخريطة بالمتاجر المصفاة
        function updateMapWithFilteredStores(filteredStores) {
            if (!window.statsMapInstance) return;

            // إزالة جميع العلامات الحالية
            window.statsMapInstance.eachLayer(layer => {
                if (layer instanceof L.Marker) {
                    window.statsMapInstance.removeLayer(layer);
                }
            });

            // إضافة المتاجر المصفاة فقط
            filteredStores.forEach(store => {
                if (store.latitude && store.longitude) {
                    try {
                        const lat = parseFloat(store.latitude);
                        const lng = parseFloat(store.longitude);

                        if (isNaN(lat) || isNaN(lng)) {
                            console.warn(`Invalid coordinates for store ${store.id || 'unknown'}: ${store.latitude}, ${store.longitude}`);
                            return;
                        }

                        const region = LibyaRegions.getSmartRegion(lat, lng, store.address);
                        const marker = L.marker([lat, lng], {
                            icon: L.divIcon({
                                className: 'store-marker',
                                html: `<i class="fas fa-map-marker-alt map-marker-alt"></i>`,
                                iconSize: [24, 24],
                                iconAnchor: [12, 24],
                                popupAnchor: [0, -24]
                            })
                        }).addTo(window.statsMapInstance);

                        marker.bindPopup(`
                            <div class="store-popup">
                                <h6>${store.name}</h6>
                                <p class="text-muted small mb-1">${region || 'غير محدد'}</p>
                                <p class="text-muted small mb-1">${store.address || 'غير محدد'}</p>
                                <button class="btn btn-sm btn-primary w-100" onclick="window.location.href='/?store=${store.id}'" style="background: linear-gradient(135deg, var(--loacker-red-dark), var(--loacker-red)); border: none;">
                                    <i class="fas fa-eye"></i> عرض
                                </button>
                            </div>
                        `);
                    } catch (error) {
                        console.error(`Error adding marker for store ${store.id || 'unknown'}:`, error);
                    }
                }
            });

            // تحريك الخريطة لتظهر جميع المتاجر المصفاة
            if (filteredStores.length > 0) {
                try {
                    const validStores = filteredStores.filter(store =>
                        store.latitude && store.longitude &&
                        !isNaN(parseFloat(store.latitude)) &&
                        !isNaN(parseFloat(store.longitude))
                    );

                    if (validStores.length > 0) {
                        const bounds = L.latLngBounds(
                            validStores.map(store => [parseFloat(store.latitude), parseFloat(store.longitude)])
                        );
                        window.statsMapInstance.fitBounds(bounds, { padding: [50, 50] });
                    }
                } catch (error) {
                    console.error('Error fitting map to bounds:', error);
                }
            }
        }

        // تحديث الخريطة الحرارية بالمتاجر المصفاة
        function updateHeatmapWithFilteredStores(filteredStores) {
            try {
                console.log("Updating heatmap with filtered stores...");

                // التحقق من وجود الخريطة وطبقة الخريطة الحرارية
                if (!heatmapMap) {
                    console.warn("Heatmap map is not initialized");
                    return;
                }

                // إنشاء بيانات الخريطة الحرارية من المتاجر المصفاة
                const heatmapData = filteredStores
                    .filter(store => store.latitude && store.longitude)
                    .map(store => {
                        try {
                            const lat = parseFloat(store.latitude);
                            const lng = parseFloat(store.longitude);

                            if (isNaN(lat) || isNaN(lng)) {
                                console.warn(`Invalid coordinates for store ${store.id || 'unknown'}: ${store.latitude}, ${store.longitude}`);
                                return null;
                            }

                            const intensity = store.performance_score ? parseFloat(store.performance_score) / 100 : 0.5;
                            return [lat, lng, intensity];
                        } catch (error) {
                            console.error(`Error processing store coordinates: ${error.message}`);
                            return null;
                        }
                    })
                    .filter(item => item !== null); // إزالة العناصر الفارغة

                console.log(`Processed ${heatmapData.length} valid store locations for filtered heatmap`);

                // إزالة طبقة الخريطة الحرارية الحالية إذا كانت موجودة
                if (heatmapLayer) {
                    try {
                        heatmapMap.removeLayer(heatmapLayer);
                    } catch (error) {
                        console.warn("Error removing existing heatmap layer:", error);
                    }
                }

                // التحقق من وجود مكتبة L.heatLayer
                if (typeof L.heatLayer === 'undefined') {
                    console.error("L.heatLayer is not defined. The leaflet-heat.js library may not be loaded correctly.");

                    // محاولة تحميل المكتبة
                    const script = document.createElement('script');
                    script.src = 'https://unpkg.com/leaflet.heat@0.2.0/dist/leaflet-heat.js';
                    script.onload = function() {
                        console.log("leaflet-heat.js loaded successfully");
                        createFilteredHeatmapLayer(heatmapData);
                    };
                    script.onerror = function() {
                        console.error("Failed to load leaflet-heat.js");
                    };
                    document.head.appendChild(script);
                    return;
                }

                createFilteredHeatmapLayer(heatmapData);
            } catch (error) {
                console.error('Error updating heatmap with filtered stores:', error);
            }
        }

        // إنشاء طبقة خريطة حرارية للبيانات المصفاة
        function createFilteredHeatmapLayer(heatmapData) {
            try {
                if (!heatmapData || heatmapData.length === 0) {
                    console.warn("No valid data for filtered heatmap layer");
                    return;
                }

                // التأكد من أن الخريطة مرئية وذات أبعاد غير صفرية
                const heatmapElement = document.getElementById('heatmap');
                if (heatmapElement) {
                    // التأكد من أن عنصر الخريطة مرئي وله أبعاد
                    if (heatmapElement.offsetWidth === 0 || heatmapElement.offsetHeight === 0) {
                        console.warn("Heatmap container has zero width or height. Setting minimum dimensions.");
                        heatmapElement.style.width = '100%';
                        heatmapElement.style.height = '400px';
                        heatmapElement.style.display = 'block';

                        // إعادة ضبط حجم الخريطة
                        if (heatmapMap) {
                            setTimeout(() => {
                                heatmapMap.invalidateSize();
                            }, 100);
                        }
                    }
                }

                // التأكد من تطبيق التصحيح لخاصية willReadFrequently
                if (!L.heatLayer._patchApplied) {
                    console.log("Patching L.heatLayer to use willReadFrequently=true in createFilteredHeatmapLayer");

                    // حفظ الوظيفة الأصلية
                    const originalCreateCanvas = L.DomUtil.create;

                    // استبدال الوظيفة بنسخة معدلة
                    L.DomUtil.create = function(tagName, className, container) {
                        const element = originalCreateCanvas.call(this, tagName, className, container);

                        // إذا كان العنصر هو canvas، قم بتعيين willReadFrequently
                        if (tagName.toLowerCase() === 'canvas') {
                            const context = element.getContext('2d', { willReadFrequently: true });
                            // تخزين سياق معدل في العنصر
                            element._getContext = function() {
                                return context;
                            };
                        }

                        return element;
                    };

                    // تعيين علامة لتجنب تطبيق التصحيح مرة أخرى
                    L.heatLayer._patchApplied = true;
                }

                // إنشاء طبقة خريطة حرارية جديدة
                try {
                    heatmapLayer = L.heatLayer(heatmapData, {
                        radius: currentHeatmapIntensity || 25,
                        blur: 15,
                        maxZoom: 17,
                        minOpacity: 0.05,
                        max: 1.0,
                        gradient: { 0.4: 'blue', 0.6: 'lime', 0.8: 'yellow', 1.0: 'red' }
                    });

                    // إضافة الطبقة إلى الخريطة
                    if (heatmapMap) {
                        // التأكد من أن الخريطة جاهزة قبل إضافة الطبقة
                        setTimeout(() => {
                            try {
                                heatmapMap.addLayer(heatmapLayer);
                                console.log("Filtered heatmap layer added to map");

                                // تحريك الخريطة لتظهر جميع النقاط
                                if (heatmapData.length > 0) {
                                    try {
                                        const bounds = L.latLngBounds(heatmapData.map(point => [point[0], point[1]]));
                                        heatmapMap.fitBounds(bounds, { padding: [50, 50] });
                                    } catch (error) {
                                        console.warn("Error fitting map to bounds:", error);
                                    }
                                }
                            } catch (error) {
                                console.error("Error adding filtered heatmap layer to map:", error);
                            }
                        }, 300);
                    } else {
                        console.warn("Heatmap map is not available");
                    }
                } catch (error) {
                    console.error("Error creating filtered heatmap layer:", error);
                }
            } catch (error) {
                console.error("Error in createFilteredHeatmapLayer:", error);
            }
        }

        // وظيفة تصدير التقرير
        function exportReport(format) {
            // الحصول على قيم التصفية الحالية
            const region = document.getElementById('region-filter').value;
            const storeType = document.getElementById('store-type-filter').value;

            // إنشاء كائن التصفية
            const filters = {};

            // إضافة تصفية المنطقة
            if (region !== 'all') {
                filters.region = region;
            }

            // إضافة تصفية نوع المتجر
            if (storeType !== 'all') {
                filters.store_type = storeType;
            }

            // عرض مؤشر التحميل
            const loadingIndicator = document.createElement('div');
            loadingIndicator.className = 'alert alert-info';
            loadingIndicator.innerHTML = `
                <div class="d-flex align-items-center">
                    <div class="spinner-border spinner-border-sm me-2" role="status">
                        <span class="visually-hidden">جاري التحميل...</span>
                    </div>
                    <span>جاري إنشاء التقرير بتنسيق ${format.toUpperCase()}...</span>
                </div>
            `;
            document.querySelector('.container').insertBefore(loadingIndicator, document.querySelector('.container').firstChild);

            // بناء عنوان URL للتصدير
            let url = `/api/statistics/export?format=${format}`;

            // إضافة معلمات التصفية إلى URL
            const queryParams = new URLSearchParams();
            for (const key in filters) {
                queryParams.append(key, filters[key]);
            }

            if (queryParams.toString()) {
                url += `&${queryParams.toString()}`;
            }

            // إذا كان التنسيق هو PDF، استخدم نافذة طباعة جديدة
            if (format === 'pdf') {
                generatePDFReport(filters, loadingIndicator);
            } else {
                // للتنسيقات الأخرى (Excel، CSV)، استخدم التنزيل المباشر
                fetch(url)
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`فشل في إنشاء التقرير: ${response.status} ${response.statusText}`);
                        }
                        return response.blob();
                    })
                    .then(blob => {
                        // إزالة مؤشر التحميل
                        loadingIndicator.remove();

                        // إنشاء رابط تنزيل
                        const downloadLink = document.createElement('a');
                        downloadLink.href = URL.createObjectURL(blob);
                        downloadLink.download = `تقرير_المتاجر_${new Date().toISOString().split('T')[0]}.${format}`;
                        downloadLink.click();

                        // عرض رسالة نجاح
                        const successAlert = document.createElement('div');
                        successAlert.className = 'alert alert-success alert-dismissible fade show';
                        successAlert.innerHTML = `
                            <i class="fas fa-check-circle me-2"></i>
                            تم إنشاء التقرير بنجاح وبدأ التنزيل.
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        `;
                        document.querySelector('.container').insertBefore(successAlert, document.querySelector('.container').firstChild);

                        // إزالة رسالة النجاح تلقائيًا بعد 3 ثوانٍ
                        setTimeout(() => {
                            successAlert.classList.remove('show');
                            setTimeout(() => successAlert.remove(), 300);
                        }, 3000);
                    })
                    .catch(error => {
                        // إزالة مؤشر التحميل
                        loadingIndicator.remove();

                        // عرض رسالة خطأ
                        console.error('خطأ في إنشاء التقرير:', error);
                        const errorAlert = document.createElement('div');
                        errorAlert.className = 'alert alert-danger alert-dismissible fade show';
                        errorAlert.innerHTML = `
                            <i class="fas fa-exclamation-circle me-2"></i>
                            حدث خطأ أثناء إنشاء التقرير. يرجى المحاولة مرة أخرى.
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        `;
                        document.querySelector('.container').insertBefore(errorAlert, document.querySelector('.container').firstChild);
                    });
            }
        }

        // إنشاء تقرير PDF
        function generatePDFReport(filters, loadingIndicator) {
            // جلب البيانات للتقرير
            fetch('/api/statistics/report?' + new URLSearchParams(filters))
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`فشل في جلب بيانات التقرير: ${response.status} ${response.statusText}`);
                    }
                    return response.json();
                })
                .then(data => {
                    // إزالة مؤشر التحميل
                    loadingIndicator.remove();

                    if (!data.success) {
                        throw new Error('فشل في جلب بيانات التقرير');
                    }

                    // إنشاء نافذة طباعة جديدة
                    const printWindow = window.open('', '_blank');

                    // تحديد عنوان التقرير بناءً على التصفية
                    let reportTitle = 'تقرير إحصائيات المتاجر';
                    if (filters.region) {
                        reportTitle += ` - منطقة ${filters.region}`;
                    }
                    if (filters.store_type) {
                        reportTitle += ` - نوع ${filters.store_type}`;
                    }

                    // الحصول على التاريخ الحالي
                    const now = new Date();
                    const reportDate = now.toLocaleDateString('ar-LY', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit'
                    });

                    // حساب النسب المئوية للمناطق
                    const regionPercentages = {};
                    const regionStats = data.stores_by_region || {};
                    const totalStores = data.summary?.total_stores || 0;

                    Object.entries(regionStats).forEach(([region, count]) => {
                        if (totalStores > 0) {
                            regionPercentages[region] = ((count / totalStores) * 100).toFixed(1);
                        } else {
                            regionPercentages[region] = "0.0";
                        }
                    });

                    // إنشاء محتوى التقرير
                    printWindow.document.write(`
                        <!DOCTYPE html>
                        <html lang="ar" dir="rtl">
                        <head>
                            <meta charset="UTF-8">
                            <meta name="viewport" content="width=device-width, initial-scale=1.0">
                            <title>${reportTitle}</title>
                            <style>
                                body {
                                    font-family: Arial, sans-serif;
                                    direction: rtl;
                                    padding: 20px;
                                    max-width: 1200px;
                                    margin: 0 auto;
                                }
                                .header {
                                    display: flex;
                                    justify-content: space-between;
                                    align-items: center;
                                    margin-bottom: 20px;
                                    border-bottom: 2px solid #d50000;
                                    padding-bottom: 15px;
                                }
                                .logo {
                                    max-width: 150px;
                                    height: auto;
                                }
                                .report-title {
                                    flex-grow: 1;
                                    text-align: center;
                                }
                                h1 {
                                    color: #d50000;
                                    margin-bottom: 5px;
                                }
                                .date {
                                    text-align: left;
                                    color: #666;
                                    font-size: 14px;
                                }
                                h2 {
                                    color: #333;
                                    margin-top: 30px;
                                    border-bottom: 1px solid #ddd;
                                    padding-bottom: 5px;
                                }
                                .report-info {
                                    display: flex;
                                    justify-content: space-between;
                                    margin-bottom: 20px;
                                    border: 1px solid #ddd;
                                    padding: 15px;
                                    border-radius: 5px;
                                    background-color: #f9f9f9;
                                }
                                .report-info div {
                                    text-align: center;
                                    flex: 1;
                                }
                                .report-info h3 {
                                    margin: 5px 0;
                                    color: #333;
                                    font-size: 24px;
                                }
                                .report-info p {
                                    margin: 0;
                                    color: #666;
                                    font-weight: bold;
                                }
                                table {
                                    width: 100%;
                                    border-collapse: collapse;
                                    margin: 20px 0;
                                    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
                                }
                                th, td {
                                    border: 1px solid #ddd;
                                    padding: 12px;
                                    text-align: right;
                                }
                                th {
                                    background-color: #d50000;
                                    color: white;
                                    font-weight: bold;
                                }
                                tr:nth-child(even) {
                                    background-color: #f9f9f9;
                                }
                                tfoot tr {
                                    background-color: #f2f2f2;
                                    font-weight: bold;
                                }
                                .footer {
                                    text-align: center;
                                    margin-top: 30px;
                                    color: #666;
                                    font-size: 12px;
                                    border-top: 1px solid #ddd;
                                    padding-top: 15px;
                                }
                                .loacker-logo {
                                    display: block;
                                    margin: 0 auto;
                                    max-width: 200px;
                                    height: auto;
                                    margin-bottom: 15px;
                                    filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));
                                }
                                @media print {
                                    body {
                                        margin: 0;
                                        padding: 15px;
                                    }
                                    .no-print {
                                        display: none;
                                    }
                                }
                            </style>
                        </head>
                        <body>
                            <div class="header">
                                <img src="/static/images/loacker-logo.svg" alt="Loacker Logo" class="logo">
                                <div class="report-title">
                                    <h1>${reportTitle}</h1>
                                </div>
                                <div class="date">
                                    <p>تاريخ التقرير: ${reportDate}</p>
                                </div>
                            </div>

                            <div style="text-align: center; margin: 20px 0;">
                                <p style="font-size: 16px; color: #666; font-style: italic;">
                                    تقرير مستخرج من نظام إدارة متاجر Loacker
                                </p>
                            </div>

                            <div class="report-info">
                                <div>
                                    <p>التاريخ</p>
                                    <h3>${reportDate.split('،')[0]}</h3>
                                </div>
                                <div>
                                    <p>إجمالي المتاجر</p>
                                    <h3>${data.summary?.total_stores || 0}</h3>
                                </div>
                                <div>
                                    <p>إجمالي المناطق</p>
                                    <h3>${Object.keys(data.stores_by_region || {}).length}</h3>
                                </div>
                            </div>

                            <h2>توزيع المتاجر حسب المنطقة</h2>
                            <table>
                                <thead>
                                    <tr>
                                        <th>المنطقة</th>
                                        <th>عدد المتاجر</th>
                                        <th>النسبة المئوية</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${Object.entries(data.stores_by_region || {}).map(([region, count]) => {
                                        // تحسين عرض اسم المنطقة
                                        let displayRegion = region || 'غير محدد';

                                        // إذا كان اسم المنطقة طويلاً، نعرض فقط الجزء الأول منه
                                        if (displayRegion.length > 30) {
                                            const parts = displayRegion.split(' ');
                                            if (parts.length > 2) {
                                                displayRegion = parts.slice(0, 2).join(' ');
                                            }
                                        }

                                        return `
                                            <tr>
                                                <td>${displayRegion}</td>
                                                <td>${count}</td>
                                                <td>${regionPercentages[region]}%</td>
                                            </tr>
                                        `;
                                    }).join('')}
                                </tbody>
                                <tfoot>
                                    <tr>
                                        <td>المجموع</td>
                                        <td>${data.summary?.total_stores || 0}</td>
                                        <td>100%</td>
                                    </tr>
                                </tfoot>
                            </table>

                            <div class="no-print">
                                <button onclick="window.print();" style="padding: 10px 20px; background-color: #d50000; color: white; border: none; border-radius: 5px; cursor: pointer; margin-top: 20px;">
                                    طباعة التقرير
                                </button>
                            </div>

                            <div class="footer">
                                <img src="/static/images/loacker-logo.svg" alt="Loacker Logo" class="loacker-logo">
                                <p>تم إنشاء هذا التقرير بواسطة نظام إدارة متاجر Loacker</p>
                                <p style="margin-top: 5px; font-size: 12px; color: #888;">
                                    ${reportDate} | جميع الحقوق محفوظة © ${new Date().getFullYear()} Loacker
                                </p>
                            </div>
                        </body>
                        </html>
                    `);

                    printWindow.document.close();

                    // عرض رسالة نجاح
                    const successAlert = document.createElement('div');
                    successAlert.className = 'alert alert-success alert-dismissible fade show';
                    successAlert.innerHTML = `
                        <i class="fas fa-check-circle me-2"></i>
                        تم إنشاء التقرير بنجاح. يرجى استخدام زر الطباعة في النافذة الجديدة لحفظ التقرير كملف PDF.
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    `;
                    document.querySelector('.container').insertBefore(successAlert, document.querySelector('.container').firstChild);
                })
                .catch(error => {
                    // إزالة مؤشر التحميل
                    loadingIndicator.remove();

                    // عرض رسالة خطأ
                    console.error('خطأ في إنشاء تقرير PDF:', error);
                    const errorAlert = document.createElement('div');
                    errorAlert.className = 'alert alert-danger alert-dismissible fade show';
                    errorAlert.innerHTML = `
                        <i class="fas fa-exclamation-circle me-2"></i>
                        حدث خطأ أثناء إنشاء التقرير. يرجى المحاولة مرة أخرى.
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    `;
                    document.querySelector('.container').insertBefore(errorAlert, document.querySelector('.container').firstChild);
                });
        }







        // دالة لتحميل خيارات المناطق وأنواع المتاجر ديناميكياً
        async function loadFilterOptions() {
            try {
                console.log('جاري تحميل خيارات التصفية...');

                // تحميل المناطق والمناطق الفرعية
                const regionsResponse = await fetch('/api/statistics/cities-districts');
                const regionsData = await regionsResponse.json();

                if (regionsData.success) {
                    const regionFilter = document.getElementById('region-filter');

                    // إضافة المدن الرئيسية
                    if (regionsData.cities && regionsData.cities.length > 0) {
                        const mainCitiesGroup = document.createElement('optgroup');
                        mainCitiesGroup.label = "المدن الرئيسية";

                        regionsData.cities.forEach(city => {
                            const option = document.createElement('option');
                            option.value = city;
                            option.textContent = city;
                            mainCitiesGroup.appendChild(option);
                        });

                        regionFilter.appendChild(mainCitiesGroup);
                    }

                    // إضافة المناطق الفرعية لكل مدينة
                    if (regionsData.districts) {
                        Object.entries(regionsData.districts).forEach(([city, districts]) => {
                            if (districts && districts.length > 0) {
                                const districtGroup = document.createElement('optgroup');
                                districtGroup.label = `مناطق ${city} الفرعية`;

                                districts.forEach(district => {
                                    const option = document.createElement('option');
                                    option.value = district;
                                    option.textContent = district;
                                    districtGroup.appendChild(option);
                                });

                                regionFilter.appendChild(districtGroup);
                            }
                        });
                    }

                    console.log('تم تحميل خيارات المناطق بنجاح');
                } else {
                    console.error('فشل في تحميل بيانات المناطق:', regionsData.error);
                }

                // تحميل أنواع المتاجر (يمكن إضافة المزيد من الأنواع إذا لزم الأمر)
                const storeTypeFilter = document.getElementById('store-type-filter');
                // الأنواع موجودة بالفعل في HTML، لكن يمكن تحميلها ديناميكياً إذا تغيرت في المستقبل

                console.log('تم تحميل خيارات التصفية بنجاح');
            } catch (error) {
                console.error('خطأ في تحميل خيارات التصفية:', error);
            }
        }

        // تعديل دالة initStatistics لتدعم النظام الجديد المحسن
        const originalInitStatistics = initStatistics;
        initStatistics = async function() {
            try {
                console.log('بدء تحميل الإحصائيات باستخدام النظام الجديد المحسن...');

                // جلب بيانات المتاجر
                const response = await fetch('/api/stores');
                const data = await response.json();

                if (!data) {
                    throw new Error('فشل في جلب بيانات المتاجر');
                }

                window.storesData = data;
                console.log(`تم جلب ${data.length} متجر من قاعدة البيانات`);

                // تحديث قوائم التصفية بناءً على البيانات الفعلية
                updateFilterOptions(window.storesData);

                // إنشاء تقرير مفصل للإحصائيات
                const detailedReport = generateDetailedReport(window.storesData);
                window.currentReport = detailedReport;

                console.log('ملخص التقرير:', detailedReport.summary);

                // تحديث العدادات الرئيسية باستخدام البيانات الجديدة
                updateMainCountersWithNewData(detailedReport);
                updateMobileCounters(window.storesData);

                // تحليل البيانات حسب النوع والمنطقة باستخدام النظام الجديد
                const storesByType = analyzeStoresByTypeNew(window.storesData);
                const storesByRegion = analyzeStoresByRegion(window.storesData);

                console.log('تحليل الأنواع:', storesByType);
                console.log('تحليل المناطق:', Object.keys(storesByRegion).length, 'منطقة');

                // إنشاء الرسوم البيانية
                createTypeChart(storesByType);
                createRegionChart(storesByRegion);
                createMobileCharts(storesByType, storesByRegion);

                // إنشاء جدول التفاصيل المحسن
                createDetailTableNew(detailedReport);

                // تحليل البيانات حسب العنوان والنوع
                const { addressMap, typeMap } = analyzeStoresByAddressAndType(window.storesData);
                window.addressAnalysis = { addressMap, typeMap };
                createAddressTable(addressMap);
                createTypeAddressTable(typeMap);
                createTypeAddressChart(window.addressAnalysis);

                // إنشاء خريطة توزيع المتاجر
                createStoresMap(window.storesData);
                createMobileMap(window.storesData);

                console.log('تم تحميل جميع الإحصائيات بنجاح باستخدام النظام الجديد');

            } catch (error) {
                console.error('خطأ في تحميل الإحصائيات:', error);
                alert('حدث خطأ أثناء تحميل الإحصائيات. يرجى المحاولة مرة أخرى.');
            }
        };

        /**
         * تحليل المتاجر حسب النوع باستخدام استخراج البيانات من عنوان المتجر واسم المتجر
         * @param {Array} stores - مصفوفة المتاجر
         * @returns {Object} - كائن يحتوي على تجميع المتاجر حسب النوع بالتنسيق المطلوب للرسوم البيانية
         */
        function analyzeStoresByTypeNew(stores) {
            // حساب إجمالي كل نوع من جميع المتاجر
            const typeStats = {
                'نوع A': 0,
                'نوع B': 0,
                'نوع D': 0,
                'أخرى': 0
            };

            if (!stores || !Array.isArray(stores) || stores.length === 0) {
                console.warn("No stores data available for type analysis");
                return typeStats;
            }

            try {
                console.log(`بدء تحليل ${stores.length} متجر حسب النوع...`);

                stores.forEach((store, index) => {
                    // تصنيف حسب نوع المتجر
                    const storeType = store.type ? store.type.toUpperCase() : '';

                    if (storeType === 'A') {
                        typeStats['نوع A']++;
                    } else if (storeType === 'B') {
                        typeStats['نوع B']++;
                    } else if (storeType === 'D') {
                        typeStats['نوع D']++;
                    } else {
                        typeStats['أخرى']++;
                    }
                });

                const totalStores = Object.values(typeStats).reduce((sum, count) => sum + count, 0);
                console.log(`تم تحليل ${totalStores} متجر حسب النوع:`);
                console.log(`- نوع A: ${typeStats['نوع A']} متجر`);
                console.log(`- نوع B: ${typeStats['نوع B']} متجر`);
                console.log(`- نوع D: ${typeStats['نوع D']} متجر`);
                console.log(`- أخرى: ${typeStats['أخرى']} متجر`);

            } catch (error) {
                console.error("Error in analyzeStoresByTypeNew:", error);
                return typeStats;
            }

            console.log('إحصائيات الأنواع الجديدة:', typeStats);
            return typeStats;
        }

        /**
         * تحديث العدادات الرئيسية باستخدام البيانات الجديدة
         * @param {Object} report - التقرير المفصل
         */
        function updateMainCountersWithNewData(report) {
            // تحديث إجمالي المتاجر
            const totalStoresElement = document.getElementById('total-stores-count');
            if (totalStoresElement) {
                totalStoresElement.textContent = report.summary.totalStores;
            }

            // تحديث عدد المناطق
            const regionsCountElement = document.getElementById('regions-count');
            if (regionsCountElement) {
                regionsCountElement.textContent = report.summary.totalCities;
            }

            // تحديث متوسط المتاجر لكل منطقة
            const avgStoresElement = document.getElementById('avg-stores-per-region');
            if (avgStoresElement) {
                const avgStores = report.summary.totalCities > 0 ?
                    Math.round(report.summary.totalStores / report.summary.totalCities) : 0;
                avgStoresElement.textContent = avgStores;
            }

            // تحديث متوسط الأداء (يمكن تحسينه لاحقاً)
            const avgPerformanceElement = document.getElementById('avg-performance-score');
            if (avgPerformanceElement) {
                avgPerformanceElement.textContent = '85'; // قيمة افتراضية
            }
        }

        /**
         * إنشاء جدول التفاصيل المحسن
         * @param {Object} report - التقرير المفصل
         */
        function createDetailTableNew(report) {
            const tableContainer = document.getElementById('region-stats-table');
            if (!tableContainer) {
                console.warn('لم يتم العثور على حاوي جدول التفاصيل');
                // محاولة البحث عن عنصر بديل
                const alternativeContainer = document.querySelector('#region-stats-table, .region-stats-table, [data-table="region-stats"]');
                if (alternativeContainer) {
                    console.log('تم العثور على عنصر بديل لجدول التفاصيل');
                } else {
                    console.error('لم يتم العثور على أي عنصر مناسب لجدول التفاصيل');
                    return;
                }
            }

            // مسح المحتوى الحالي
            tableContainer.innerHTML = '';

            // ترتيب المناطق حسب عدد المتاجر (تنازلي)
            const sortedRegions = Object.entries(report.regions).sort((a, b) => b[1].totalStores - a[1].totalStores);

            // إضافة صفوف البيانات
            sortedRegions.forEach(([regionName, regionData]) => {
                const percentage = report.summary.totalStores > 0 ?
                    ((regionData.totalStores / report.summary.totalStores) * 100).toFixed(1) : '0.0';

                const row = document.createElement('tr');
                row.innerHTML = `
                    <td><strong>${regionName}</strong></td>
                    <td class="text-center">
                        <button class="btn btn-sm btn-outline-info details-btn" data-region="${regionName}">
                            <i class="fas fa-info-circle"></i>
                        </button>
                    </td>
                    <td><span class="badge bg-primary">${regionData.totalStores}</span></td>
                    <td><span class="badge bg-success">${regionData.storeTypes.A}</span></td>
                    <td><span class="badge bg-info">${regionData.storeTypes.B}</span></td>
                    <td><span class="badge bg-warning">${regionData.storeTypes.D}</span></td>
                    <td>
                        <div class="progress" style="height: 20px;">
                            <div class="progress-bar" role="progressbar" style="width: ${percentage}%; background: linear-gradient(135deg, var(--loacker-red-dark), var(--loacker-red));"
                                aria-valuenow="${percentage}" aria-valuemin="0" aria-valuemax="100">
                                ${percentage}%
                            </div>
                        </div>
                    </td>
                `;
                tableContainer.appendChild(row);
            });

            // إضافة مستمعي الأحداث لأزرار التفاصيل
            document.querySelectorAll('.details-btn').forEach(button => {
                button.addEventListener('click', function() {
                    const region = this.getAttribute('data-region');
                    console.log(`عرض تفاصيل المنطقة: ${region}`);
                    showRegionDetails(region, window.storesData);
                });
            });
        }

        // دالة عرض تفاصيل المنطقة
        function showRegionDetails(regionName, stores) {
            console.log(`عرض تفاصيل المنطقة: ${regionName}`);
            console.log(`عدد المتاجر المتاحة: ${stores ? stores.length : 0}`);

            if (!stores || stores.length === 0) {
                alert('لا توجد بيانات متاجر متاحة');
                return;
            }

            // تصفية المتاجر حسب المنطقة المحددة
            let storesInRegion = [];
            let regionStats = {
                total: 0,
                typeA: 0,
                typeB: 0,
                typeD: 0
            };

            // معالجة حالة الإجمالي
            if (regionName === "الإجمالي") {
                storesInRegion = stores;
                regionStats = {
                    total: stores.length,
                    typeA: stores.filter(store => store.type === 'A').length,
                    typeB: stores.filter(store => store.type === 'B').length,
                    typeD: stores.filter(store => store.type === 'D').length
                };
            } else {
                // تصفية المتاجر حسب المنطقة المحددة
                storesInRegion = stores.filter(store => {
                    // البحث المباشر في العنوان
                    if (store.address && store.address.includes(regionName)) {
                        return true;
                    }

                    // إذا كانت المنطقة تحتوي على " - " فهي منطقة فرعية
                    if (regionName.includes(' - ')) {
                        const [mainCity, subRegion] = regionName.split(' - ');

                        // البحث عن المدينة الرئيسية والمنطقة الفرعية
                        if (store.address) {
                            // البحث عن المدينة الرئيسية والمنطقة الفرعية في العنوان
                            const addressLower = store.address.toLowerCase();
                            const mainCityLower = mainCity.toLowerCase();
                            const subRegionLower = subRegion.toLowerCase();

                            // تحقق من وجود كلا الجزأين في العنوان
                            if (addressLower.includes(mainCityLower) && addressLower.includes(subRegionLower)) {
                                return true;
                            }

                            // تحقق من وجود المنطقة الفرعية فقط
                            if (addressLower.includes(subRegionLower)) {
                                return true;
                            }

                            // تحقق من التطابق العكسي (المنطقة - المدينة)
                            if (addressLower.includes(`${subRegionLower} - ${mainCityLower}`) ||
                                addressLower.includes(`${subRegionLower}-${mainCityLower}`)) {
                                return true;
                            }
                        }

                        // البحث في حقول city_name و region_name إذا كانت متوفرة
                        if (store.city_name && store.region_name) {
                            const cityLower = store.city_name.toLowerCase();
                            const regionLower = store.region_name.toLowerCase();
                            const mainCityLower = mainCity.toLowerCase();
                            const subRegionLower = subRegion.toLowerCase();

                            if (cityLower === mainCityLower && regionLower === subRegionLower) {
                                return true;
                            }
                        }
                    } else {
                        // البحث عن المدينة الرئيسية فقط
                        if (store.address) {
                            const addressLower = store.address.toLowerCase();
                            const regionLower = regionName.toLowerCase();

                            if (addressLower.includes(regionLower)) {
                                return true;
                            }
                        }

                        // البحث في city_name
                        if (store.city_name && store.city_name.toLowerCase() === regionName.toLowerCase()) {
                            return true;
                        }
                    }

                    // البحث في اسم المتجر كخيار أخير
                    if (store.name && store.name.includes(regionName)) {
                        return true;
                    }

                    return false;
                });

                // حساب الإحصائيات للمنطقة
                regionStats = {
                    total: storesInRegion.length,
                    typeA: storesInRegion.filter(store => store.type === 'A').length,
                    typeB: storesInRegion.filter(store => store.type === 'B').length,
                    typeD: storesInRegion.filter(store => store.type === 'D').length
                };

                console.log(`تم العثور على ${storesInRegion.length} متجر في منطقة ${regionName}`);
                console.log('إحصائيات المنطقة:', regionStats);

                // إضافة معلومات تشخيصية
                if (storesInRegion.length === 0) {
                    console.log('تشخيص البحث:');
                    console.log(`اسم المنطقة المطلوبة: "${regionName}"`);

                    // عرض عينة من عناوين المتاجر المتاحة
                    const sampleAddresses = stores.slice(0, 5).map(store => ({
                        name: store.name,
                        address: store.address,
                        city_name: store.city_name,
                        region_name: store.region_name
                    }));
                    console.log('عينة من عناوين المتاجر المتاحة:', sampleAddresses);

                    alert(`لا توجد متاجر في منطقة ${regionName}\nتحقق من وحدة التحكم للمزيد من التفاصيل`);
                    return;
                }
            }

            // إنشاء نافذة منبثقة لعرض التفاصيل
            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.id = 'regionDetailsModal';
            modal.setAttribute('tabindex', '-1');
            modal.setAttribute('aria-labelledby', 'regionDetailsModalLabel');
            modal.setAttribute('aria-hidden', 'true');

            // محتوى النافذة المنبثقة
            modal.innerHTML = `
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header bg-gradient" style="background: linear-gradient(135deg, #d50000, #ff5722); color: white;">
                            <h5 class="modal-title" id="regionDetailsModalLabel">
                                <i class="fas fa-map-marker-alt me-2"></i>تفاصيل منطقة: ${regionName}
                            </h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body p-3">
                            <!-- الصف الأول: الإحصائيات والخريطة -->
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <div class="card h-100 shadow-sm border-0" style="background: #343a40;">
                                        <div class="card-header text-white text-center" style="background: #d50000;">
                                            <h6 class="mb-0"><i class="fas fa-chart-bar me-2"></i>إحصائيات المتاجر</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="row text-center">
                                                <div class="col-6 mb-2">
                                                    <div class="p-2 rounded" style="background: linear-gradient(135deg, #007bff, #0056b3);">
                                                        <h4 class="text-white mb-0">${regionStats.total}</h4>
                                                        <small class="text-white">إجمالي المتاجر</small>
                                                    </div>
                                                </div>
                                                <div class="col-6 mb-2">
                                                    <div class="p-2 rounded" style="background: linear-gradient(135deg, #28a745, #1e7e34);">
                                                        <h4 class="text-white mb-0">${regionStats.typeA}</h4>
                                                        <small class="text-white">نوع A</small>
                                                    </div>
                                                </div>
                                                <div class="col-6">
                                                    <div class="p-2 rounded" style="background: linear-gradient(135deg, #17a2b8, #117a8b);">
                                                        <h4 class="text-white mb-0">${regionStats.typeB}</h4>
                                                        <small class="text-white">نوع B</small>
                                                    </div>
                                                </div>
                                                <div class="col-6">
                                                    <div class="p-2 rounded" style="background: linear-gradient(135deg, #ffc107, #e0a800);">
                                                        <h4 class="text-dark mb-0">${regionStats.typeD}</h4>
                                                        <small class="text-dark">نوع D</small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card h-100 shadow-sm border-0" style="background: #343a40;">
                                        <div class="card-header text-white text-center" style="background: #d50000;">
                                            <h6 class="mb-0"><i class="fas fa-map me-2"></i>خريطة المتاجر</h6>
                                        </div>
                                        <div class="card-body p-0">
                                            <div id="region-mini-map" style="height: 280px; width: 100%; border-radius: 0 0 0.375rem 0.375rem;"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- الصف الثاني: توزيع المتاجر حسب النوع -->
                            <div class="row mb-2">
                                <div class="col-md-7 mx-auto">
                                    <div class="card border-0" style="background: #343a40;">
                                        <div class="card-body p-2">
                                            <h6 class="text-center mb-2" style="color: #ffffff; font-size: 14px;">
                                                <i class="fas fa-chart-pie me-1"></i>توزيع المتاجر حسب النوع
                                            </h6>
                                            <div style="height: 120px; display: flex; justify-content: center; align-items: center;">
                                                <canvas id="region-type-chart" style="max-height: 100px; width: 100%;"></canvas>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- الصف الثالث: قائمة المتاجر -->
                            <div class="row">
                                <div class="col-12">
                                    <div class="card shadow-sm border-0" style="background: #343a40;">
                                        <div class="card-header text-white text-center" style="background: #d50000;">
                                            <h6 class="mb-0"><i class="fas fa-list me-2"></i>قائمة المتاجر في المنطقة (${regionStats.total} متجر)</h6>
                                        </div>
                                        <div class="card-body p-0">
                                            <div class="table-responsive">
                                                <table class="table table-hover table-sm mb-0" style="color: #ffffff;">
                                                    <thead style="background: #495057; color: #ffffff;">
                                                        <tr>
                                                            <th class="text-center">#</th>
                                                            <th>اسم المتجر</th>
                                                            <th class="text-center">النوع</th>
                                                            <th>العنوان</th>
                                                            <th class="text-center">رقم الهاتف</th>
                                                            <th class="text-center">الإجراءات</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody id="region-stores-list">
                                                        <!-- سيتم ملؤها بواسطة JavaScript -->
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                        </div>
                    </div>
                </div>
            `;

            // إضافة النافذة المنبثقة إلى الصفحة
            document.body.appendChild(modal);

            // إنشاء كائن النافذة المنبثقة
            const modalInstance = new bootstrap.Modal(document.getElementById('regionDetailsModal'));

            // عرض النافذة المنبثقة
            modalInstance.show();

            // ملء جدول المتاجر
            const storesList = document.getElementById('region-stores-list');
            if (storesList) {
                storesList.innerHTML = '';

                storesInRegion.forEach((store, index) => {
                    const row = document.createElement('tr');

                    // تحديد لون الشارة حسب نوع المتجر
                    let badgeClass = 'bg-secondary';
                    if (store.type === 'A') badgeClass = 'bg-success';
                    else if (store.type === 'B') badgeClass = 'bg-primary';
                    else if (store.type === 'D') badgeClass = 'bg-warning text-dark';

                    row.innerHTML = `
                        <td class="text-center"><strong>${index + 1}</strong></td>
                        <td>
                            <div class="d-flex align-items-center">
                                <i class="fas fa-store text-danger me-2"></i>
                                <span>${store.name || 'غير محدد'}</span>
                            </div>
                        </td>
                        <td class="text-center">
                            <span class="badge ${badgeClass}">${store.type || 'غير محدد'}</span>
                        </td>
                        <td>
                            <small class="text-muted">
                                <i class="fas fa-map-marker-alt text-danger me-1"></i>
                                ${store.address || regionName}
                            </small>
                        </td>
                        <td class="text-center">
                            <small class="text-muted">
                                <i class="fas fa-phone text-success me-1"></i>
                                ${store.phone || 'غير متوفر'}
                            </small>
                        </td>
                        <td class="text-center">
                            <button class="btn btn-sm btn-outline-danger" onclick="focusOnStore(${store.latitude}, ${store.longitude}, '${store.name}')" title="التركيز على الخريطة">
                                <i class="fas fa-crosshairs"></i>
                            </button>
                        </td>
                    `;
                    storesList.appendChild(row);
                });

                console.log(`تم إضافة ${storesInRegion.length} متجر إلى الجدول`);
            } else {
                console.error('لم يتم العثور على عنصر جدول المتاجر (region-stores-list)');
            }

            // إنشاء الرسم البياني المصغر
            setTimeout(() => {
                const ctx = document.getElementById('region-type-chart');
                if (ctx) {
                    new Chart(ctx.getContext('2d'), {
                        type: 'doughnut',
                        data: {
                            labels: ['نوع A', 'نوع B', 'نوع D'],
                            datasets: [{
                                data: [regionStats.typeA, regionStats.typeB, regionStats.typeD],
                                backgroundColor: ['#28a745', '#007bff', '#ffc107'],
                                borderWidth: 2,
                                borderColor: '#fff'
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                legend: {
                                    position: 'bottom',
                                    labels: {
                                        font: {
                                            size: 10
                                        },
                                        padding: 5,
                                        usePointStyle: true,
                                        boxWidth: 12,
                                        color: '#ffffff'
                                    }
                                }
                            },
                            cutout: '50%'
                        }
                    });
                }

                // إنشاء الخريطة المصغرة فوراً
                const mapContainer = document.getElementById('region-mini-map');
                if (mapContainer && storesInRegion.length > 0) {
                    // تنظيف الحاوية أولاً
                    mapContainer.innerHTML = '';

                    // تصفية المتاجر التي لها إحداثيات صالحة
                    const storesWithCoords = storesInRegion.filter(store =>
                        store.latitude && store.longitude &&
                        !isNaN(parseFloat(store.latitude)) && !isNaN(parseFloat(store.longitude))
                    );

                    if (storesWithCoords.length > 0) {
                        // حساب المركز والحدود
                        const lats = storesWithCoords.map(store => parseFloat(store.latitude));
                        const lngs = storesWithCoords.map(store => parseFloat(store.longitude));

                        const centerLat = lats.reduce((a, b) => a + b, 0) / lats.length;
                        const centerLng = lngs.reduce((a, b) => a + b, 0) / lngs.length;

                        const minLat = Math.min(...lats);
                        const maxLat = Math.max(...lats);
                        const minLng = Math.min(...lngs);
                        const maxLng = Math.max(...lngs);

                        // إنشاء الخريطة مع إعدادات محسنة
                        const miniMap = L.map('region-mini-map', {
                            zoomControl: true,
                            scrollWheelZoom: true,
                            doubleClickZoom: true,
                            boxZoom: true,
                            keyboard: true,
                            dragging: true,
                            touchZoom: true,
                            preferCanvas: false
                        }).setView([centerLat, centerLng], 12);

                        // حفظ مرجع الخريطة للاستخدام في دالة التركيز
                        window.currentMiniMap = miniMap;

                        // إضافة طبقة الخريطة مع إعدادات محسنة
                        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                            attribution: '© OpenStreetMap contributors',
                            maxZoom: 19,
                            minZoom: 8,
                            detectRetina: true
                        }).addTo(miniMap);

                        // إضافة علامات المتاجر بدبابيس حمراء
                        storesWithCoords.forEach(store => {
                            const lat = parseFloat(store.latitude);
                            const lng = parseFloat(store.longitude);

                            // إنشاء دبوس أحمر مخصص
                            const redIcon = L.icon({
                                iconUrl: 'data:image/svg+xml;base64,' + btoa(`
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 36" width="24" height="36">
                                        <path fill="#d50000" stroke="#ffffff" stroke-width="2" d="M12 0C7.6 0 4 3.6 4 8c0 5.4 8 28 8 28s8-22.6 8-28c0-4.4-3.6-8-8-8z"/>
                                        <circle fill="#ffffff" cx="12" cy="8" r="3"/>
                                    </svg>
                                `),
                                iconSize: [24, 36],
                                iconAnchor: [12, 36],
                                popupAnchor: [0, -36]
                            });

                            // إضافة العلامة مع نافذة معلومات محسنة
                            const marker = L.marker([lat, lng], { icon: redIcon }).addTo(miniMap);

                            // تحديد لون الشارة حسب نوع المتجر
                            let badgeColor = '#dc3545'; // أحمر افتراضي
                            if (store.type === 'A') badgeColor = '#28a745'; // أخضر
                            else if (store.type === 'B') badgeColor = '#007bff'; // أزرق
                            else if (store.type === 'D') badgeColor = '#ffc107'; // أصفر

                            marker.bindPopup(
                                '<div style="text-align: right; direction: rtl; min-width: 200px;">' +
                                    '<div class="text-center mb-2">' +
                                        '<h6 class="mb-1" style="color: #d50000;">' + (store.name || 'غير محدد') + '</h6>' +
                                        '<span class="badge" style="background-color: ' + badgeColor + '; color: white; font-size: 12px;">نوع ' + store.type + '</span>' +
                                    '</div>' +
                                    '<hr style="margin: 8px 0;">' +
                                    '<div style="font-size: 13px;">' +
                                        '<div class="mb-1"><i class="fas fa-map-marker-alt" style="color: #d50000; width: 15px;"></i> ' + (store.address || 'عنوان غير محدد') + '</div>' +
                                        '<div><i class="fas fa-phone" style="color: #d50000; width: 15px;"></i> ' + (store.phone || 'غير متوفر') + '</div>' +
                                    '</div>' +
                                '</div>',
                                {
                                    maxWidth: 250,
                                    className: 'custom-popup'
                                }
                            );
                        });

                        // إجبار الخريطة على إعادة تحديد الحجم
                        setTimeout(() => {
                            miniMap.invalidateSize();

                            // تعديل حدود الخريطة لتشمل جميع المتاجر
                            if (storesWithCoords.length > 1) {
                                const bounds = L.latLngBounds(
                                    [minLat, minLng],
                                    [maxLat, maxLng]
                                );
                                miniMap.fitBounds(bounds, {
                                    padding: [20, 20],
                                    maxZoom: 15
                                });
                            } else {
                                // إذا كان هناك متجر واحد فقط، اضبط التكبير على مستوى مناسب
                                miniMap.setView([centerLat, centerLng], 14);
                            }
                        }, 200);

                        console.log('تم إنشاء خريطة مصغرة مع ' + storesWithCoords.length + ' متجر');
                    } else {
                        // عرض رسالة في حالة عدم وجود إحداثيات
                        mapContainer.innerHTML =
                            '<div class="d-flex align-items-center justify-content-center h-100 text-muted">' +
                                '<div class="text-center">' +
                                    '<i class="fas fa-map-marker-alt fa-3x mb-2"></i>' +
                                    '<p>لا توجد إحداثيات متاحة للمتاجر</p>' +
                                '</div>' +
                            '</div>';
                    }
                } else if (mapContainer) {
                    // عرض رسالة في حالة عدم وجود متاجر
                    mapContainer.innerHTML =
                        '<div class="d-flex align-items-center justify-content-center h-100 text-muted">' +
                            '<div class="text-center">' +
                                '<i class="fas fa-store fa-3x mb-2"></i>' +
                                '<p>لا توجد متاجر لعرضها</p>' +
                            '</div>' +
                        '</div>';
                }
            }, 100);

            // إضافة دالة التركيز على المتجر في الخريطة
            window.focusOnStore = function(lat, lng, storeName) {
                if (window.currentMiniMap && lat && lng) {
                    window.currentMiniMap.setView([parseFloat(lat), parseFloat(lng)], 16);

                    // إضافة تأثير بصري مؤقت
                    const tempMarker = L.circleMarker([parseFloat(lat), parseFloat(lng)], {
                        color: '#d50000',
                        fillColor: '#ff5722',
                        fillOpacity: 0.5,
                        radius: 20
                    }).addTo(window.currentMiniMap);

                    // إزالة التأثير بعد 3 ثوان
                    setTimeout(() => {
                        window.currentMiniMap.removeLayer(tempMarker);
                    }, 3000);
                }
            };



            // تنظيف عند إغلاق النافذة المنبثقة
            modal.addEventListener('hidden.bs.modal', function() {
                // تنظيف مرجع الخريطة
                if (window.currentMiniMap) {
                    window.currentMiniMap = null;
                }
                if (window.focusOnStore) {
                    delete window.focusOnStore;
                }
                document.body.removeChild(modal);
            });
        }
    </script>
</body>
</html>