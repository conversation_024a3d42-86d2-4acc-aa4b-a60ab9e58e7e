import os
import sqlite3
from flask import g, current_app

# تكوين قاعدة البيانات
DATABASE = 'database.db'

def get_db():
    """الحصول على اتصال قاعدة البيانات"""
    db = getattr(g, '_database', None)
    if db is None:
        db = g._database = sqlite3.connect(DATABASE)
        db.row_factory = sqlite3.Row  # للحصول على النتائج كقواميس
    return db

def close_db(exception):
    """إغلاق اتصال قاعدة البيانات"""
    db = getattr(g, '_database', None)
    if db is not None:
        db.close()

def init_db(app):
    """تهيئة قاعدة البيانات"""
    # التأكد من وجود مجلد التحميلات
    upload_folder = app.config['UPLOAD_FOLDER']
    if not os.path.exists(upload_folder):
        os.makedirs(upload_folder)

    # إنشاء جدول المتاجر إذا لم يكن موجودًا
    with app.app_context():
        db = get_db()
        cursor = db.cursor()

        # التحقق من وجود جدول المتاجر
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='stores'")
        table_exists = cursor.fetchone()

        if not table_exists:
            # إنشاء جدول جديد مع حقل القائمة
            cursor.execute('''
            CREATE TABLE stores (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                phone TEXT,
                latitude REAL NOT NULL,
                longitude REAL NOT NULL,
                image_path TEXT,
                list_id INTEGER DEFAULT 1,
                type TEXT DEFAULT 'A',
                performance_score REAL DEFAULT 0,
                address TEXT DEFAULT 'غير محدد',
                full_address TEXT DEFAULT '',
                created_at TEXT,
                updated_at TEXT
            )
            ''')
        else:
            # التحقق من وجود الأعمدة المطلوبة
            cursor.execute("PRAGMA table_info(stores)")
            columns = cursor.fetchall()
            column_names = [column[1] for column in columns]

            # التحقق من وجود عمود list_id
            if 'list_id' not in column_names:
                # إضافة عمود list_id إلى الجدول الحالي
                cursor.execute("ALTER TABLE stores ADD COLUMN list_id INTEGER DEFAULT 1")

            # التحقق من وجود عمود type
            if 'type' not in column_names:
                # إضافة عمود type إلى الجدول الحالي
                cursor.execute("ALTER TABLE stores ADD COLUMN type TEXT DEFAULT 'A'")

            # التحقق من وجود عمود performance_score
            if 'performance_score' not in column_names:
                # إضافة عمود performance_score إلى الجدول الحالي
                cursor.execute("ALTER TABLE stores ADD COLUMN performance_score REAL DEFAULT 0")

            # التحقق من وجود عمود address
            if 'address' not in column_names:
                # إضافة عمود address إلى الجدول الحالي
                cursor.execute("ALTER TABLE stores ADD COLUMN address TEXT DEFAULT 'غير محدد'")

            # التحقق من وجود عمود city_name
            if 'city_name' not in column_names:
                # إضافة عمود city_name إلى الجدول الحالي
                cursor.execute("ALTER TABLE stores ADD COLUMN city_name TEXT DEFAULT ''")

            # التحقق من وجود عمود region_name
            if 'region_name' not in column_names:
                # إضافة عمود region_name إلى الجدول الحالي
                cursor.execute("ALTER TABLE stores ADD COLUMN region_name TEXT DEFAULT ''")

            # التحقق من وجود عمود city_id
            if 'city_id' not in column_names:
                # إضافة عمود city_id إلى الجدول الحالي
                cursor.execute("ALTER TABLE stores ADD COLUMN city_id INTEGER")

            # التحقق من وجود عمود region_id
            if 'region_id' not in column_names:
                # إضافة عمود region_id إلى الجدول الحالي
                cursor.execute("ALTER TABLE stores ADD COLUMN region_id INTEGER")

            # التحقق من وجود عمود full_address
            if 'full_address' not in column_names:
                # إضافة عمود full_address إلى الجدول الحالي
                cursor.execute("ALTER TABLE stores ADD COLUMN full_address TEXT DEFAULT ''")

        # التحقق من وجود جدول الأدوار
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='roles'")
        roles_table_exists = cursor.fetchone()

        if not roles_table_exists:
            # إنشاء جدول الأدوار
            cursor.execute('''
            CREATE TABLE roles (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                description TEXT
            )
            ''')

            # إضافة الأدوار الافتراضية
            cursor.execute("INSERT INTO roles (id, name, description) VALUES (1, 'admin', 'مدير')")
            cursor.execute("INSERT INTO roles (id, name, description) VALUES (2, 'marketer', 'مسوّق')")
            cursor.execute("INSERT INTO roles (id, name, description) VALUES (3, 'visitor', 'زائر')")

        # التحقق من وجود جدول المستخدمين
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='users'")
        users_table_exists = cursor.fetchone()

        if not users_table_exists:
            # إنشاء جدول المستخدمين الجديد مع حقل رقم الهاتف
            cursor.execute('''
            CREATE TABLE users (
                id TEXT PRIMARY KEY,
                username TEXT NOT NULL UNIQUE,
                email TEXT,
                phone TEXT,
                password_hash TEXT NOT NULL,
                role_id INTEGER NOT NULL,
                is_active INTEGER DEFAULT 1,
                created_at TEXT,
                updated_at TEXT,
                FOREIGN KEY (role_id) REFERENCES roles (id)
            )
            ''')

            # إضافة مستخدم المدير الافتراضي
            from werkzeug.security import generate_password_hash
            from datetime import datetime
            import uuid
            now = datetime.now().isoformat()
            user_id = f"user-{uuid.uuid4().hex[:8]}"  # إنشاء معرف فريد بتنسيق مناسب
            admin_password_hash = generate_password_hash('admin123')
            cursor.execute('''
            INSERT INTO users (id, username, email, phone, password_hash, role_id, is_active, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (user_id, 'admin', '<EMAIL>', '************', admin_password_hash, 1, 1, now, now))
        else:
            # التحقق من وجود عمود phone
            cursor.execute("PRAGMA table_info(users)")
            columns = cursor.fetchall()
            has_phone = any(column[1] == 'phone' for column in columns)

            if not has_phone:
                # إضافة عمود phone إلى الجدول الحالي
                cursor.execute("ALTER TABLE users ADD COLUMN phone TEXT")
                # تحديث المستخدمين الحاليين برقم هاتف افتراضي
                cursor.execute("UPDATE users SET phone = '************' WHERE phone IS NULL")

            # التحقق من نوع عمود id
            cursor.execute("PRAGMA table_info(users)")
            columns = cursor.fetchall()
            id_type = next((column[2] for column in columns if column[1] == 'id'), None)

            if id_type == 'INTEGER':
                # إنشاء جدول مؤقت لتحويل البيانات
                cursor.execute("CREATE TABLE users_new AS SELECT * FROM users")
                cursor.execute("DROP TABLE users")

                # إنشاء جدول جديد بالهيكل المطلوب
                cursor.execute('''
                CREATE TABLE users (
                    id TEXT PRIMARY KEY,
                    username TEXT NOT NULL UNIQUE,
                    email TEXT,
                    phone TEXT,
                    password_hash TEXT NOT NULL,
                    role_id INTEGER NOT NULL,
                    is_active INTEGER DEFAULT 1,
                    created_at TEXT,
                    updated_at TEXT,
                    FOREIGN KEY (role_id) REFERENCES roles (id)
                )
                ''')

                # نقل البيانات مع تحويل معرفات المستخدمين
                import uuid
                cursor.execute("SELECT * FROM users_new")
                old_users = cursor.fetchall()

                for old_user in old_users:
                    user_id = f"user-{uuid.uuid4().hex[:8]}"  # إنشاء معرف فريد بتنسيق مناسب
                    cursor.execute('''
                    INSERT INTO users (id, username, email, phone, password_hash, role_id, is_active, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (user_id, old_user['username'], old_user['email'], old_user.get('phone', '************'),
                           old_user['password_hash'], old_user['role_id'], old_user['is_active'],
                           old_user['created_at'], old_user['updated_at']))

                # حذف الجدول المؤقت
                cursor.execute("DROP TABLE users_new")

        # التحقق من وجود جدول القوائم المخصصة
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='lists'")
        lists_table_exists = cursor.fetchone()

        if not lists_table_exists:
            # إنشاء جدول القوائم المخصصة
            cursor.execute('''
            CREATE TABLE lists (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                description TEXT,
                created_at TEXT,
                updated_at TEXT
            )
            ''')

            # إضافة القوائم الافتراضية
            from datetime import datetime
            now = datetime.now().isoformat()
            cursor.execute("INSERT INTO lists (name, description, created_at, updated_at) VALUES (?, ?, ?, ?)",
                          ('القائمة الافتراضية', 'القائمة الافتراضية للمتاجر', now, now))
            cursor.execute("INSERT INTO lists (name, description, created_at, updated_at) VALUES (?, ?, ?, ?)",
                          ('متاجر الرياض', 'متاجر منطقة الرياض', now, now))
            cursor.execute("INSERT INTO lists (name, description, created_at, updated_at) VALUES (?, ?, ?, ?)",
                          ('متاجر جدة', 'متاجر منطقة جدة', now, now))

        # التحقق من وجود جدول ربط المسوقين بالقوائم
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='marketer_lists'")
        marketer_lists_table_exists = cursor.fetchone()

        if not marketer_lists_table_exists:
            # إنشاء جدول ربط المسوقين بالقوائم
            cursor.execute('''
            CREATE TABLE marketer_lists (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id TEXT NOT NULL,
                list_id INTEGER NOT NULL,
                created_at TEXT,
                FOREIGN KEY (user_id) REFERENCES users (id),
                FOREIGN KEY (list_id) REFERENCES lists (id)
            )
            ''')

        # التحقق من وجود جدول المتاجر المعلقة
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='pending_stores'")
        pending_stores_table_exists = cursor.fetchone()

        if pending_stores_table_exists:
            # التحقق من وجود الأعمدة المطلوبة في جدول المتاجر المعلقة
            cursor.execute("PRAGMA table_info(pending_stores)")
            columns = cursor.fetchall()
            column_names = [column[1] for column in columns]

            # التحقق من وجود عمود type
            if 'type' not in column_names:
                # إضافة عمود type إلى الجدول الحالي
                cursor.execute("ALTER TABLE pending_stores ADD COLUMN type TEXT DEFAULT 'A'")

            # التحقق من وجود عمود address
            if 'address' not in column_names:
                # إضافة عمود address إلى الجدول الحالي
                cursor.execute("ALTER TABLE pending_stores ADD COLUMN address TEXT DEFAULT 'غير محدد'")

            # التحقق من وجود عمود city_name
            if 'city_name' not in column_names:
                # إضافة عمود city_name إلى الجدول الحالي
                cursor.execute("ALTER TABLE pending_stores ADD COLUMN city_name TEXT DEFAULT ''")

            # التحقق من وجود عمود region_name
            if 'region_name' not in column_names:
                # إضافة عمود region_name إلى الجدول الحالي
                cursor.execute("ALTER TABLE pending_stores ADD COLUMN region_name TEXT DEFAULT ''")

            # التحقق من وجود عمود city_id
            if 'city_id' not in column_names:
                # إضافة عمود city_id إلى الجدول الحالي
                cursor.execute("ALTER TABLE pending_stores ADD COLUMN city_id INTEGER")

            # التحقق من وجود عمود region_id
            if 'region_id' not in column_names:
                # إضافة عمود region_id إلى الجدول الحالي
                cursor.execute("ALTER TABLE pending_stores ADD COLUMN region_id INTEGER")

            # التحقق من وجود عمود full_address
            if 'full_address' not in column_names:
                # إضافة عمود full_address إلى الجدول الحالي
                cursor.execute("ALTER TABLE pending_stores ADD COLUMN full_address TEXT DEFAULT ''")

        if not pending_stores_table_exists:
            # إنشاء جدول المتاجر المعلقة
            cursor.execute('''
            CREATE TABLE pending_stores (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                phone TEXT,
                latitude REAL NOT NULL,
                longitude REAL NOT NULL,
                image_path TEXT,
                list_id INTEGER DEFAULT 1,
                marketer_id TEXT NOT NULL,
                type TEXT DEFAULT 'A',
                address TEXT DEFAULT 'غير محدد',
                full_address TEXT DEFAULT '',
                created_at TEXT,
                updated_at TEXT,
                FOREIGN KEY (marketer_id) REFERENCES users (id)
            )
            ''')

        # التحقق من وجود جدول إحصائيات المتاجر
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='store_statistics'")
        store_stats_table_exists = cursor.fetchone()

        if not store_stats_table_exists:
            # إنشاء جدول إحصائيات المتاجر
            cursor.execute('''
            CREATE TABLE store_statistics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                store_id TEXT NOT NULL,
                region TEXT,
                date TEXT NOT NULL,
                visits INTEGER DEFAULT 0,
                sales REAL DEFAULT 0,
                performance_score REAL DEFAULT 0,
                weather_condition TEXT,
                event_name TEXT,
                created_at TEXT,
                FOREIGN KEY (store_id) REFERENCES stores (id)
            )
            ''')

        # التحقق من وجود جدول تقارير الإحصائيات
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='statistics_reports'")
        stats_reports_table_exists = cursor.fetchone()

        if not stats_reports_table_exists:
            # إنشاء جدول تقارير الإحصائيات
            cursor.execute('''
            CREATE TABLE statistics_reports (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                title TEXT NOT NULL,
                description TEXT,
                filters TEXT,
                format TEXT DEFAULT 'PDF',
                user_id TEXT NOT NULL,
                scheduled BOOLEAN DEFAULT 0,
                schedule_frequency TEXT,
                last_generated TEXT,
                created_at TEXT,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
            ''')

        # التحقق من وجود جدول المناطق
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='regions'")
        regions_table_exists = cursor.fetchone()

        if not regions_table_exists:
            # إنشاء جدول المناطق
            cursor.execute('''
            CREATE TABLE regions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                parent_id INTEGER,
                region_type TEXT NOT NULL,
                latitude REAL,
                longitude REAL,
                min_lat REAL,
                max_lat REAL,
                min_lng REAL,
                max_lng REAL,
                synonyms TEXT,
                created_at TEXT,
                updated_at TEXT,
                FOREIGN KEY (parent_id) REFERENCES regions (id)
            )
            ''')

            # إنشاء جدول سجل تحديثات المناطق
            cursor.execute('''
            CREATE TABLE region_updates (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                region_id INTEGER NOT NULL,
                user_id TEXT,
                update_type TEXT NOT NULL,
                old_value TEXT,
                new_value TEXT,
                created_at TEXT,
                FOREIGN KEY (region_id) REFERENCES regions (id),
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
            ''')

            # إنشاء فهارس للبحث السريع
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_regions_name ON regions (name)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_regions_parent ON regions (parent_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_regions_type ON regions (region_type)')

        db.commit()
