/**
 * وحدة معالجة الأخطاء المخصصة
 * تقوم بتحسين عرض رسائل الخطأ للمستخدم
 */

// قائمة برسائل الخطأ المخصصة
const customErrorMessages = {
};

/**
 * تحسين عرض رسائل الخطأ
 * @param {string} errorMessage - رسالة الخطأ الأصلية
 * @returns {Object} - كائن يحتوي على معلومات الخطأ المحسنة
 */
function enhanceErrorMessage(errorMessage) {
    // البحث عن رسالة خطأ مخصصة
    for (const [key, value] of Object.entries(customErrorMessages)) {
        if (errorMessage.includes(key)) {
            return value;
        }
    }

    // إرجاع رسالة الخطأ الأصلية إذا لم يتم العثور على رسالة مخصصة
    return {
        title: 'خطأ',
        message: errorMessage,
        icon: 'fas fa-exclamation-circle',
        type: 'danger'
    };
}

/**
 * عرض رسالة خطأ محسنة
 * @param {string} errorMessage - رسالة الخطأ الأصلية
 * @param {number} autoCloseTime - الوقت بالملي ثانية قبل إغلاق التنبيه تلقائيًا
 */
function showEnhancedError(errorMessage, autoCloseTime = 5000) {
    const enhancedError = enhanceErrorMessage(errorMessage);

    // إنشاء عنصر التنبيه
    const alertContainer = document.getElementById('alertContainer');
    if (!alertContainer) {
        console.warn('عنصر alertContainer غير موجود، لا يمكن عرض التنبيه:', errorMessage);
        return;
    }

    const alert = document.createElement('div');
    alert.className = `alert alert-${enhancedError.type} alert-dismissible fade show`;
    alert.role = 'alert';

    alert.innerHTML = `
        <div class="d-flex align-items-center">
            <div class="me-3">
                <i class="${enhancedError.icon} fa-2x"></i>
            </div>
            <div>
                <h5 class="alert-heading mb-1">${enhancedError.title}</h5>
                <p class="mb-0">${enhancedError.message}</p>
            </div>
        </div>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;

    alertContainer.appendChild(alert);

    // إزالة التنبيه بعد الوقت المحدد
    setTimeout(() => {
        alert.classList.remove('show');
        setTimeout(() => alert.remove(), 150);
    }, autoCloseTime);
}

// تعديل دالة showAlert الأصلية لاستخدام معالج الأخطاء المحسن
const originalShowAlert = window.showAlert;
/**
 * عرض تنبيه للمستخدم
 * @param {string} message - نص الرسالة
 * @param {string} type - نوع التنبيه (info, success, warning, danger)
 * @param {number} autoCloseTime - الوقت بالملي ثانية قبل إغلاق التنبيه تلقائيًا
 */
window.showAlert = function(message, type = 'info', autoCloseTime = 5000) {
    // إذا كان نوع التنبيه هو خطأ، استخدم معالج الأخطاء المحسن
    if (type === 'danger' || type === 'warning') {
        showEnhancedError(message, autoCloseTime);
    } else {
        // استخدم الدالة الأصلية لأنواع التنبيهات الأخرى
        if (typeof originalShowAlert === 'function') {
            originalShowAlert(message, type);

            // إذا تم تحديد وقت للإغلاق التلقائي، ابحث عن آخر تنبيه وقم بإغلاقه بعد الوقت المحدد
            if (autoCloseTime) {
                const alerts = document.querySelectorAll('.alert');
                if (alerts.length > 0) {
                    const lastAlert = alerts[alerts.length - 1];
                    setTimeout(() => {
                        lastAlert.classList.remove('show');
                        setTimeout(() => lastAlert.remove(), 150);
                    }, autoCloseTime);
                }
            }
        } else {
            // إذا لم تكن الدالة الأصلية متاحة، استخدم تنفيذًا بسيطًا
            const alertContainer = document.getElementById('alertContainer');
            if (!alertContainer) {
                console.warn('عنصر alertContainer غير موجود، لا يمكن عرض التنبيه:', message);
                return;
            }

            const alert = document.createElement('div');
            alert.className = `alert alert-${type} alert-dismissible fade show`;
            alert.role = 'alert';

            alert.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            `;

            alertContainer.appendChild(alert);

            // إزالة التنبيه بعد الوقت المحدد
            setTimeout(() => {
                alert.classList.remove('show');
                setTimeout(() => alert.remove(), 150);
            }, autoCloseTime);
        }
    }
};

// تصدير الدوال
window.ErrorHandler = {
    enhanceErrorMessage,
    showEnhancedError
};
