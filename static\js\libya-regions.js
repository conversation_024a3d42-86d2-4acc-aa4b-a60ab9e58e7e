/**
 * Libya Regions Utility
 * يحتوي على تعريفات المناطق الجغرافية في ليبيا وأدوات تحديد المنطقة من الإحداثيات
 */

const LibyaRegions = {
    // تعريف المناطق الفرعية في طرابلس
    tripoliDistricts: [
        { name: "طرابلس - وسط المدينة", lat: 32.8925, lng: 13.1802, bounds: { minLat: 32.880, maxLat: 32.905, minLng: 13.170, maxLng: 13.190 } },
        { name: "طرابلس - باب البحر", lat: 32.8964, lng: 13.1823, bounds: { minLat: 32.890, maxLat: 32.903, minLng: 13.175, maxLng: 13.190 } },
        { name: "طرابلس - سوق الجمعة", lat: 32.9086, lng: 13.1944, bounds: { minLat: 32.900, maxLat: 32.920, minLng: 13.185, maxLng: 13.205 } },
        { name: "طرابلس - أبو سليم", lat: 32.8472, lng: 13.1731, bounds: { minLat: 32.835, maxLat: 32.860, minLng: 13.160, maxLng: 13.185 } },
        { name: "طرابلس - عين زارة", lat: 32.8167, lng: 13.2500, bounds: { minLat: 32.800, maxLat: 32.835, minLng: 13.230, maxLng: 13.270 } },
        { name: "طرابلس - تاجوراء", lat: 32.8828, lng: 13.3500, bounds: { minLat: 32.865, maxLat: 32.900, minLng: 13.320, maxLng: 13.380 } },
        { name: "طرابلس - جنزور", lat: 32.8333, lng: 13.0333, bounds: { minLat: 32.820, maxLat: 32.850, minLng: 13.000, maxLng: 13.070 } },
        { name: "طرابلس - حي الأندلس", lat: 32.8700, lng: 13.1400, bounds: { minLat: 32.860, maxLat: 32.880, minLng: 13.130, maxLng: 13.150 } },
        { name: "طرابلس - قرقارش", lat: 32.8600, lng: 13.1100, bounds: { minLat: 32.850, maxLat: 32.870, minLng: 13.090, maxLng: 13.130 } },
        { name: "طرابلس - حي الأكواخ", lat: 32.8750, lng: 13.2000, bounds: { minLat: 32.865, maxLat: 32.885, minLng: 13.190, maxLng: 13.210 } },
        { name: "طرابلس - السراج", lat: 32.8200, lng: 13.1000, bounds: { minLat: 32.810, maxLat: 32.830, minLng: 13.090, maxLng: 13.110 } },
        { name: "طرابلس - سيدي المصري", lat: 32.8800, lng: 13.1600, bounds: { minLat: 32.870, maxLat: 32.890, minLng: 13.150, maxLng: 13.170 } },
        { name: "طرابلس - الهضبة", lat: 32.8500, lng: 13.2100, bounds: { minLat: 32.840, maxLat: 32.860, minLng: 13.200, maxLng: 13.220 } },
        // المناطق الجديدة المضافة
        { name: "طرابلس - باب بن غشير", lat: 32.8550, lng: 13.1850, bounds: { minLat: 32.845, maxLat: 32.865, minLng: 13.175, maxLng: 13.195 } },
        { name: "طرابلس - الفرناج", lat: 32.8600, lng: 13.1950, bounds: { minLat: 32.850, maxLat: 32.870, minLng: 13.185, maxLng: 13.205 } },
        { name: "طرابلس - قصر بن غشير", lat: 32.7900, lng: 13.1400, bounds: { minLat: 32.780, maxLat: 32.800, minLng: 13.130, maxLng: 13.150 } },
        { name: "طرابلس - الهضبة الخضراء", lat: 32.8450, lng: 13.2050, bounds: { minLat: 32.835, maxLat: 32.855, minLng: 13.195, maxLng: 13.215 } },
        { name: "طرابلس - غوط الشعال", lat: 32.8750, lng: 13.1700, bounds: { minLat: 32.865, maxLat: 32.885, minLng: 13.160, maxLng: 13.180 } },
        { name: "طرابلس - السياحية", lat: 32.8650, lng: 13.1300, bounds: { minLat: 32.855, maxLat: 32.875, minLng: 13.120, maxLng: 13.140 } },
        { name: "طرابلس - زاوية الدهماني", lat: 32.8850, lng: 13.1750, bounds: { minLat: 32.875, maxLat: 32.895, minLng: 13.165, maxLng: 13.185 } },
        { name: "طرابلس - فشلوم", lat: 32.8950, lng: 13.1850, bounds: { minLat: 32.885, maxLat: 32.905, minLng: 13.175, maxLng: 13.195 } },
        { name: "طرابلس - زاوية المحجوب", lat: 32.8700, lng: 13.1200, bounds: { minLat: 32.860, maxLat: 32.880, minLng: 13.110, maxLng: 13.130 } },
        { name: "طرابلس - الكريمية", lat: 32.8400, lng: 13.1350, bounds: { minLat: 32.830, maxLat: 32.850, minLng: 13.125, maxLng: 13.145 } },
        { name: "طرابلس - السبعة", lat: 32.8300, lng: 13.1450, bounds: { minLat: 32.820, maxLat: 32.840, minLng: 13.135, maxLng: 13.155 } },
        { name: "طرابلس - الحشان", lat: 32.8350, lng: 13.1550, bounds: { minLat: 32.825, maxLat: 32.845, minLng: 13.145, maxLng: 13.165 } },
        { name: "طرابلس - طريق المطار", lat: 32.8100, lng: 13.1900, bounds: { minLat: 32.800, maxLat: 32.820, minLng: 13.180, maxLng: 13.200 } },
        { name: "طرابلس - مشروع الهضبة", lat: 32.8450, lng: 13.2150, bounds: { minLat: 32.835, maxLat: 32.855, minLng: 13.205, maxLng: 13.225 } },
        { name: "طرابلس - عرادة", lat: 32.8800, lng: 13.1900, bounds: { minLat: 32.870, maxLat: 32.890, minLng: 13.180, maxLng: 13.200 } },
        { name: "طرابلس - الحي الإسلامي", lat: 32.8600, lng: 13.1800, bounds: { minLat: 32.850, maxLat: 32.870, minLng: 13.170, maxLng: 13.190 } },
        { name: "طرابلس - أبوسليم المشروع", lat: 32.8400, lng: 13.1700, bounds: { minLat: 32.830, maxLat: 32.850, minLng: 13.160, maxLng: 13.180 } },
        { name: "طرابلس - الظهرة", lat: 32.8500, lng: 13.1600, bounds: { minLat: 32.840, maxLat: 32.860, minLng: 13.150, maxLng: 13.170 } },
        { name: "طرابلس - حي دمشق", lat: 32.8550, lng: 13.1650, bounds: { minLat: 32.845, maxLat: 32.865, minLng: 13.155, maxLng: 13.175 } },
        { name: "طرابلس - النوفليين", lat: 32.8900, lng: 13.1750, bounds: { minLat: 32.880, maxLat: 32.900, minLng: 13.165, maxLng: 13.185 } },
        { name: "طرابلس - السياحية الغربية", lat: 32.8650, lng: 13.1250, bounds: { minLat: 32.855, maxLat: 32.875, minLng: 13.115, maxLng: 13.135 } },
        { name: "طرابلس - الكريمية الشرقية", lat: 32.8400, lng: 13.1400, bounds: { minLat: 32.830, maxLat: 32.850, minLng: 13.130, maxLng: 13.150 } },
        { name: "طرابلس - السواني", lat: 32.8200, lng: 13.0800, bounds: { minLat: 32.810, maxLat: 32.830, minLng: 13.070, maxLng: 13.090 } },
        { name: "طرابلس - القره بوللي", lat: 32.9000, lng: 13.5000, bounds: { minLat: 32.890, maxLat: 32.910, minLng: 13.490, maxLng: 13.510 } },
        { name: "طرابلس - الماية", lat: 32.8000, lng: 13.1000, bounds: { minLat: 32.790, maxLat: 32.810, minLng: 13.090, maxLng: 13.110 } },
        { name: "طرابلس - الساعدية", lat: 32.8700, lng: 13.1500, bounds: { minLat: 32.860, maxLat: 32.880, minLng: 13.140, maxLng: 13.160 } },
        { name: "طرابلس - العزيزية", lat: 32.7800, lng: 13.1500, bounds: { minLat: 32.770, maxLat: 32.790, minLng: 13.140, maxLng: 13.160 } },
        { name: "طرابلس - الكريمية طريق الساحلي", lat: 32.8400, lng: 13.1300, bounds: { minLat: 32.830, maxLat: 32.850, minLng: 13.120, maxLng: 13.140 } },
        { name: "طرابلس - النجيلة", lat: 32.8600, lng: 13.1500, bounds: { minLat: 32.850, maxLat: 32.870, minLng: 13.140, maxLng: 13.160 } },
        { name: "طرابلس - الطويشة", lat: 32.8500, lng: 13.1400, bounds: { minLat: 32.840, maxLat: 32.860, minLng: 13.130, maxLng: 13.150 } },
        { name: "طرابلس - بوسليم بئر الأسطى ميلاد", lat: 32.8450, lng: 13.1750, bounds: { minLat: 32.835, maxLat: 32.855, minLng: 13.165, maxLng: 13.185 } },
        { name: "طرابلس - المدينة القديمة", lat: 32.8950, lng: 13.1800, bounds: { minLat: 32.885, maxLat: 32.905, minLng: 13.170, maxLng: 13.190 } }
    ],

    // تعريف المناطق الفرعية في بنغازي
    benghaziDistricts: [
        { name: "بنغازي - وسط المدينة", lat: 32.1167, lng: 20.0667, bounds: { minLat: 32.105, maxLat: 32.125, minLng: 20.055, maxLng: 20.075 } },
        { name: "بنغازي - الصابري", lat: 32.1200, lng: 20.0800, bounds: { minLat: 32.115, maxLat: 32.125, minLng: 20.075, maxLng: 20.085 } },
        { name: "بنغازي - البركة", lat: 32.1150, lng: 20.0750, bounds: { minLat: 32.110, maxLat: 32.120, minLng: 20.070, maxLng: 20.080 } },
        { name: "بنغازي - السلماني الشرقي", lat: 32.1050, lng: 20.0850, bounds: { minLat: 32.100, maxLat: 32.110, minLng: 20.080, maxLng: 20.090 } },
        { name: "بنغازي - السلماني الغربي", lat: 32.1050, lng: 20.0750, bounds: { minLat: 32.100, maxLat: 32.110, minLng: 20.070, maxLng: 20.080 } },
        { name: "بنغازي - سيدي حسين", lat: 32.1100, lng: 20.0500, bounds: { minLat: 32.105, maxLat: 32.115, minLng: 20.045, maxLng: 20.055 } },
        { name: "بنغازي - الكيش", lat: 32.1000, lng: 20.0600, bounds: { minLat: 32.095, maxLat: 32.105, minLng: 20.055, maxLng: 20.065 } },
        { name: "بنغازي - الفويهات", lat: 32.0950, lng: 20.0550, bounds: { minLat: 32.090, maxLat: 32.100, minLng: 20.050, maxLng: 20.060 } },
        { name: "بنغازي - اللثامة", lat: 32.1050, lng: 20.0600, bounds: { minLat: 32.100, maxLat: 32.110, minLng: 20.055, maxLng: 20.065 } },
        { name: "بنغازي - الماجوري", lat: 32.0900, lng: 20.0650, bounds: { minLat: 32.085, maxLat: 32.095, minLng: 20.060, maxLng: 20.070 } },
        { name: "بنغازي - المحيشي", lat: 32.0850, lng: 20.0700, bounds: { minLat: 32.080, maxLat: 32.090, minLng: 20.065, maxLng: 20.075 } },
        { name: "بنغازي - الرويسات", lat: 32.0800, lng: 20.0750, bounds: { minLat: 32.075, maxLat: 32.085, minLng: 20.070, maxLng: 20.080 } },
        { name: "بنغازي - الكويفية", lat: 32.1250, lng: 20.0450, bounds: { minLat: 32.120, maxLat: 32.130, minLng: 20.040, maxLng: 20.050 } },
        { name: "بنغازي - قنفودة", lat: 32.0750, lng: 20.0500, bounds: { minLat: 32.070, maxLat: 32.080, minLng: 20.045, maxLng: 20.055 } },
        { name: "بنغازي - جروثة", lat: 32.0700, lng: 20.0550, bounds: { minLat: 32.065, maxLat: 32.075, minLng: 20.050, maxLng: 20.060 } },
        { name: "بنغازي - بو فاخرة", lat: 32.0650, lng: 20.0600, bounds: { minLat: 32.060, maxLat: 32.070, minLng: 20.055, maxLng: 20.065 } },
        { name: "بنغازي - بوعطني", lat: 32.0600, lng: 20.0650, bounds: { minLat: 32.055, maxLat: 32.065, minLng: 20.060, maxLng: 20.070 } },
        { name: "بنغازي - الهواري", lat: 32.0550, lng: 20.0700, bounds: { minLat: 32.050, maxLat: 32.060, minLng: 20.065, maxLng: 20.075 } },
        { name: "بنغازي - بنينا", lat: 32.0967, lng: 20.2694, bounds: { minLat: 32.090, maxLat: 32.100, minLng: 20.260, maxLng: 20.280 } },
        { name: "بنغازي - سيدي خليفة", lat: 32.0700, lng: 20.1200, bounds: { minLat: 32.065, maxLat: 32.075, minLng: 20.115, maxLng: 20.125 } },
        { name: "بنغازي - قمينس", lat: 32.0500, lng: 20.0800, bounds: { minLat: 32.045, maxLat: 32.055, minLng: 20.075, maxLng: 20.085 } },
        { name: "بنغازي - سلوق", lat: 32.0450, lng: 20.0850, bounds: { minLat: 32.040, maxLat: 32.050, minLng: 20.080, maxLng: 20.090 } },
        { name: "بنغازي - حي الحدائق", lat: 32.1100, lng: 20.0700, bounds: { minLat: 32.105, maxLat: 32.115, minLng: 20.065, maxLng: 20.075 } },
        { name: "بنغازي - حي الفاتح", lat: 32.1050, lng: 20.0650, bounds: { minLat: 32.100, maxLat: 32.110, minLng: 20.060, maxLng: 20.070 } },
        { name: "بنغازي - رأس اعبيدة", lat: 32.1300, lng: 20.0500, bounds: { minLat: 32.125, maxLat: 32.135, minLng: 20.045, maxLng: 20.055 } },
        { name: "بنغازي - الزريريعية", lat: 32.1350, lng: 20.0550, bounds: { minLat: 32.130, maxLat: 32.140, minLng: 20.050, maxLng: 20.060 } },
        { name: "بنغازي - القوارشة", lat: 32.1300, lng: 20.0400, bounds: { minLat: 32.125, maxLat: 32.135, minLng: 20.035, maxLng: 20.045 } },
        { name: "بنغازي - قاريونس", lat: 32.0800, lng: 20.1100, bounds: { minLat: 32.075, maxLat: 32.085, minLng: 20.105, maxLng: 20.115 } }
    ],

    // تعريف المناطق الرئيسية في ليبيا مع إحداثياتها التقريبية
    mainRegions: [
        { name: "طرابلس", lat: 32.8872, lng: 13.1913, bounds: { minLat: 32.7, maxLat: 33.0, minLng: 12.9, maxLng: 13.4 } },
        { name: "بنغازي", lat: 32.1167, lng: 20.0667, bounds: { minLat: 31.9, maxLat: 32.3, minLng: 19.8, maxLng: 20.3 } },
        { name: "مصراتة", lat: 32.3754, lng: 15.0925, bounds: { minLat: 32.2, maxLat: 32.5, minLng: 14.9, maxLng: 15.3 } },
        { name: "الزاوية", lat: 32.7571, lng: 12.7276, bounds: { minLat: 32.6, maxLat: 32.9, minLng: 12.5, maxLng: 12.9 } },
        { name: "سبها", lat: 27.0377, lng: 14.4283, bounds: { minLat: 26.8, maxLat: 27.2, minLng: 14.2, maxLng: 14.6 } },
        { name: "تاجوراء", lat: 32.8829, lng: 13.3500, bounds: { minLat: 32.7, maxLat: 33.0, minLng: 13.2, maxLng: 13.5 } },
        { name: "زليتن", lat: 32.4674, lng: 14.5689, bounds: { minLat: 32.3, maxLat: 32.6, minLng: 14.4, maxLng: 14.7 } },
        { name: "صبراتة", lat: 32.7933, lng: 12.4885, bounds: { minLat: 32.6, maxLat: 32.9, minLng: 12.3, maxLng: 12.6 } },
        { name: "سرت", lat: 31.2089, lng: 16.5910, bounds: { minLat: 31.0, maxLat: 31.4, minLng: 16.4, maxLng: 16.8 } },
        { name: "البيضاء", lat: 32.7627, lng: 21.7551, bounds: { minLat: 32.6, maxLat: 32.9, minLng: 21.6, maxLng: 21.9 } },
        { name: "طبرق", lat: 32.0836, lng: 23.9764, bounds: { minLat: 31.9, maxLat: 32.2, minLng: 23.8, maxLng: 24.1 } },
        { name: "غريان", lat: 32.1722, lng: 13.0203, bounds: { minLat: 32.0, maxLat: 32.3, minLng: 12.9, maxLng: 13.2 } },
        { name: "الخمس", lat: 32.6500, lng: 14.2667, bounds: { minLat: 32.5, maxLat: 32.8, minLng: 14.1, maxLng: 14.4 } },
        { name: "درنة", lat: 32.7667, lng: 22.6333, bounds: { minLat: 32.6, maxLat: 32.9, minLng: 22.5, maxLng: 22.8 } }
    ],

    // للتوافق مع الكود القديم
    regions: [],

    /**
     * تحديد المنطقة من الإحداثيات بشكل دقيق
     * @param {number} lat - خط العرض
     * @param {number} lng - خط الطول
     * @returns {string|null} - اسم المنطقة أو null إذا لم يتم العثور على منطقة مطابقة تماماً
     */
    getRegionFromCoordinates: function(lat, lng) {
        // تم حذف هذه الدالة
        return "منطقة غير محددة";
    },

    /**
     * تحديد المنطقة من الإحداثيات مع محاولة إيجاد أقرب منطقة إذا لم يتم العثور على مطابقة دقيقة
     * @param {number} lat - خط العرض
     * @param {number} lng - خط الطول
     * @returns {string} - اسم المنطقة
     */
    getRegionFromCoordinatesWithFallback: function(lat, lng) {
        // تم حذف هذه الدالة
        return "منطقة غير محددة";
    },

    /**
     * حساب المسافة بين نقطتين باستخدام صيغة هافرساين
     * @param {number} lat1 - خط العرض للنقطة الأولى
     * @param {number} lng1 - خط الطول للنقطة الأولى
     * @param {number} lat2 - خط العرض للنقطة الثانية
     * @param {number} lng2 - خط الطول للنقطة الثانية
     * @returns {number} - المسافة بالدرجات (تقريبية)
     */
    calculateDistance: function(lat1, lng1, lat2, lng2) {
        // تم حذف هذه الدالة
        return 0;
    },

    /**
     * الحصول على قائمة بجميع المناطق
     * @param {boolean} includeDistricts - ما إذا كان يجب تضمين المناطق الفرعية
     * @returns {Array} - قائمة بأسماء المناطق
     */
    getAllRegions: function(includeDistricts = false) {
        if (includeDistricts) {
            // إرجاع جميع المناطق بما في ذلك المناطق الفرعية
            const mainRegions = this.mainRegions.map(region => region.name);
            const tripoliDistricts = this.tripoliDistricts.map(district => district.name);
            const benghaziDistricts = this.benghaziDistricts.map(district => district.name);
            return [...mainRegions, ...tripoliDistricts, ...benghaziDistricts];
        } else {
            // إرجاع المناطق الرئيسية فقط
            return this.mainRegions.map(region => region.name);
        }
    },

    /**
     * الحصول على قائمة بالمناطق الفرعية في طرابلس
     * @returns {Array} - قائمة بأسماء المناطق الفرعية في طرابلس
     */
    getTripoliDistricts: function() {
        return this.tripoliDistricts.map(district => district.name);
    },

    /**
     * الحصول على قائمة بالمناطق الفرعية في بنغازي
     * @returns {Array} - قائمة بأسماء المناطق الفرعية في بنغازي
     */
    getBenghaziDistricts: function() {
        return this.benghaziDistricts.map(district => district.name);
    },

    /**
     * الحصول على إحداثيات منطقة معينة
     * @param {string} regionName - اسم المنطقة
     * @returns {Object|null} - كائن يحتوي على إحداثيات المنطقة أو null إذا لم يتم العثور عليها
     */
    getRegionCoordinates: function(regionName) {
        // البحث في المناطق الرئيسية
        const region = this.mainRegions.find(r => r.name === regionName);
        if (region) {
            return { lat: region.lat, lng: region.lng };
        }

        // البحث في المناطق الفرعية في طرابلس
        const tripoliDistrict = this.tripoliDistricts.find(d => d.name === regionName);
        if (tripoliDistrict) {
            return { lat: tripoliDistrict.lat, lng: tripoliDistrict.lng };
        }

        // البحث في المناطق الفرعية في بنغازي
        const benghaziDistrict = this.benghaziDistricts.find(d => d.name === regionName);
        if (benghaziDistrict) {
            return { lat: benghaziDistrict.lat, lng: benghaziDistrict.lng };
        }

        return null;
    },

    /**
     * الحصول على المناطق الفرعية لمنطقة رئيسية
     * @param {string} mainRegion - اسم المنطقة الرئيسية
     * @returns {Array} - قائمة بأسماء المناطق الفرعية
     */
    getSubRegions: function(mainRegion) {
        if (mainRegion === "طرابلس") {
            return this.getTripoliDistricts();
        } else if (mainRegion === "بنغازي") {
            return this.getBenghaziDistricts();
        }
        return [];
    },

    /**
     * استخراج عنوان المتجر من حقول city_name و region_name
     * @param {Object} store - كائن المتجر
     * @returns {string|null} - عنوان المتجر المدمج أو null
     */
    getStoreAddressFromFields: function(store) {
        if (!store || typeof store !== 'object') {
            return null;
        }

        const cityName = store.city_name ? store.city_name.trim() : '';
        const regionName = store.region_name ? store.region_name.trim() : '';

        // تجاهل "المنطقة غير محددة" فقط
        const isAlertMessage = regionName === 'المنطقة غير محددة';

        // إذا كان لدينا كلا الحقلين وكانت المنطقة ليست رسالة تنبيه
        if (cityName && regionName && !isAlertMessage) {
            return `${cityName} - ${regionName}`;
        }

        // إذا كان لدينا المدينة فقط
        if (cityName) {
            return cityName;
        }

        // إذا كان لدينا المنطقة فقط وليست رسالة تنبيه
        if (regionName && !isAlertMessage) {
            return regionName;
        }

        // إذا لم يكن لدينا أي منهما، تحقق من حقل address
        if (store.address && store.address.trim() !== '') {
            return store.address.trim();
        }

        return null;
    },

    /**
     * استخراج المنطقة من عنوان المتجر
     * @param {Object} store - كائن المتجر
     * @returns {string|null} - اسم المنطقة أو null إذا لم يتم العثور على منطقة
     */
    getRegionFromStore: function(store) {
        const storeAddress = this.getStoreAddressFromFields(store);
        if (!storeAddress) {
            return null;
        }

        return storeAddress;
    },

    /**
     * استخراج المدينة الرئيسية من المتجر
     * @param {Object} store - كائن المتجر
     * @returns {string|null} - اسم المدينة الرئيسية أو null
     */
    getMainCityFromStore: function(store) {
        if (!store || typeof store !== 'object') {
            return null;
        }

        // أولاً، تحقق من حقل city_name
        if (store.city_name && store.city_name.trim() !== '') {
            return store.city_name.trim();
        }

        // إذا لم يكن موجوداً، حاول استخراجه من العنوان المدمج
        const storeAddress = this.getStoreAddressFromFields(store);
        if (storeAddress && storeAddress.includes(' - ')) {
            return storeAddress.split(' - ')[0].trim();
        }

        // إذا لم يحتوي على " - " فالعنوان كله هو المدينة الرئيسية
        return storeAddress;
    },

    /**
     * استخراج المنطقة الفرعية من المتجر
     * @param {Object} store - كائن المتجر
     * @returns {string|null} - اسم المنطقة الفرعية أو null إذا لم توجد
     */
    getSubRegionFromStore: function(store) {
        if (!store || typeof store !== 'object') {
            return null;
        }

        // أولاً، تحقق من حقل region_name وتجاهل "المنطقة غير محددة" فقط
        if (store.region_name && store.region_name.trim() !== '') {
            const regionName = store.region_name.trim();
            if (regionName !== 'المنطقة غير محددة') {
                return regionName;
            }
        }

        // إذا لم يكن موجوداً، حاول استخراجه من العنوان المدمج
        const storeAddress = this.getStoreAddressFromFields(store);
        if (storeAddress && storeAddress.includes(' - ')) {
            const parts = storeAddress.split(' - ');
            if (parts.length >= 2) {
                return parts[1].trim();
            }
        }

        // إذا لم يحتوي على " - " فلا توجد منطقة فرعية
        return null;
    },

    /**
     * تحليل بيانات المتجر وإرجاع معلومات مفصلة
     * @param {Object} store - كائن المتجر
     * @returns {Object} - كائن يحتوي على المدينة الرئيسية والمنطقة الفرعية والعنوان الكامل
     */
    parseStoreAddress: function(store) {
        if (!store || typeof store !== 'object') {
            return {
                fullAddress: null,
                mainCity: null,
                subRegion: null,
                isSubRegion: false
            };
        }

        const fullAddress = this.getStoreAddressFromFields(store);
        const mainCity = this.getMainCityFromStore(store);
        const subRegion = this.getSubRegionFromStore(store);

        return {
            fullAddress: fullAddress,
            mainCity: mainCity,
            subRegion: subRegion,
            isSubRegion: subRegion !== null
        };
    },

    /**
     * استخراج المنطقة من وصف العنوان (للتوافق مع النظام القديم)
     * @param {string} address - وصف العنوان
     * @returns {string|null} - اسم المنطقة أو null إذا لم يتم العثور على منطقة
     */
    getRegionFromAddress: function(address) {
        if (!address || typeof address !== 'string' || address.trim() === '') {
            return null;
        }

        // تنظيف العنوان من المسافات الزائدة
        const cleanAddress = address.trim();

        // إذا كان العنوان يحتوي على " - " فهو يتبع النمط: "المدينة - المنطقة"
        if (cleanAddress.includes(' - ')) {
            return cleanAddress; // إرجاع العنوان كاملاً كمنطقة
        }

        // إذا لم يحتوي على " - " فهو مدينة رئيسية فقط
        return cleanAddress;
    },

    /**
     * استخراج المدينة الرئيسية من العنوان (للتوافق مع النظام القديم)
     * @param {string} address - وصف العنوان
     * @returns {string|null} - اسم المدينة الرئيسية أو null
     */
    getMainCityFromAddress: function(address) {
        if (!address || typeof address !== 'string' || address.trim() === '') {
            return null;
        }

        const cleanAddress = address.trim();

        // إذا كان العنوان يحتوي على " - " فالجزء الأول هو المدينة الرئيسية
        if (cleanAddress.includes(' - ')) {
            return cleanAddress.split(' - ')[0].trim();
        }

        // إذا لم يحتوي على " - " فالعنوان كله هو المدينة الرئيسية
        return cleanAddress;
    },

    /**
     * استخراج المنطقة الفرعية من العنوان (للتوافق مع النظام القديم)
     * @param {string} address - وصف العنوان
     * @returns {string|null} - اسم المنطقة الفرعية أو null إذا لم توجد
     */
    getSubRegionFromAddress: function(address) {
        if (!address || typeof address !== 'string' || address.trim() === '') {
            return null;
        }

        const cleanAddress = address.trim();

        // إذا كان العنوان يحتوي على " - " فالجزء الثاني هو المنطقة الفرعية
        if (cleanAddress.includes(' - ')) {
            const parts = cleanAddress.split(' - ');
            if (parts.length >= 2) {
                return parts[1].trim();
            }
        }

        // إذا لم يحتوي على " - " فلا توجد منطقة فرعية
        return null;
    },

    /**
     * تحليل العنوان وإرجاع معلومات مفصلة (للتوافق مع النظام القديم)
     * @param {string} address - وصف العنوان
     * @returns {Object} - كائن يحتوي على المدينة الرئيسية والمنطقة الفرعية والعنوان الكامل
     */
    parseAddress: function(address) {
        if (!address || typeof address !== 'string' || address.trim() === '') {
            return {
                fullAddress: null,
                mainCity: null,
                subRegion: null,
                isSubRegion: false
            };
        }

        const cleanAddress = address.trim();
        const mainCity = this.getMainCityFromAddress(cleanAddress);
        const subRegion = this.getSubRegionFromAddress(cleanAddress);

        return {
            fullAddress: cleanAddress,
            mainCity: mainCity,
            subRegion: subRegion,
            isSubRegion: subRegion !== null
        };
    },

    /**
     * تحديد المنطقة بشكل ذكي من كائن المتجر
     * @param {Object} store - كائن المتجر
     * @returns {string} - اسم المنطقة
     */
    getSmartRegionFromStore: function(store) {
        if (!store || typeof store !== 'object') {
            return "غير محدد";
        }

        // أولاً، محاولة استخراج المنطقة من حقول عنوان المتجر
        const storeAddress = this.getStoreAddressFromFields(store);
        if (storeAddress) {
            return storeAddress;
        }

        // إذا لم يكن هناك عنوان صالح، استخدم الإحداثيات
        if (store.latitude && store.longitude &&
            !isNaN(store.latitude) && !isNaN(store.longitude)) {
            const lat_num = parseFloat(store.latitude);
            const lng_num = parseFloat(store.longitude);

            // تحديد المنطقة الرئيسية بناءً على الإحداثيات التقريبية
            if (32.7 <= lat_num && lat_num <= 33.0 && 12.9 <= lng_num && lng_num <= 13.4) {
                return "طرابلس";
            } else if (31.9 <= lat_num && lat_num <= 32.3 && 19.8 <= lng_num && lng_num <= 20.3) {
                return "بنغازي";
            } else if (32.2 <= lat_num && lat_num <= 32.5 && 14.9 <= lng_num && lng_num <= 15.3) {
                return "مصراتة";
            }
        }

        // إذا فشل كل شيء، إرجاع قيمة افتراضية
        return "غير محدد";
    },

    /**
     * تحديد المنطقة بشكل ذكي باستخدام الإحداثيات ووصف العنوان (للتوافق مع النظام القديم)
     * @param {number} lat - خط العرض
     * @param {number} lng - خط الطول
     * @param {string} address - وصف العنوان
     * @returns {string} - اسم المنطقة
     */
    getSmartRegion: function(lat, lng, address) {
        // أولاً، محاولة استخراج المنطقة من العنوان
        const regionFromAddress = this.getRegionFromAddress(address);
        if (regionFromAddress) {
            return regionFromAddress;
        }

        // إذا لم يكن هناك عنوان صالح، استخدم الإحداثيات
        if (lat && lng && !isNaN(lat) && !isNaN(lng)) {
            const lat_num = parseFloat(lat);
            const lng_num = parseFloat(lng);

            // تحديد المنطقة الرئيسية بناءً على الإحداثيات التقريبية
            if (32.7 <= lat_num && lat_num <= 33.0 && 12.9 <= lng_num && lng_num <= 13.4) {
                return "طرابلس";
            } else if (31.9 <= lat_num && lat_num <= 32.3 && 19.8 <= lng_num && lng_num <= 20.3) {
                return "بنغازي";
            } else if (32.2 <= lat_num && lat_num <= 32.5 && 14.9 <= lng_num && lng_num <= 15.3) {
                return "مصراتة";
            }
        }

        // إذا فشل كل شيء، إرجاع قيمة افتراضية
        return "غير محدد";
    },

    /**
     * الحصول على قائمة بجميع المدن الرئيسية من مجموعة من المتاجر
     * @param {Array} stores - مصفوفة المتاجر
     * @returns {Array} - قائمة بأسماء المدن الرئيسية الفريدة
     */
    getUniqueCitiesFromStores: function(stores) {
        if (!stores || !Array.isArray(stores)) {
            return [];
        }

        const cities = new Set();

        stores.forEach(store => {
            const mainCity = this.getMainCityFromStore(store);
            if (mainCity) {
                cities.add(mainCity);
            }
        });

        return Array.from(cities).sort();
    },

    /**
     * الحصول على قائمة بجميع المناطق الفرعية لمدينة معينة من مجموعة من المتاجر
     * @param {Array} stores - مصفوفة المتاجر
     * @param {string} cityName - اسم المدينة الرئيسية
     * @returns {Array} - قائمة بأسماء المناطق الفرعية الفريدة
     */
    getSubRegionsForCity: function(stores, cityName) {
        if (!stores || !Array.isArray(stores) || !cityName) {
            return [];
        }

        const subRegions = new Set();

        stores.forEach(store => {
            const storeInfo = this.parseStoreAddress(store);
            if (storeInfo.mainCity === cityName && storeInfo.subRegion) {
                subRegions.add(storeInfo.subRegion);
            }
        });

        return Array.from(subRegions).sort();
    },

    /**
     * تجميع المتاجر حسب المدن والمناطق
     * @param {Array} stores - مصفوفة المتاجر
     * @returns {Object} - كائن يحتوي على تجميع المتاجر حسب المدن والمناطق
     */
    groupStoresByRegions: function(stores) {
        if (!stores || !Array.isArray(stores)) {
            return {
                cities: {},
                regions: {},
                totalStores: 0,
                storesWithValidAddress: 0,
                storesWithoutAddress: 0
            };
        }

        const cities = {};
        const regions = {};
        let totalStores = stores.length;
        let storesWithValidAddress = 0;
        let storesWithoutAddress = 0;

        stores.forEach(store => {
            // استخراج عنوان المتجر من الحقول الصحيحة
            const storeAddress = this.getStoreAddressFromFields(store);

            if (!storeAddress) {
                storesWithoutAddress++;
                return;
            }

            storesWithValidAddress++;
            const addressInfo = this.parseStoreAddress(store);

            if (addressInfo.mainCity) {
                // تجميع حسب المدينة الرئيسية
                if (!cities[addressInfo.mainCity]) {
                    cities[addressInfo.mainCity] = {
                        name: addressInfo.mainCity,
                        totalStores: 0,
                        subRegions: {},
                        storeTypes: { A: 0, B: 0, D: 0, other: 0 }
                    };
                }

                cities[addressInfo.mainCity].totalStores++;

                // تحديث عدد المتاجر حسب النوع
                const storeType = ['A', 'B', 'D'].includes(store.type) ? store.type : 'other';
                cities[addressInfo.mainCity].storeTypes[storeType]++;

                // تجميع حسب المنطقة الفرعية إذا وجدت
                if (addressInfo.subRegion) {
                    if (!cities[addressInfo.mainCity].subRegions[addressInfo.subRegion]) {
                        cities[addressInfo.mainCity].subRegions[addressInfo.subRegion] = {
                            name: addressInfo.subRegion,
                            totalStores: 0,
                            storeTypes: { A: 0, B: 0, D: 0, other: 0 }
                        };
                    }

                    cities[addressInfo.mainCity].subRegions[addressInfo.subRegion].totalStores++;
                    cities[addressInfo.mainCity].subRegions[addressInfo.subRegion].storeTypes[storeType]++;
                }

                // تجميع حسب العنوان الكامل
                if (!regions[addressInfo.fullAddress]) {
                    regions[addressInfo.fullAddress] = {
                        name: addressInfo.fullAddress,
                        mainCity: addressInfo.mainCity,
                        subRegion: addressInfo.subRegion,
                        isSubRegion: addressInfo.isSubRegion,
                        totalStores: 0,
                        storeTypes: { A: 0, B: 0, D: 0, other: 0 }
                    };
                }

                regions[addressInfo.fullAddress].totalStores++;
                regions[addressInfo.fullAddress].storeTypes[storeType]++;
            }
        });

        return {
            cities: cities,
            regions: regions,
            totalStores: totalStores,
            storesWithValidAddress: storesWithValidAddress,
            storesWithoutAddress: storesWithoutAddress
        };
    }
};
