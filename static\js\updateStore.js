/**
 * وظائف مساعدة لتحديث المتجر
 * هذا الملف يحتوي على وظائف مساعدة لتحديث المتجر
 */

// تعريف متغيرات عامة للاستخدام في هذا الملف
let storeUpdateHelpers = {
    /**
     * التحقق من وجود تغييرات في بيانات المتجر
     * @param {Object} store - المتجر الأصلي
     * @param {Object} newData - البيانات الجديدة
     * @returns {Object} - كائن يحتوي على معلومات التغييرات
     */
    checkForChanges: function(store, newData) {
        const originalLat = parseFloat(store.latitude || store.lat);
        const originalLng = parseFloat(store.longitude || store.lng);
        const newLat = parseFloat(newData.latitude || newData.lat);
        const newLng = parseFloat(newData.longitude || newData.lng);

        return {
            locationChanged: Math.abs(originalLat - newLat) > 0.0001 || Math.abs(originalLng - newLng) > 0.0001,
            nameChanged: newData.name !== (store.name || ''),
            phoneChanged: newData.phone !== (store.phone || ''),
            listChanged: newData.list_id !== (store.list_id || '1'),
            typeChanged: newData.type !== (store.type || 'A'),
            addressChanged: newData.address !== (store.address || ''),
            cityChanged: newData.city_name !== (store.city_name || ''),
            regionChanged: newData.region_name !== (store.region_name || '')
        };
    },

    /**
     * الحصول على الموقع الحالي للمتجر إذا لم يتم تحديد موقع جديد
     * @param {Object} store - المتجر الأصلي
     * @returns {Object} - كائن يحتوي على إحداثيات الموقع
     */
    getCurrentLocation: function(store) {
        return {
            lat: parseFloat(store.latitude || store.lat),
            lng: parseFloat(store.longitude || store.lng)
        };
    },

    /**
     * إنشاء كائن بيانات المتجر للتحديث
     * @param {string} storeId - معرف المتجر
     * @param {string} name - اسم المتجر
     * @param {string} phone - رقم هاتف المتجر
     * @param {Object} location - موقع المتجر
     * @param {string} listId - معرف القائمة
     * @param {File} imageFile - ملف الصورة (اختياري)
     * @param {string} type - نوع المتجر
     * @param {string} address - وصف العنوان
     * @param {string} cityName - اسم المدينة
     * @param {string} regionName - اسم المنطقة
     * @param {string} cityId - معرف المدينة
     * @param {string} regionId - معرف المنطقة
     * @returns {Object} - كائن بيانات المتجر
     */
    createStoreData: function(storeId, name, phone, location, listId, imageFile, type = 'A', address = '', cityName = '', regionName = '', cityId = '', regionId = '') {
        const storeData = {
            id: storeId,
            name,
            phone,
            latitude: location.lat,
            longitude: location.lng,
            list_id: listId,
            type: type,
            address: address,
            city_name: cityName,
            region_name: regionName,
            city_id: cityId,
            region_id: regionId
        };

        if (imageFile) {
            storeData.imageFile = imageFile;
        }

        return storeData;
    }
};

// إضافة مستمع حدث عند تحميل الصفحة للتأكد من أن الكود يعمل بشكل صحيح
document.addEventListener('DOMContentLoaded', function() {
    console.log('تم تحميل ملف updateStore.js بنجاح');
});

