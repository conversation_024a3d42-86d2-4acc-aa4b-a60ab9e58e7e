/**
 * StoreManager class for managing store data and UI interactions
 */
class StoreManager {
    /**
     * Initialize the store manager
     * @param {StoreMap} map - The StoreMap instance
     */
    constructor(map) {
        console.log('Initializing StoreManager');
        this.map = map;
        this.stores = [];
        this.storeListContainer = document.getElementById('storeList');
        console.log('Store list container:', this.storeListContainer);

        // القائمة الحالية المحددة
        this.currentListId = null;

        // التحقق من وجود حاوية قائمة المتاجر
        if (!this.storeListContainer) {
            console.error('Store list container not found! Trying to find it again...');
            // محاولة العثور على الحاوية بعد تحميل الصفحة
            setTimeout(() => {
                this.storeListContainer = document.getElementById('storeList');
                console.log('Store list container (retry):', this.storeListContainer);
            }, 500);
        }

        // تحميل القوائم المتاحة
        this.loadAvailableLists();

        // إعداد مستمعي الأحداث لأزرار القوائم
        this.setupListTabs();
    }

    /**
     * تحميل القوائم المتاحة من الخادم
     */
    loadAvailableLists() {
        console.log('Loading available lists...');
        fetch('/api/lists')
            .then(response => response.json())
            .then(lists => {
                console.log('Available lists:', lists);

                // تحديث القائمة المنسدلة في نموذج إضافة المتجر
                const storeListSelect = document.getElementById('storeListSelect');
                if (storeListSelect) {
                    // حفظ القيمة الحالية
                    const currentValue = storeListSelect.value;

                    // إفراغ القائمة
                    storeListSelect.innerHTML = '';

                    // إضافة القوائم المتاحة
                    lists.forEach(listId => {
                        const option = document.createElement('option');
                        option.value = listId;
                        option.textContent = `القائمة ${listId}`;
                        storeListSelect.appendChild(option);
                    });

                    // إعادة تعيين القيمة الحالية إذا كانت موجودة في القوائم المتاحة
                    if (lists.includes(parseInt(currentValue))) {
                        storeListSelect.value = currentValue;
                    }
                }

                // تحديث قائمة القوائم المنسدلة في التبويب
                const listDropdownMenu = document.getElementById('list-dropdown-menu');
                if (listDropdownMenu) {
                    // إفراغ القائمة مع الاحتفاظ بالعنصر الأخير (جميع القوائم)
                    const allListsItem = listDropdownMenu.querySelector('#list-tab-all').parentNode;
                    const divider = listDropdownMenu.querySelector('hr').parentNode;
                    listDropdownMenu.innerHTML = '';

                    // إضافة القوائم المتاحة
                    lists.forEach(listId => {
                        const li = document.createElement('li');
                        const button = document.createElement('button');
                        button.className = 'dropdown-item';
                        button.id = `list-tab-${listId}`;
                        button.setAttribute('data-bs-toggle', 'tab');
                        button.setAttribute('data-bs-target', '#list-content');
                        button.setAttribute('data-list-id', listId);
                        button.setAttribute('type', 'button');
                        button.setAttribute('role', 'tab');
                        button.textContent = `القائمة ${listId}`;

                        // إضافة مستمع الحدث
                        button.addEventListener('click', (e) => {
                            const listId = parseInt(e.target.dataset.listId);
                            this.currentListId = listId;
                            console.log(`Switching to list ${listId}`);
                            this.loadStores(listId);

                            // تحديث عنوان القائمة
                            const listTitle = document.querySelector('.store-list-header h5');
                            if (listTitle) {
                                listTitle.textContent = `قائمة المتاجر ${listId}`;
                            }
                        });

                        li.appendChild(button);
                        listDropdownMenu.appendChild(li);
                    });

                    // إضافة الفاصل وعنصر جميع القوائم
                    listDropdownMenu.appendChild(divider);
                    listDropdownMenu.appendChild(allListsItem);

                    // إعادة إضافة مستمع الحدث لزر جميع القوائم
                    const allListsButton = listDropdownMenu.querySelector('#list-tab-all');
                    if (allListsButton) {
                        allListsButton.addEventListener('click', () => {
                            this.currentListId = null;
                            console.log('Switching to all lists');
                            this.loadStores();

                            // تحديث عنوان القائمة
                            const listTitle = document.querySelector('.store-list-header h5');
                            if (listTitle) {
                                listTitle.textContent = 'جميع المتاجر';
                            }
                        });
                    }
                }
            })
            .catch(error => console.error('Error loading lists:', error));
    }

    /**
     * إعداد مستمعي الأحداث لأزرار القوائم
     */
    setupListTabs() {
        // إضافة مستمعي الأحداث لأزرار القوائم
        const listTabs = document.querySelectorAll('[id^="list-tab-"]');
        listTabs.forEach(tab => {
            tab.addEventListener('click', (e) => {
                const listId = e.target.dataset.listId ? parseInt(e.target.dataset.listId) : null;
                this.currentListId = listId;
                console.log(`Switching to list ${listId || 'all'}`);
                this.loadStores(listId);

                // تحديث عنوان القائمة
                const listTitle = document.querySelector('.store-list-header h5');
                if (listTitle) {
                    listTitle.textContent = listId ? `قائمة المتاجر ${listId}` : 'جميع المتاجر';
                }
            });
        });
    }

    /**
     * Load stores from the server
     * @param {number|null} listId - Optional list ID to filter stores
     */
    loadStores(listId = null) {
        console.log(`Loading stores from server for list ${listId || 'all'}...`);

        // بناء عنوان URL مع معلمات الاستعلام إذا لزم الأمر
        let url = '/api/stores';
        if (listId !== null) {
            url += `?list_id=${listId}`;
        }

        fetch(url)
            .then(response => {
                console.log('Response status:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('Loaded stores:', data);
                this.stores = data;

                // التأكد من وجود حاوية قائمة المتاجر
                this.storeListContainer = document.getElementById('storeList');
                console.log('Store list container (before render):', this.storeListContainer);

                // عرض المتاجر
                this.renderStores();

                // تحديث عنوان القائمة
                const listTitle = document.querySelector('.store-list-header h5');
                if (listTitle) {
                    listTitle.textContent = listId ? `قائمة المتاجر ${listId}` : 'جميع المتاجر';
                }

                // لا نقوم بالانتقال إلى تبويب القائمة تلقائيًا لجعل حركة القائمة مستقلة عن حقول الإدخال
                console.log('Stores loaded successfully, found', this.stores.length, 'stores');
            })
            .catch(error => console.error('Error loading stores:', error));
    }

    /**
     * Save stores to local storage
     */
    saveStores() {
        localStorage.setItem('stores', JSON.stringify(this.stores));
    }

    /**
     * Add a new store to the server
     * @param {Object} storeData - The store data to add
     */
    addStore(storeData) {
        // إنشاء كائن FormData لإرسال البيانات والملفات
        const formData = new FormData();

        // إضافة البيانات إلى النموذج
        formData.append('name', storeData.name);
        formData.append('phone', storeData.phone || '');
        formData.append('latitude', storeData.lat || storeData.latitude);
        formData.append('longitude', storeData.lng || storeData.longitude);

        // إضافة الصورة إذا كانت موجودة
        if (storeData.imageFile) {
            formData.append('image', storeData.imageFile);
        }

        console.log('Adding store with data:', storeData);

        // إرسال البيانات إلى الخادم
        fetch('/api/stores', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            console.log('Add store response:', response);
            if (response.ok) {
                return response.json();
            } else {
                throw new Error('Failed to add store');
            }
        })
        .then(newStore => {
            console.log('Store added successfully:', newStore);
            // إضافة المتجر الجديد إلى المصفوفة المحلية
            this.stores.unshift(newStore);

            // عرض المتاجر بدون إعادة تحميل من الخادم
            this.renderStores();

            // إعادة تحميل المتاجر من الخادم بعد فترة قصيرة
            setTimeout(() => {
                this.loadStores();
            }, 500);

            return newStore;
        })
        .catch(error => {
            console.error('Error adding store:', error);
            throw error;
        });
    }

    /**
     * Update an existing store
     * @param {string} storeId - The ID of the store to update
     * @param {Object} updatedData - The updated store data
     */
    updateStore(updatedData) {
        // إنشاء كائن FormData لإرسال البيانات والملفات
        const formData = new FormData();

        // إضافة البيانات إلى النموذج
        formData.append('id', updatedData.id);
        formData.append('name', updatedData.name);
        formData.append('phone', updatedData.phone || '');
        formData.append('latitude', updatedData.lat || updatedData.latitude);
        formData.append('longitude', updatedData.lng || updatedData.longitude);

        // إضافة الصورة إذا كانت موجودة
        if (updatedData.imageFile) {
            formData.append('image', updatedData.imageFile);
        }

        // إرسال البيانات إلى الخادم
        fetch('/api/stores', {
            method: 'PUT',
            body: formData
        })
        .then(response => {
            if (response.ok) {
                return response.json();
            } else {
                throw new Error('Failed to update store');
            }
        })
        .then(updatedStore => {
            console.log('Store updated successfully:', updatedStore);

            // تحديث المتجر في المصفوفة المحلية
            const index = this.stores.findIndex(store => store.id === updatedStore.id);
            if (index !== -1) {
                this.stores[index] = updatedStore;

                // عرض المتاجر بدون إعادة تحميل من الخادم
                this.renderStores();
            }

            // إعادة تحميل المتاجر من الخادم بعد فترة قصيرة
            setTimeout(() => {
                this.loadStores();
            }, 500);

            return updatedStore;
        })
        .catch(error => {
            console.error('Error updating store:', error);
            throw error;
        });
    }

    /**
     * Delete a store
     * @param {string} storeId - The ID of the store to delete
     * @returns {Promise} A promise that resolves when the store is deleted
     */
    deleteStore(storeId) {
        // إرسال طلب حذف المتجر إلى الخادم
        return fetch(`/api/stores?id=${storeId}`, {
            method: 'DELETE'
        })
        .then(response => {
            if (response.ok) {
                return response.json();
            } else {
                throw new Error('Failed to delete store');
            }
        })
        .then(result => {
            console.log('Store deleted successfully:', result);

            // تحديث القائمة المحلية بعد الحذف
            this.stores = this.stores.filter(store => store.id !== storeId);

            // عرض المتاجر بدون إعادة تحميل من الخادم
            this.renderStores();

            return result;
        })
        .catch(error => {
            console.error('Error deleting store:', error);
            throw error;
        });
    }

    /**
     * Render all stores on the map and in the list
     */
    renderStores() {
        console.log('Rendering stores:', this.stores);
        // Clear existing markers
        this.map.clearMarkers();

        // Add markers for each store
        this.stores.forEach(store => {
            console.log('Adding marker for store:', store);
            this.map.addStoreMarker(store, (clickedStore) => {
                this.selectStore(clickedStore);
            });
        });

        // Render store list
        this.renderStoreList();
    }

    /**
     * Render the store list in the UI
     */
    renderStoreList() {
        console.log('Rendering store list, container:', this.storeListContainer);

        // التأكد من وجود حاوية قائمة المتاجر
        if (!this.storeListContainer) {
            console.error('Store list container not found! Trying to find it again...');
            this.storeListContainer = document.getElementById('storeList');

            // إذا لم يتم العثور على الحاوية، نحاول مرة أخرى بعد قليل
            if (!this.storeListContainer) {
                console.error('Still could not find store list container. Will retry later.');
                setTimeout(() => {
                    this.storeListContainer = document.getElementById('storeList');
                    console.log('Store list container (retry):', this.storeListContainer);
                    if (this.storeListContainer) {
                        this.renderStoreList();
                    }
                }, 500);
                return;
            }
        }

        // تحديث عدد المتاجر
        const storeCountEl = document.getElementById('storeCount');
        if (storeCountEl) {
            storeCountEl.textContent = this.stores.length;
        }

        this.storeListContainer.innerHTML = '';

        if (this.stores.length === 0) {
            console.log('No stores to display');
            this.storeListContainer.innerHTML = `
                <div class="text-center py-5">
                    <i class="fas fa-store mb-3" style="font-size: 48px; color: var(--bs-secondary);"></i>
                    <p class="text-muted">لم تتم إضافة أي متاجر بعد. انقر على الخريطة لإضافة أول متجر!</p>
                </div>
            `;
            return;
        }

        console.log('Displaying', this.stores.length, 'stores');

        this.stores.forEach(store => {
            console.log('Creating element for store:', store);
            const storeEl = document.createElement('div');
            storeEl.className = 'card mb-3 store-card';
            storeEl.dataset.storeId = store.id;

            // التأكد من وجود الإحداثيات
            const lat = store.latitude || store.lat;
            const lng = store.longitude || store.lng;
            const coordsText = (lat && lng) ? `${parseFloat(lat).toFixed(6)}, ${parseFloat(lng).toFixed(6)}` : 'لا توجد إحداثيات';

            storeEl.innerHTML = `
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="d-flex align-items-start">
                            <div class="form-check ms-2">
                                <input class="form-check-input store-checkbox" type="checkbox" value="${store.id}" id="store-check-${store.id}">
                                <label class="form-check-label" for="store-check-${store.id}">
                                    <span class="visually-hidden">تحديد المتجر</span>
                                </label>
                            </div>
                            <div>
                                <h5 class="card-title">${store.name || 'متجر بدون اسم'}</h5>
                                <h6 class="card-subtitle mb-2 text-muted">${store.phone || 'لا يوجد رقم هاتف'}</h6>
                                <span class="badge bg-info mb-2">القائمة ${store.list_id || 1}</span>
                                <p class="card-text small">
                                    <i class="fas fa-map-marker-alt"></i>
                                    ${coordsText}
                                </p>
                            </div>
                        </div>
                        ${store.image_path ? `
                            <div class="store-image">
                                <img src="/static/${store.image_path}" alt="${store.name || 'متجر'}" class="rounded shadow-sm" style="width: 70px; height: 70px; object-fit: cover; border: 2px solid var(--bs-primary); transition: all 0.3s ease;" onmouseover="this.style.transform='scale(1.1)'" onmouseout="this.style.transform='scale(1.0)'">
                            </div>
                        ` : (store.imageUrl ? `
                            <div class="store-image">
                                <img src="${store.imageUrl}" alt="${store.name || 'متجر'}" class="rounded shadow-sm" style="width: 70px; height: 70px; object-fit: cover; border: 2px solid var(--bs-primary); transition: all 0.3s ease;" onmouseover="this.style.transform='scale(1.1)'" onmouseout="this.style.transform='scale(1.0)'">
                            </div>
                        ` : `
                            <div class="store-image">
                                <div class="rounded bg-secondary d-flex align-items-center justify-content-center" style="width: 70px; height: 70px;">
                                    <i class="fas fa-store text-light" style="font-size: 24px;"></i>
                                </div>
                            </div>
                        `)}
                    </div>
                    <div class="btn-group w-100 mt-2" role="group">
                        <button class="btn btn-sm btn-outline-primary locate-store" data-store-id="${store.id}">
                            <i class="fas fa-map-marked-alt"></i> تحديد الموقع
                        </button>
                        <button class="btn btn-sm btn-outline-secondary edit-store" data-store-id="${store.id}">
                            <i class="fas fa-edit"></i> تعديل
                        </button>
                        <button class="btn btn-sm btn-outline-danger delete-store" data-store-id="${store.id}">
                            <i class="fas fa-trash"></i> حذف
                        </button>
                        <a href="#" class="btn btn-sm btn-outline-success share-whatsapp" data-store-id="${store.id}">
                            <i class="fab fa-whatsapp"></i> مشاركة
                        </a>
                    </div>
                </div>
            `;

            // إضافة مستمعي الأحداث بطريقة أكثر موثوقية
            const storeIdStr = String(store.id);

            // زر تحديد الموقع
            const locateBtn = storeEl.querySelector('.locate-store');
            if (locateBtn) {
                locateBtn.onclick = (e) => {
                    e.preventDefault();
                    console.log('Locate button clicked for store ID:', storeIdStr);
                    this.locateStore(storeIdStr);
                };
            }

            // زر التعديل
            const editBtn = storeEl.querySelector('.edit-store');
            if (editBtn) {
                editBtn.onclick = (e) => {
                    e.preventDefault();
                    console.log('Edit button clicked for store ID:', storeIdStr);
                    this.editStore(storeIdStr);
                };
            }

            // زر الحذف
            const deleteBtn = storeEl.querySelector('.delete-store');
            if (deleteBtn) {
                deleteBtn.onclick = (e) => {
                    e.preventDefault();
                    console.log('Delete button clicked for store ID:', storeIdStr);
                    this.confirmDeleteStore(storeIdStr);
                };
            }

            // زر المشاركة
            const shareBtn = storeEl.querySelector('.share-whatsapp');
            if (shareBtn) {
                shareBtn.onclick = (e) => {
                    e.preventDefault();
                    console.log('Share button clicked for store ID:', storeIdStr);
                    this.shareStoreOnWhatsApp(storeIdStr);
                };
            }

            this.storeListContainer.appendChild(storeEl);
        });

        // تحديث عداد المتاجر المحددة
        this.updateSelectedStoresCount();

        // إضافة مستمعي الأحداث لأزرار المشاركة في النوافذ المنبثقة
        const popupShareButtons = document.querySelectorAll('.leaflet-popup-content .share-whatsapp');
        popupShareButtons.forEach(button => {
            if (button.dataset.hasListener !== 'true') {
                button.onclick = (e) => {
                    e.preventDefault();
                    const storeId = button.dataset.storeId;
                    console.log('Popup share button clicked for store ID:', storeId);
                    this.shareStoreOnWhatsApp(storeId);
                };
                button.dataset.hasListener = 'true';
            }
        });

        console.log('Store list rendered with', this.stores.length, 'stores');
    }

    /**
     * Select a store and populate the form for editing
     * @param {Object} store - The store to select
     */
    selectStore(store) {
        // Populate the form with store data
        document.getElementById('storeId').value = store.id;
        document.getElementById('storeName').value = store.name;
        document.getElementById('storePhone').value = store.phone || '';

        // معالجة الصورة
        const imagePreview = document.getElementById('imagePreview');
        const imagePreviewContainer = document.getElementById('imagePreviewContainer');

        // التحقق من وجود صورة
        if (store.image_path) {
            // عرض الصورة من قاعدة البيانات
            imagePreview.src = `/static/${store.image_path}`;
            imagePreviewContainer.style.display = 'block';
        } else if (store.imageUrl) {
            // عرض الصورة من URL مباشر
            imagePreview.src = store.imageUrl;
            imagePreviewContainer.style.display = 'block';
        } else {
            // لا توجد صورة
            imagePreviewContainer.style.display = 'none';
        }

        // تعيين الموقع المحدد على الخريطة
        const lat = store.latitude || store.lat;
        const lng = store.longitude || store.lng;
        this.map.setSelectedLocation(L.latLng(lat, lng));

        // تحديث واجهة المستخدم
        document.getElementById('formTitle').textContent = 'تعديل المتجر';
        document.getElementById('submitStore').innerHTML = '<i class="fas fa-save me-1"></i> تحديث المتجر';
    }

    /**
     * Locate a store on the map
     * @param {string} storeId - The ID of the store to locate
     */
    locateStore(storeId) {
        const store = this.stores.find(s => s.id === storeId);

        if (store) {
            // Switch to the map tab
            const mapTab = document.getElementById('map-tab');
            if (mapTab) {
                mapTab.click();
            }

            // Fly to store
            this.map.flyToStore(store);
        }
    }

    /**
     * Edit a store
     * @param {string} storeId - The ID of the store to edit
     */
    editStore(storeId) {
        console.log('Editing store with ID:', storeId);

        try {
            // استخدام الدالة الجديدة للبحث عن المتجر
            const store = this.findStoreById(storeId);
            console.log('Found store for editing:', store);

            if (store) {
                // التبديل إلى تبويب الخريطة لرؤية الموقع
                const mapTab = document.getElementById('map-tab');
                if (mapTab) {
                    mapTab.click();
                }

                // تأخير قليل للسماح بتحميل الخريطة
                setTimeout(() => {
                    // تحديد المتجر وملء النموذج
                    this.selectStore(store);

                    // التمرير إلى النموذج
                    setTimeout(() => {
                        const storeForm = document.getElementById('storeForm');
                        if (storeForm) {
                            storeForm.scrollIntoView({ behavior: 'smooth' });
                        } else {
                            console.error('Store form element not found');
                        }
                    }, 200);
                }, 300);
            } else {
                console.error('Store not found with ID:', storeId);
                alert('لم يتم العثور على المتجر');
            }
        } catch (error) {
            console.error('Error editing store:', error);
            alert('حدث خطأ أثناء محاولة تعديل المتجر');
        }
    }

    /**
     * Confirm deletion of a store
     * @param {string} storeId - The ID of the store to delete
     */
    confirmDeleteStore(storeId) {
        const confirmed = confirm('هل أنت متأكد من رغبتك في حذف هذا المتجر؟');

        if (confirmed) {
            this.deleteStore(storeId);
            showAlert('تم حذف المتجر بنجاح', 'success');
        }
    }

    /**
     * Share a store location via WhatsApp
     * @param {string} storeId - The ID of the store to share
     */
    shareStoreOnWhatsApp(storeId) {
        console.log('Sharing single store with ID:', storeId);

        try {
            // استخدام الدالة الجديدة للبحث عن المتجر
            const store = this.findStoreById(storeId);
            console.log('Found store for sharing:', store);

            if (store) {
                // استخدام الدالة الجديدة لمشاركة المتجر عبر واتساب
                const success = this.map.shareStoreViaWhatsApp(store);

                if (success) {
                    console.log('Store shared successfully via WhatsApp');
                } else {
                    console.error('Failed to share store via WhatsApp');
                    alert('فشل فتح واتساب. يرجى المحاولة مرة أخرى.');
                }
            } else {
                console.error('Store not found with ID:', storeId);
                alert('لم يتم العثور على المتجر');
            }
        } catch (error) {
            console.error('Error sharing store:', error);
            alert('حدث خطأ أثناء محاولة مشاركة المتجر');
        }
    }

    /**
     * Share multiple stores via WhatsApp
     * @param {Array} storeIds - Array of store IDs to share
     */
    shareMultipleStoresOnWhatsApp(storeIds) {
        // التحقق من وجود متاجر محددة
        if (!storeIds || storeIds.length === 0) {
            // التحقق من وجود متاجر محددة باستخدام خانات الاختيار
            const checkedBoxes = document.querySelectorAll('.store-checkbox:checked');
            if (checkedBoxes.length > 0) {
                // إذا كانت هناك خانات اختيار محددة، استخدمها مباشرة
                storeIds = Array.from(checkedBoxes).map(cb => cb.value);
                console.log('Using checked boxes directly:', storeIds);
            } else {
                alert('يرجى تحديد متجر واحد على الأقل');
                return;
            }
        }

        console.log('Selected store IDs for sharing:', storeIds);
        console.log('Available stores:', this.stores);

        try {
            // تجميع المتاجر المحددة
            const storesToShare = [];

            // التحقق من كل متجر محدد
            for (const storeId of storeIds) {
                // البحث عن المتجر في قائمة المتاجر
                const store = this.findStoreById(storeId);
                if (store) {
                    storesToShare.push(store);
                    console.log('Added store to share:', store.name);
                }
            }

            console.log('Final stores to share:', storesToShare);

            if (storesToShare.length > 0) {
                // إنشاء رسالة للمشاركة
                let message = 'إليك المتاجر التي اخترتها:\n\n';

                storesToShare.forEach((store, index) => {
                    message += `${index + 1}. ${store.name}\n`;

                    // إضافة عنوان المتجر
                    let storeAddress = '';
                    if (store.city_name && store.region_name) {
                        storeAddress = `${store.city_name} - ${store.region_name}`;
                    } else if (store.address) {
                        storeAddress = store.address;
                    }

                    if (storeAddress) {
                        message += `   العنوان: ${storeAddress}\n`;
                    }

                    message += `   الهاتف: ${store.phone}\n`;
                    message += `   الموقع: https://www.google.com/maps?q=${store.lat},${store.lng}\n\n`;
                });

                // استخدام الدالة الجديدة لفتح واتساب مباشرة
                const success = this.map.openWhatsApp(message);

                if (success) {
                    console.log('Multiple stores shared successfully via WhatsApp');
                    setTimeout(() => {
                        // إظهار رسالة للمستخدم
                        alert('تم فتح رابط واتساب لمشاركة ' + storesToShare.length + ' متجر');
                    }, 500);
                } else {
                    console.error('Failed to open WhatsApp for multiple stores');
                    alert('فشل فتح واتساب. يرجى المحاولة مرة أخرى.');
                }
            } else {
                alert('لم يتم العثور على المتاجر المحددة');
            }
        } catch (error) {
            console.error('Error sharing stores:', error);
            alert('حدث خطأ في مشاركة المتاجر المحددة');
        }
    }

    /**
     * Search stores by name or phone
     * @param {string} query - The search query
     */
    searchStores(query) {
        console.log('Searching stores with query:', query);

        // التبديل إلى تبويب قائمة المتاجر عند البحث
        if (query && query.trim() !== '' && document.getElementById('list-dropdown-menu')) {
            // النقر على زر جميع القوائم للبحث في جميع المتاجر
            const allListsButton = document.getElementById('list-tab-all');
            if (allListsButton && this.currentListId !== null) {
                allListsButton.click();
                return; // سيتم استدعاء البحث مرة أخرى بعد تحميل جميع المتاجر
            }
        }

        // التحقق من وجود بطاقات المتاجر في الصفحة
        const storeCards = document.querySelectorAll('.store-card');

        // إذا كان البحث فارغًا، عرض جميع المتاجر
        if (!query || query.trim() === '') {
            // إظهار جميع البطاقات وإزالة التمييز
            storeCards.forEach(card => {
                card.style.display = 'block';

                // إزالة التمييز من النص
                const nameElement = card.querySelector('.card-title');
                const phoneElement = card.querySelector('.card-subtitle');

                if (nameElement) {
                    const store = this.findStoreById(card.dataset.storeId);
                    if (store) {
                        nameElement.textContent = store.name || 'متجر بدون اسم';
                    }
                }

                if (phoneElement) {
                    const store = this.findStoreById(card.dataset.storeId);
                    if (store) {
                        phoneElement.textContent = store.phone || 'لا يوجد رقم هاتف';
                    }
                }
            });

            // تحديث عدد المتاجر المعروضة
            const storeCountEl = document.getElementById('storeCount');
            if (storeCountEl) {
                storeCountEl.textContent = storeCards.length;
            }

            // إزالة رسالة عدم وجود نتائج إذا كانت موجودة
            const noResultsMsg = document.getElementById('no-results-message');
            if (noResultsMsg) {
                noResultsMsg.remove();
            }

            return;
        }

        // تنفيذ البحث المحلي
        const normalizedQuery = query.toLowerCase().trim();
        const queryWords = normalizedQuery.split(/\s+/).filter(word => word.length > 0);

        let visibleCount = 0;

        // البحث في بطاقات المتاجر الموجودة
        storeCards.forEach(card => {
            const storeId = card.dataset.storeId;
            const store = this.findStoreById(storeId);

            if (store) {
                const storeName = (store.name || '').toLowerCase();
                const storePhone = (store.phone || '').toLowerCase();

                // التحقق من تطابق جميع كلمات البحث مع اسم المتجر أو رقم الهاتف
                const allWordsMatch = queryWords.every(word =>
                    storeName.includes(word) || storePhone.includes(word)
                );

                // إظهار أو إخفاء البطاقة بناءً على نتيجة البحث
                card.style.display = allWordsMatch ? 'block' : 'none';

                if (allWordsMatch) {
                    visibleCount++;

                    // تمييز كلمات البحث في النص
                    const nameElement = card.querySelector('.card-title');
                    const phoneElement = card.querySelector('.card-subtitle');

                    if (nameElement && store.name) {
                        let nameText = store.name;
                        queryWords.forEach(word => {
                            const regex = new RegExp(word, 'gi');
                            nameText = nameText.replace(regex, match => `<mark>${match}</mark>`);
                        });
                        nameElement.innerHTML = nameText;
                    }

                    if (phoneElement && store.phone) {
                        let phoneText = store.phone;
                        queryWords.forEach(word => {
                            const regex = new RegExp(word, 'gi');
                            phoneText = phoneText.replace(regex, match => `<mark>${match}</mark>`);
                        });
                        phoneElement.innerHTML = phoneText;
                    }
                }
            }
        });

        console.log('Search results:', visibleCount, 'stores found');

        // تحديث عدد المتاجر المعروضة
        const storeCountEl = document.getElementById('storeCount');
        if (storeCountEl) {
            storeCountEl.textContent = visibleCount;
        }

        // إظهار رسالة إذا لم يتم العثور على نتائج
        if (visibleCount === 0) {
            // إذا لم توجد نتائج بحث، أضف رسالة
            if (!document.getElementById('no-results-message')) {
                const noResultsMsg = document.createElement('div');
                noResultsMsg.id = 'no-results-message';
                noResultsMsg.className = 'alert alert-info mt-3';
                noResultsMsg.innerHTML = `<i class="fas fa-search me-2"></i> لا توجد نتائج تطابق "${query}"`;
                this.storeListContainer.prepend(noResultsMsg);
            } else {
                document.getElementById('no-results-message').innerHTML =
                    `<i class="fas fa-search me-2"></i> لا توجد نتائج تطابق "${query}"`;
            }
        } else {
            // إزالة رسالة عدم وجود نتائج إذا كانت موجودة
            const noResultsMsg = document.getElementById('no-results-message');
            if (noResultsMsg) {
                noResultsMsg.remove();
            }
        }
                storeEl.className = 'card mb-3 store-card';
                storeEl.dataset.storeId = store.id;

                // التأكد من وجود الإحداثيات
                const lat = store.latitude || store.lat;
                const lng = store.longitude || store.lng;
                const coordsText = (lat && lng) ? `${parseFloat(lat).toFixed(6)}, ${parseFloat(lng).toFixed(6)}` : 'لا توجد إحداثيات';

                // إنشاء محتوى المتجر مع تمييز كلمات البحث
                let nameText = store.name || 'متجر بدون اسم';
                let phoneText = store.phone || 'لا يوجد رقم هاتف';

                // تمييز كلمات البحث في النص
                if (store.name) {
                    queryWords.forEach(word => {
                        const regex = new RegExp(word, 'gi');
                        nameText = nameText.replace(regex, match => `<mark>${match}</mark>`);
                    });
                }

                if (store.phone) {
                    queryWords.forEach(word => {
                        const regex = new RegExp(word, 'gi');
                        phoneText = phoneText.replace(regex, match => `<mark>${match}</mark>`);
                    });
                }

                storeEl.innerHTML = `
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start">
                            <div class="d-flex align-items-start">
                                <div class="form-check ms-2">
                                    <input class="form-check-input store-checkbox" type="checkbox" value="${store.id}" id="store-check-${store.id}">
                                    <label class="form-check-label" for="store-check-${store.id}">
                                        <span class="visually-hidden">تحديد المتجر</span>
                                    </label>
                                </div>
                                <div>
                                    <h5 class="card-title">${nameText}</h5>
                                    <h6 class="card-subtitle mb-2 text-muted">${phoneText}</h6>
                                    <p class="card-text small">
                                        <i class="fas fa-map-marker-alt"></i>
                                        ${coordsText}
                                    </p>
                                </div>
                            </div>
                            ${store.image_path ? `
                                <div class="store-image">
                                    <img src="/static/${store.image_path}" alt="${store.name || 'متجر'}" class="rounded shadow-sm" style="width: 70px; height: 70px; object-fit: cover; border: 2px solid var(--bs-primary); transition: all 0.3s ease;" onmouseover="this.style.transform='scale(1.1)'" onmouseout="this.style.transform='scale(1.0)'">
                                </div>
                            ` : (store.imageUrl ? `
                                <div class="store-image">
                                    <img src="${store.imageUrl}" alt="${store.name || 'متجر'}" class="rounded shadow-sm" style="width: 70px; height: 70px; object-fit: cover; border: 2px solid var(--bs-primary); transition: all 0.3s ease;" onmouseover="this.style.transform='scale(1.1)'" onmouseout="this.style.transform='scale(1.0)'">
                                </div>
                            ` : `
                                <div class="store-image">
                                    <div class="rounded bg-secondary d-flex align-items-center justify-content-center" style="width: 70px; height: 70px;">
                                        <i class="fas fa-store text-light" style="font-size: 24px;"></i>
                                    </div>
                                </div>
                            `)}
                        </div>
                        <div class="btn-group w-100 mt-2" role="group">
                            <button class="btn btn-sm btn-outline-primary locate-store" data-store-id="${store.id}">
                                <i class="fas fa-map-marked-alt"></i> تحديد الموقع
                            </button>
                            <button class="btn btn-sm btn-outline-secondary edit-store" data-store-id="${store.id}">
                                <i class="fas fa-edit"></i> تعديل
                            </button>
                            <button class="btn btn-sm btn-outline-danger delete-store" data-store-id="${store.id}">
                                <i class="fas fa-trash"></i> حذف
                            </button>
                            <a href="#" class="btn btn-sm btn-outline-success share-whatsapp" data-store-id="${store.id}">
                                <i class="fab fa-whatsapp"></i> مشاركة
                            </a>
                        </div>
                    </div>
                `;

                this.storeListContainer.appendChild(storeEl);

                // إضافة مستمعي الأحداث للأزرار
                const locateBtn = storeEl.querySelector('.locate-store');
                if (locateBtn) {
                    locateBtn.addEventListener('click', () => {
                        this.locateStore(store.id);
                    });
                }

                const editBtn = storeEl.querySelector('.edit-store');
                if (editBtn) {
                    editBtn.addEventListener('click', () => {
                        this.selectStore(store);
                    });
                }

                const deleteBtn = storeEl.querySelector('.delete-store');
                if (deleteBtn) {
                    deleteBtn.addEventListener('click', () => {
                        if (confirm('هل أنت متأكد من حذف هذا المتجر؟')) {
                            this.deleteStore(store.id);
                        }
                    });
                }

                const shareBtn = storeEl.querySelector('.share-whatsapp');
                if (shareBtn) {
                    shareBtn.addEventListener('click', (e) => {
                        e.preventDefault();
                        this.map.shareStoreViaWhatsApp(store);
                    });
                }
            });
        }
    }

    /**
     * Find a store by its ID
     * @param {string|number} storeId - The ID of the store to find
     * @returns {Object|null} The found store or null if not found
     */
    findStoreById(storeId) {
        if (!storeId) return null;

        // تحويل معرف المتجر إلى نص للمقارنة
        const storeIdStr = String(storeId).trim();

        // البحث عن المتجر باستخدام المقارنة النصية
        for (const store of this.stores) {
            if (String(store.id).trim() === storeIdStr) {
                return store;
            }
        }

        console.warn(`Store with ID ${storeId} not found`);
        return null;
    }

    /**
     * Get selected store IDs
     * @returns {Array} Array of selected store IDs
     */
    getSelectedStoreIds() {
        try {
            const selectedStores = [];

            // الحصول على جميع خانات الاختيار المحددة
            const checkboxes = document.querySelectorAll('.store-checkbox:checked');
            console.log('Found checked checkboxes:', checkboxes.length);

            // طباعة جميع خانات الاختيار للتصحيح
            checkboxes.forEach((checkbox, index) => {
                console.log(`Checkbox ${index}:`, checkbox);
                console.log(`  - value:`, checkbox.value);
                console.log(`  - id:`, checkbox.id);
                console.log(`  - checked:`, checkbox.checked);
            });

            // التحقق من كل خانة اختيار وإضافة قيمتها إلى القائمة
            for (let i = 0; i < checkboxes.length; i++) {
                const checkbox = checkboxes[i];
                if (checkbox.checked && checkbox.value && checkbox.value.trim() !== '') {
                    const storeId = checkbox.value.trim();
                    selectedStores.push(storeId);
                    console.log('Added store ID to selection:', storeId);

                    // التحقق من وجود المتجر في قائمة المتاجر
                    const store = this.findStoreById(storeId);
                    if (store) {
                        console.log('  - Found matching store:', store.name);
                    } else {
                        console.warn('  - No matching store found for ID:', storeId);
                    }
                } else {
                    console.warn('Checkbox has invalid value or is not checked:', checkbox);
                }
            }

            if (selectedStores.length === 0) {
                console.warn('No stores selected or checkboxes not found');
            }

            console.log('Final selected store IDs:', selectedStores);
            return selectedStores;
        } catch (error) {
            console.error('Error getting selected store IDs:', error);
            return [];
        }
    }

    /**
     * Update selected stores count
     */
    updateSelectedStoresCount() {
        const checkboxes = document.querySelectorAll('.store-checkbox:checked');
        const count = checkboxes.length;
        const countElem = document.getElementById('selectedStoresCount');
        const shareButton = document.getElementById('shareSelectedStores');

        console.log('Updating selected stores count:', count);

        if (countElem) {
            countElem.textContent = count;
        } else {
            console.warn('Selected stores count element not found');
        }

        if (shareButton) {
            if (count > 0) {
                shareButton.disabled = false;
                shareButton.classList.add('btn-success');
                shareButton.classList.remove('btn-outline-success');
            } else {
                shareButton.disabled = true;
                shareButton.classList.remove('btn-success');
                shareButton.classList.add('btn-outline-success');
            }
        } else {
            console.warn('Share button element not found');
        }

        // تحديث المتاجر المحددة بصريًا
        document.querySelectorAll('.store-card').forEach(card => {
            if (checkboxes.length > 0) {
                const storeId = card.dataset.storeId;
                const isChecked = Array.from(checkboxes).some(cb => cb.value === storeId);

                if (isChecked) {
                    card.classList.add('selected-store');
                } else {
                    card.classList.remove('selected-store');
                }
            } else {
                card.classList.remove('selected-store');
            }
        });
    }

    /**
     * Search stores by name or phone number
     * @param {string} query - The search query
     */

}
