/**
 * مدير صلاحيات المستخدمين
 * يتحكم في تفعيل/تعطيل الأزرار بناءً على دور المستخدم
 */

class PermissionManager {
    constructor() {
        // تعريف الأدوار
        this.ROLES = {
            ADMIN: 1,
            MARKETER: 2,
            VISITOR: 3
        };

        // الحصول على دور المستخدم الحالي
        this.currentUserRole = this.getCurrentUserRole();

        console.log('تم تهيئة مدير الصلاحيات. دور المستخدم الحالي:', this.currentUserRole);

        // تطبيق الصلاحيات عند تحميل الصفحة
        this.applyPermissions();

        // إضافة مستمع للتغييرات في DOM لتطبيق الصلاحيات على العناصر الجديدة
        this.observeDOMChanges();
    }

    /**
     * الحصول على دور المستخدم الحالي من عنصر meta
     */
    getCurrentUserRole() {
        const roleElement = document.querySelector('meta[name="user-role"]');
        return roleElement ? parseInt(roleElement.getAttribute('content')) : null;
    }

    /**
     * التحقق مما إذا كان المستخدم لديه صلاحية معينة
     * @param {Array} allowedRoles - الأدوار المسموح لها
     * @returns {Boolean} - هل المستخدم لديه صلاحية
     */
    hasPermission(allowedRoles) {
        if (!this.currentUserRole) return false;
        return allowedRoles.includes(this.currentUserRole);
    }

    /**
     * تطبيق الصلاحيات على جميع الأزرار في الصفحة
     */
    applyPermissions() {
        // تطبيق الصلاحيات على أزرار إدارة المتاجر
        this.applyStoreButtonsPermissions();

        // تطبيق الصلاحيات على أزرار إدارة المستخدمين
        this.applyUserButtonsPermissions();
    }

    /**
     * تطبيق الصلاحيات على أزرار إدارة المتاجر
     */
    applyStoreButtonsPermissions() {
        // أزرار إضافة المتاجر (واجهة الكمبيوتر والهاتف)
        const addStoreButtons = document.querySelectorAll('#add-store-btn, #mobile-add-store, #submitStore, #mobile-submit-store');
        this.applyButtonPermission(addStoreButtons, [this.ROLES.ADMIN, this.ROLES.MARKETER]);

        // أزرار تعديل المتاجر (واجهة الكمبيوتر والهاتف)
        const editStoreButtons = document.querySelectorAll('.edit-store, #editStore, #mobile-edit-store');
        this.applyButtonPermission(editStoreButtons, [this.ROLES.ADMIN, this.ROLES.MARKETER]);

        // أزرار حذف المتاجر (واجهة الكمبيوتر والهاتف)
        const deleteStoreButtons = document.querySelectorAll('.delete-store, #deleteSelectedStores, #confirmDelete');
        this.applyButtonPermission(deleteStoreButtons, [this.ROLES.ADMIN, this.ROLES.MARKETER]);

        // تعطيل نماذج إضافة المتاجر للزائر
        if (this.currentUserRole === this.ROLES.VISITOR) {
            // تعطيل حقول نموذج إضافة المتجر في واجهة الكمبيوتر
            const desktopFormInputs = document.querySelectorAll('#storeForm input, #storeForm select, #storeForm textarea');
            this.disableFormElements(desktopFormInputs);

            // تعطيل حقول نموذج إضافة المتجر في واجهة الهاتف
            const mobileFormInputs = document.querySelectorAll('#mobile-store-form input, #mobile-store-form select, #mobile-store-form textarea');
            this.disableFormElements(mobileFormInputs);

            // تعطيل تبويب الإضافة في واجهة الهاتف
            const mobileAddTab = document.querySelector('.mobile-tab-btn[data-tab="mobile-form-tab"]');
            if (mobileAddTab) {
                this.applyButtonPermission([mobileAddTab], [this.ROLES.ADMIN, this.ROLES.MARKETER]);
            }
        }
    }

    /**
     * تطبيق الصلاحيات على أزرار إدارة المستخدمين
     */
    applyUserButtonsPermissions() {
        // أزرار إدارة المستخدمين (فقط للمدير)
        const userManagementButtons = document.querySelectorAll('.edit-user-btn, .delete-user-btn, #add-user-btn');
        this.applyButtonPermission(userManagementButtons, [this.ROLES.ADMIN]);
    }

    /**
     * تطبيق الصلاحية على مجموعة من الأزرار
     * @param {NodeList} buttons - مجموعة الأزرار
     * @param {Array} allowedRoles - الأدوار المسموح لها
     */
    applyButtonPermission(buttons, allowedRoles) {
        if (!buttons || buttons.length === 0) return;

        const hasPermission = this.hasPermission(allowedRoles);

        buttons.forEach(button => {
            if (!hasPermission) {
                // تعطيل الزر مع الاحتفاظ به ظاهراً
                button.classList.add('disabled-button');

                // إضافة سمة للإشارة إلى أن الزر معطل بسبب الصلاحيات
                button.setAttribute('data-permission-disabled', 'true');

                // إضافة تلميح لتوضيح سبب التعطيل
                button.setAttribute('data-bs-toggle', 'tooltip');
                button.setAttribute('data-bs-placement', 'top');
                button.setAttribute('title', '');

                // إضافة مستمع حدث لمنع الإجراء الافتراضي
                this.addDisabledButtonListener(button);
            } else {
                // إزالة التعطيل إذا كان موجوداً
                button.classList.remove('disabled-button');
                button.removeAttribute('data-permission-disabled');
            }
        });

        // تهيئة tooltips من Bootstrap
        if (typeof bootstrap !== 'undefined' && bootstrap.Tooltip) {
            const tooltips = document.querySelectorAll('[data-bs-toggle="tooltip"]');
            tooltips.forEach(tooltip => {
                new bootstrap.Tooltip(tooltip);
            });
        }
    }

    /**
     * إضافة مستمع حدث للأزرار المعطلة
     * @param {Element} button - الزر المعطل
     */
    addDisabledButtonListener(button) {
        // إزالة المستمعات السابقة لتجنب التكرار
        button.removeEventListener('click', this.handleDisabledButtonClick);

        // إضافة مستمع جديد
        button.addEventListener('click', this.handleDisabledButtonClick);
    }

    /**
     * تعطيل عناصر النموذج
     * @param {NodeList} elements - عناصر النموذج التي سيتم تعطيلها
     */
    disableFormElements(elements) {
        if (!elements || elements.length === 0) return;

        elements.forEach(element => {
            // تعطيل العنصر
            element.disabled = true;
            element.classList.add('disabled-input');

            // إضافة تلميح لتوضيح سبب التعطيل
            element.setAttribute('data-bs-toggle', 'tooltip');
            element.setAttribute('data-bs-placement', 'top');
            element.setAttribute('title', '');

            // إضافة مستمع حدث للنقر لإظهار رسالة
            element.addEventListener('click', this.handleDisabledFormElementClick);
        });

        // تهيئة tooltips من Bootstrap
        if (typeof bootstrap !== 'undefined' && bootstrap.Tooltip) {
            const tooltips = document.querySelectorAll('[data-bs-toggle="tooltip"]');
            tooltips.forEach(tooltip => {
                new bootstrap.Tooltip(tooltip);
            });
        }
    }

    /**
     * معالجة النقر على عنصر نموذج معطل
     * @param {Event} event - حدث النقر
     */
    handleDisabledFormElementClick(event) {
        // منع الإجراء الافتراضي
        event.preventDefault();
        event.stopPropagation();
        return false;
    }

    /**
     * معالجة النقر على زر معطل
     * @param {Event} event - حدث النقر
     */
    handleDisabledButtonClick(event) {
        // منع الإجراء الافتراضي
        event.preventDefault();
        event.stopPropagation();

        // إظهار تأثير بصري للنقر (اهتزاز خفيف)
        const button = event.currentTarget;
        button.classList.add('shake-animation');

        // إزالة تأثير الاهتزاز بعد انتهائه
        setTimeout(() => {
            button.classList.remove('shake-animation');
        }, 500);



        return false;
    }

    /**
     * مراقبة التغييرات في DOM لتطبيق الصلاحيات على العناصر الجديدة
     */
    observeDOMChanges() {
        // إنشاء مراقب للتغييرات في DOM
        const observer = new MutationObserver((mutations) => {
            let shouldApplyPermissions = false;

            // التحقق من إضافة عناصر جديدة
            mutations.forEach(mutation => {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    shouldApplyPermissions = true;
                }
            });

            // تطبيق الصلاحيات إذا تم إضافة عناصر جديدة
            if (shouldApplyPermissions) {
                this.applyPermissions();
            }
        });

        // بدء المراقبة
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }
}

// تهيئة مدير الصلاحيات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    // إنشاء نسخة عالمية من مدير الصلاحيات
    window.permissionManager = new PermissionManager();

    // تطبيق الصلاحيات على العناصر الموجودة
    window.permissionManager.applyPermissions();

    console.log('تم تهيئة مدير الصلاحيات بنجاح');
});
