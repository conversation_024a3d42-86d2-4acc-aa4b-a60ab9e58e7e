/**
 * نظام إشعارات المتاجر المعلقة - LOACKER Application
 * يقوم بالتحقق من وجود متاجر معلقة وعرض عددها في شارة الإشعارات
 */
class PendingStoresNotifier {
    constructor() {
        this.pendingStoresCount = 0;
        this.badges = {
            desktopBell: document.getElementById('pendingStoresBadgeBell'),
            mobileBell: document.getElementById('mobilePendingStoresBadgeBell')
        };
        this.bellButton = document.getElementById('notificationBell');
        this.checkInterval = 60000; // التحقق كل دقيقة
        this.intervalId = null;
    }

    /**
     * تهيئة نظام الإشعارات
     */
    init() {
        // التحقق من وجود المستخدم المدير
        const userRoleElement = document.querySelector('meta[name="user-role"]');
        if (!userRoleElement || userRoleElement.getAttribute('content') !== '1') {
            console.log('المستخدم ليس مديرًا، لن يتم تفعيل نظام إشعارات المتاجر المعلقة');
            return;
        }

        console.log('🔔 تهيئة نظام إشعارات المتاجر المعلقة');

        // التحقق الأولي من المتاجر المعلقة
        this.checkPendingStores();

        // بدء التحقق الدوري
        this.startPeriodicCheck();

        // إضافة مستمعات الأحداث
        this.setupEventListeners();
    }

    /**
     * بدء التحقق الدوري من المتاجر المعلقة
     */
    startPeriodicCheck() {
        // إيقاف أي فاصل زمني سابق
        if (this.intervalId) {
            clearInterval(this.intervalId);
        }

        // بدء فاصل زمني جديد
        this.intervalId = setInterval(() => {
            this.checkPendingStores();
        }, this.checkInterval);

        console.log(`تم بدء التحقق الدوري من المتاجر المعلقة كل ${this.checkInterval / 1000} ثانية`);
    }

    /**
     * إيقاف التحقق الدوري
     */
    stopPeriodicCheck() {
        if (this.intervalId) {
            clearInterval(this.intervalId);
            this.intervalId = null;
            console.log('تم إيقاف التحقق الدوري من المتاجر المعلقة');
        }
    }

    /**
     * التحقق من وجود متاجر معلقة
     */
    checkPendingStores() {
        fetch('/api/pending-stores')
            .then(response => {
                if (!response.ok) {
                    throw new Error(`فشل في جلب المتاجر المعلقة: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    this.updateNotificationBadges(data.stores.length);
                }
            })
            .catch(error => {
                console.error('خطأ في التحقق من المتاجر المعلقة:', error);
            });
    }

    /**
     * تحديث شارات الإشعارات
     * @param {number} count - عدد المتاجر المعلقة
     */
    updateNotificationBadges(count) {
        this.pendingStoresCount = count;

        // تحديث جميع الشارات
        Object.values(this.badges).forEach(badge => {
            if (badge) {
                badge.textContent = count;

                if (count > 0) {
                    badge.classList.remove('d-none');
                } else {
                    badge.classList.add('d-none');
                }
            }
        });

        // تحديث حالة زر الجرس
        if (this.bellButton) {
            if (count > 0) {
                this.bellButton.classList.add('has-notifications');

                // إضافة تأثير الاهتزاز مرة واحدة
                this.bellButton.classList.remove('has-notifications');
                void this.bellButton.offsetWidth; // إعادة تشغيل الرسوم المتحركة
                this.bellButton.classList.add('has-notifications');
            } else {
                this.bellButton.classList.remove('has-notifications');
            }
        }

        // إذا كان هناك متاجر معلقة وكان هناك كائن إشعارات عام، عرض إشعار
        if (count > 0 && window.notificationManager && this.lastCount !== count) {
            window.notificationManager.show(
                'متاجر معلقة',
                `يوجد ${count} متجر معلق بانتظار الموافقة`,
                'warning',
                5000
            );
        }

        // تخزين العدد الحالي للمقارنة في المرة القادمة
        this.lastCount = count;
    }

    /**
     * إعداد مستمعات الأحداث
     */
    setupEventListeners() {
        // إضافة مستمع حدث للنقر على زر الجرس
        if (this.bellButton) {
            this.bellButton.addEventListener('click', (event) => {
                // إيقاف التحقق الدوري مؤقتًا لمدة 5 ثوانٍ بعد النقر على الجرس
                this.stopPeriodicCheck();
                setTimeout(() => {
                    this.startPeriodicCheck();
                }, 5000);
            });
        }

        // إضافة مستمع حدث للتبديل بين علامات التبويب
        document.addEventListener('visibilitychange', () => {
            if (document.visibilityState === 'visible') {
                // عند العودة إلى الصفحة، التحقق فورًا من المتاجر المعلقة
                this.checkPendingStores();
            }
        });
    }
}

// إنشاء كائن من نظام إشعارات المتاجر المعلقة
const pendingStoresNotifier = new PendingStoresNotifier();

// تهيئة النظام عند اكتمال تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    pendingStoresNotifier.init();
});
