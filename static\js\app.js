/**
 * تطبيق خريطة المتاجر المحلية
 * الملف الرئيسي للتطبيق
 */

// متغيرات عامة
let storeMap;
let storeManager;

// Cache for API responses
const apiCache = {
    stores: new Map(),
    lists: new Map(),
    lastUpdated: new Map(),

    // Get cached data
    get: function(key) {
        const data = this[key].get('data');
        const timestamp = this.lastUpdated.get(key);
        if (!data || !timestamp) return null;

        // Check if cache is still valid (5 minutes)
        const now = Date.now();
        if (now - timestamp > 5 * 60 * 1000) {
            this[key].delete('data');
            this.lastUpdated.delete(key);
            return null;
        }

        return data;
    },

    // Set cached data
    set: function(key, data) {
        this[key].set('data', data);
        this.lastUpdated.set(key, Date.now());
    }
};

// تهيئة التطبيق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    // التحقق من نوع الجهاز لتهيئة الواجهة المناسبة
    const isMobileDevice = document.body.classList.contains('mobile-device');
    console.log(`نوع الجهاز: ${isMobileDevice ? 'هاتف محمول' : 'كمبيوتر'}`);

    // تهيئة الخريطة المناسبة بناءً على نوع الجهاز
    if (isMobileDevice) {
        // تهيئة خريطة الهاتف
        if (document.getElementById('mobile-map')) {
            storeMap = new StoreMap('mobile-map');
            console.log('تم تهيئة خريطة الهاتف');
        }
    } else {
        // تهيئة خريطة الكمبيوتر
        storeMap = new StoreMap('map');
        console.log('تم تهيئة خريطة الكمبيوتر');

        // تهيئة خريطة الموبايل إذا كانت موجودة (للاختبار فقط)
        if (document.getElementById('mobile-map')) {
            storeMap.initMobileMap('mobile-map');
        }
    }

    // تهيئة مدير المتاجر
    storeManager = new StoreManager(storeMap);

    // تحميل المتاجر من قاعدة البيانات بعد تأخير قصير للتأكد من تحميل الصفحة بالكامل
    console.log('Will load stores after a short delay...');
    setTimeout(() => {
        console.log('Loading stores on application start...');
        storeManager.loadStores();
    }, 500);

    // إعداد مستمعي الأحداث
    setupEventListeners();

    // إعداد وظيفة البحث
    setupSearch();

    // إعداد مستمعي أحداث نافذة تأكيد تغيير الموقع
    setupLocationChangeHandlers();

    // لا نحتاج لإعداد مستمعي الأحداث للموبايل منفصلين في هذه المرحلة

    // معالجة تغيير حجم النافذة للخرائط
    window.addEventListener('resize', () => {
        if (storeMap && storeMap.map) {
            storeMap.map.invalidateSize();
        }
        if (storeMap && storeMap.mobileMap) {
            storeMap.mobileMap.invalidateSize();
        }
    });
});

/**
 * إعداد مستمعي الأحداث
 */
function setupEventListeners() {
    // زر قائمة المتاجر المباشر
    const listTabDirect = document.getElementById('list-tab-direct');
    if (listTabDirect) {
        listTabDirect.addEventListener('click', () => {
            // عرض جميع المتاجر عند النقر على زر قائمة المتاجر
            console.log('Direct list tab clicked, showing all stores');
            storeManager.currentListId = null;
            storeManager.loadStores();

            // تحديث عنوان القائمة
            const listTitle = document.querySelector('.store-list-header h5');
            if (listTitle) {
                listTitle.textContent = 'جميع المتاجر';
            }

            // تحديث حالة زر جميع القوائم
            const viewAllListsBtn = document.getElementById('view-all-lists');
            if (viewAllListsBtn) {
                viewAllListsBtn.classList.add('active');
                viewAllListsBtn.classList.remove('btn-outline-primary');
                viewAllListsBtn.classList.add('btn-primary');
            }
        });
    }

    // زر طي/فتح النموذج على الهواتف النقالة
    const toggleFormBtn = document.getElementById('toggleFormBtn');
    const storeFormContainer = document.getElementById('storeFormContainer');

    if (toggleFormBtn && storeFormContainer) {
        toggleFormBtn.addEventListener('click', () => {
            // تبديل حالة عرض النموذج
            if (storeFormContainer.style.display === 'none') {
                storeFormContainer.style.display = 'block';
                toggleFormBtn.innerHTML = '<i class="fas fa-chevron-up"></i>';
            } else {
                storeFormContainer.style.display = 'none';
                toggleFormBtn.innerHTML = '<i class="fas fa-chevron-down"></i>';
            }
        });

        // التحقق من حجم الشاشة عند تحميل الصفحة
        function checkScreenSize() {
            if (window.innerWidth < 768) {
                // إخفاء النموذج افتراضيًا على الشاشات الصغيرة
                storeFormContainer.style.display = 'none';
                toggleFormBtn.innerHTML = '<i class="fas fa-chevron-down"></i>';
            } else {
                // إظهار النموذج على الشاشات الكبيرة
                storeFormContainer.style.display = 'block';
            }
        }

        // التحقق من حجم الشاشة عند تحميل الصفحة وعند تغيير حجم النافذة
        checkScreenSize();
        window.addEventListener('resize', checkScreenSize);
    }

    // زر عرض القائمة على الهواتف النقالة (سيتم إعداده لاحقًا في الدالة setupMobileFormHandlers)

    // زر تعديل المتجر
    const editStoreBtn = document.getElementById('editStore');
    if (editStoreBtn) {
        editStoreBtn.addEventListener('click', () => {
            // الحصول على معرف المتجر من النموذج
            const storeId = document.getElementById('storeId').value;
            if (storeId) {
                // استدعاء دالة معالجة تحديث المتجر
                handleStoreUpdate(storeId);
            }
        });
    }

    // زر تعديل المتجر في واجهة الموبايل
    const mobileEditStoreBtn = document.getElementById('mobileEditStore');
    if (mobileEditStoreBtn) {
        mobileEditStoreBtn.addEventListener('click', () => {
            // التحقق من صلاحيات المستخدم
            const userRoleElement = document.querySelector('meta[name="user-role"]');
            const userRole = userRoleElement ? parseInt(userRoleElement.getAttribute('content')) : null;

            // إذا كان المستخدم زائراً (الدور 3)، لا يمكنه تعديل المتاجر
            if (userRole === 3) {
                return;
            }

            // الحصول على معرف المتجر من النموذج
            const storeId = document.getElementById('mobileStoreId').value;
            if (storeId) {
                // التأكد من وجود مدير المتاجر
                if (storeManager) {
                    // استدعاء دالة تعديل المتجر
                    storeManager.editStore(storeId);
                }
            }
        });
    }

    // زر تعديل المتجر في واجهة الموبايل الثانية
    const mobileEditStoreBtn2 = document.getElementById('mobile-edit-store');
    if (mobileEditStoreBtn2) {
        mobileEditStoreBtn2.addEventListener('click', () => {
            // التحقق من صلاحيات المستخدم
            const userRoleElement = document.querySelector('meta[name="user-role"]');
            const userRole = userRoleElement ? parseInt(userRoleElement.getAttribute('content')) : null;

            // إذا كان المستخدم زائراً (الدور 3)، لا يمكنه تعديل المتاجر
            if (userRole === 3) {
                return;
            }

            // الحصول على معرف المتجر من النموذج
            const storeId = document.getElementById('mobile-store-id').value;
            if (storeId) {
                // التأكد من وجود مدير المتاجر
                if (storeManager) {
                    // استدعاء دالة تعديل المتجر
                    storeManager.editStore(storeId);
                }
            }
        });
    }

    // نموذج المتجر
    const storeForm = document.getElementById('storeForm');

    // مراقبة تغييرات الحقول لإظهار أو إخفاء زر مسح النموذج
    const storeNameInput = document.getElementById('storeName');
    const storePhoneInput = document.getElementById('storePhone');
    const clearFormBtn = document.getElementById('clearForm');

    // دالة للتحقق من ملء الحقول
    function checkFormFields() {
        if (storeNameInput && storePhoneInput && clearFormBtn) {
            // إظهار زر مسح النموذج بمجرد الضغط على أي حقل
            clearFormBtn.classList.remove('d-none');

            // الحصول على قيم الحقول الإضافية
            const storeTypeValue = document.getElementById('storeType') ? document.getElementById('storeType').value : 'A';
            const storeAddressValue = document.getElementById('storeAddress') ? document.getElementById('storeAddress').value.trim() : '';
            const storeFullAddressValue = document.getElementById('storeFullAddress') ? document.getElementById('storeFullAddress').value.trim() : '';

            // إذا كانت جميع الحقول فارغة ولم يكن هناك معرف متجر، يمكن إخفاء الزر مرة أخرى
            // لكن فقط إذا لم يكن هناك أي تفاعل مع النموذج
            if (storeNameInput.value.trim() === '' &&
                storePhoneInput.value.trim() === '' &&
                storeAddressValue === '' &&
                storeFullAddressValue === '' &&
                storeTypeValue === 'A' &&
                document.getElementById('storeId').value.trim() === '' &&
                !document.activeElement.closest('#storeForm')) {
                clearFormBtn.classList.add('d-none');
            }

            // التحقق من وجود معرف متجر (في وضع التعديل)
            const storeId = document.getElementById('storeId').value;
            if (storeId && storeId.trim() !== '') {
                // إظهار زر التعديل
                const editButton = document.getElementById('editStore');
                if (editButton) {
                    editButton.classList.remove('d-none');
                }
            } else {
                // إخفاء زر التعديل إذا لم يكن هناك معرف متجر
                const editButton = document.getElementById('editStore');
                if (editButton) {
                    editButton.classList.add('d-none');
                }
            }
        }
    }

    // إضافة مستمعي الأحداث للحقول
    if (storeNameInput) {
        storeNameInput.addEventListener('input', checkFormFields);
        storeNameInput.addEventListener('focus', checkFormFields); // إظهار الأزرار عند الضغط على الحقل
    }

    if (storePhoneInput) {
        storePhoneInput.addEventListener('input', checkFormFields);
        storePhoneInput.addEventListener('focus', checkFormFields); // إظهار الأزرار عند الضغط على الحقل
    }

    // إضافة مستمع حدث لحقل القائمة
    const storeListSelect = document.getElementById('storeListSelect');
    if (storeListSelect) {
        storeListSelect.addEventListener('focus', checkFormFields);
    }

    // إضافة مستمع حدث لحقل نوع المتجر
    const storeType = document.getElementById('storeType');
    if (storeType) {
        storeType.addEventListener('focus', checkFormFields);
        storeType.addEventListener('change', checkFormFields);
    }

    // إضافة مستمع حدث لحقل وصف العنوان
    const storeAddress = document.getElementById('storeAddress');
    if (storeAddress) {
        storeAddress.addEventListener('input', checkFormFields);
        storeAddress.addEventListener('focus', checkFormFields);
    }

    // إضافة مستمع حدث لقائمة المدن المنسدلة
    const citySelect = document.getElementById('citySelect');
    const districtSelect = document.getElementById('districtSelect');

    // إصلاح مشكلة ظهور القائمة المنسدلة فوق الصندوق
    if (districtSelect) {
        districtSelect.style.position = 'relative';
        districtSelect.style.zIndex = '1000';
    }

    // الحصول على حقل عنوان المتجر
    const storeFullAddress = document.getElementById('storeFullAddress');

    if (citySelect) {
        citySelect.addEventListener('change', function() {
            // تفعيل الأزرار عند تغيير المدينة
            checkFormFields();
            const selectedCity = this.value;

            // تفعيل/تعطيل قائمة المناطق بناءً على المدينة المختارة
            if (districtSelect) {
                if (selectedCity) {
                    // تفعيل قائمة المناطق
                    districtSelect.disabled = false;

                    // إخفاء جميع مجموعات المناطق
                    const optgroups = districtSelect.querySelectorAll('optgroup');
                    optgroups.forEach(optgroup => {
                        optgroup.style.display = 'none';
                    });

                    // إظهار مجموعة المناطق للمدينة المختارة
                    let cityClassName = '';
                    switch (selectedCity) {
                        case 'طرابلس':
                            cityClassName = 'tripoli';
                            break;
                        case 'بنغازي':
                            cityClassName = 'benghazi';
                            break;
                        case 'مصراتة':
                            cityClassName = 'misrata';
                            break;
                        default:
                            cityClassName = selectedCity.toLowerCase().replace(/\s+/g, '-');
                    }

                    // تحديث حقل عنوان المتجر سيتم أسفل

                    const cityDistricts = districtSelect.querySelector(`.${cityClassName}-districts`);
                    if (cityDistricts) {
                        cityDistricts.style.display = 'block';
                    }
                } else {
                    // تعطيل قائمة المناطق إذا لم يتم اختيار مدينة
                    districtSelect.disabled = true;
                    districtSelect.value = '';
                }
            }

            // تحديث حقل عنوان المتجر
            if (storeFullAddress && selectedCity) {
                const selectedDistrict = districtSelect ? districtSelect.value : '';
                if (selectedDistrict) {
                    storeFullAddress.value = selectedCity + ' - ' + selectedDistrict;
                } else {
                    storeFullAddress.value = selectedCity;
                }
            }
        });
    }

    // إضافة مستمع حدث لقائمة المناطق
    if (districtSelect) {
        districtSelect.addEventListener('change', function() {
            // تفعيل الأزرار عند تغيير المنطقة
            checkFormFields();

            const selectedDistrict = this.value;
            const selectedCity = citySelect ? citySelect.value : '';

            // تحديث حقل عنوان المتجر
            if (storeFullAddress) {
                if (selectedCity && selectedDistrict) {
                    storeFullAddress.value = selectedCity + ' - ' + selectedDistrict;
                } else if (selectedCity) {
                    storeFullAddress.value = selectedCity;
                } else {
                    storeFullAddress.value = '';
                }
            }
        });
    }

    // إضافة مستمع حدث لحقل الصورة
    const imageUpload = document.getElementById('imageUpload');
    if (imageUpload) {
        imageUpload.addEventListener('focus', checkFormFields);
    }

    // إضافة مستمع حدث لحقل عنوان المتجر
    if (storeFullAddress) {
        storeFullAddress.addEventListener('focus', checkFormFields);
        storeFullAddress.addEventListener('click', checkFormFields);
    }

    // مراقبة تغييرات الحقول في واجهة الموبايل
    const mobileStoreNameInput = document.getElementById('mobileStoreName');
    const mobileStorePhoneInput = document.getElementById('mobileStorePhone');
    const mobileClearFormBtn = document.getElementById('mobileClearForm');

    // دالة للتحقق من ملء حقول الموبايل
    function checkMobileFormFields() {
        if (mobileStoreNameInput && mobileStorePhoneInput && mobileClearFormBtn) {
            // الحصول على قيم الحقول الإضافية
            const mobileStoreTypeValue = document.getElementById('mobile-store-type') ? document.getElementById('mobile-store-type').value : 'A';
            const mobileStoreAddressValue = document.getElementById('mobile-store-address') ? document.getElementById('mobile-store-address').value.trim() : '';
            const mobileStoreFullAddressValue = document.getElementById('mobile-store-full-address') ? document.getElementById('mobile-store-full-address').value.trim() : '';

            // التحقق من وجود بيانات في النموذج لإظهار أو إخفاء زر مسح النموذج
            if (mobileStoreNameInput.value.trim() !== '' ||
                mobileStorePhoneInput.value.trim() !== '' ||
                mobileStoreAddressValue !== '' ||
                mobileStoreFullAddressValue !== '' ||
                mobileStoreTypeValue !== 'A' ||
                document.getElementById('mobileStoreId').value.trim() !== '') {
                // إظهار زر مسح النموذج فقط إذا كان هناك بيانات
                mobileClearFormBtn.classList.remove('d-none');
            } else {
                // إخفاء زر مسح النموذج إذا كانت جميع الحقول فارغة
                mobileClearFormBtn.classList.add('d-none');
            }

            // التحقق من وجود معرف متجر (في وضع التعديل)
            const mobileStoreId = document.getElementById('mobileStoreId').value;
            if (mobileStoreId && mobileStoreId.trim() !== '') {
                // إظهار زر التعديل
                const mobileEditButton = document.getElementById('mobileEditStore');
                if (mobileEditButton) {
                    mobileEditButton.classList.remove('d-none');
                }
            } else {
                // إخفاء زر التعديل إذا لم يكن هناك معرف متجر
                const mobileEditButton = document.getElementById('mobileEditStore');
                if (mobileEditButton) {
                    mobileEditButton.classList.add('d-none');
                }
            }
        }
    }

    // إضافة مستمعي الأحداث لحقول الموبايل
    if (mobileStoreNameInput) {
        mobileStoreNameInput.addEventListener('input', checkMobileFormFields);
        mobileStoreNameInput.addEventListener('focus', checkMobileFormFields); // إظهار الأزرار عند الضغط على الحقل
    }

    if (mobileStorePhoneInput) {
        mobileStorePhoneInput.addEventListener('input', checkMobileFormFields);
        mobileStorePhoneInput.addEventListener('focus', checkMobileFormFields); // إظهار الأزرار عند الضغط على الحقل
    }

    // إضافة مستمع حدث لحقل القائمة في واجهة الموبايل
    const mobileStoreListSelect = document.getElementById('mobileStoreList');
    if (mobileStoreListSelect) {
        mobileStoreListSelect.addEventListener('focus', checkMobileFormFields);
    }

    // إضافة مستمع حدث لحقل نوع المتجر في واجهة الموبايل
    const mobileStoreType = document.getElementById('mobile-store-type');
    if (mobileStoreType) {
        mobileStoreType.addEventListener('focus', checkMobileFormFields);
        mobileStoreType.addEventListener('change', checkMobileFormFields);
    }

    // إضافة مستمع حدث لحقل وصف العنوان في واجهة الموبايل
    const mobileStoreAddress = document.getElementById('mobile-store-address');
    if (mobileStoreAddress) {
        mobileStoreAddress.addEventListener('input', checkMobileFormFields);
        mobileStoreAddress.addEventListener('focus', checkMobileFormFields);
    }

    // إضافة مستمع حدث لقائمة المدن المنسدلة في واجهة الموبايل
    const mobileCitySelect = document.getElementById('mobileCitySelect');
    const mobileDistrictSelect = document.getElementById('mobileDistrictSelect');

    // إصلاح مشكلة ظهور القائمة المنسدلة فوق الصندوق في واجهة الموبايل
    if (mobileDistrictSelect) {
        mobileDistrictSelect.style.position = 'relative';
        mobileDistrictSelect.style.zIndex = '1000';
    }

    // الحصول على حقل عنوان المتجر للموبايل
    const mobileStoreFullAddress = document.getElementById('mobile-store-full-address');

    if (mobileCitySelect) {
        mobileCitySelect.addEventListener('change', function() {
            // تفعيل الأزرار عند تغيير المدينة في النسخة المحمولة
            checkMobileFormFields();

            const selectedCity = this.value;

            // تفعيل/تعطيل قائمة المناطق بناءً على المدينة المختارة
            if (mobileDistrictSelect) {
                if (selectedCity) {
                    // تفعيل قائمة المناطق
                    mobileDistrictSelect.disabled = false;

                    // إخفاء جميع مجموعات المناطق
                    const optgroups = mobileDistrictSelect.querySelectorAll('optgroup');
                    optgroups.forEach(optgroup => {
                        optgroup.style.display = 'none';
                    });

                    // إظهار مجموعة المناطق للمدينة المختارة
                    let cityClassName = '';
                    switch (selectedCity) {
                        case 'طرابلس':
                            cityClassName = 'tripoli';
                            break;
                        case 'بنغازي':
                            cityClassName = 'benghazi';
                            break;
                        case 'مصراتة':
                            cityClassName = 'misrata';
                            break;
                        default:
                            cityClassName = selectedCity.toLowerCase().replace(/\s+/g, '-');
                    }

                    const cityDistricts = mobileDistrictSelect.querySelector(`.${cityClassName}-districts`);
                    if (cityDistricts) {
                        cityDistricts.style.display = 'block';
                    }
                } else {
                    // تعطيل قائمة المناطق إذا لم يتم اختيار مدينة
                    mobileDistrictSelect.disabled = true;
                    mobileDistrictSelect.value = '';
                }
            }

            // تحديث حقل عنوان المتجر
            if (mobileStoreFullAddress && selectedCity) {
                const selectedDistrict = mobileDistrictSelect ? mobileDistrictSelect.value : '';
                if (selectedDistrict) {
                    mobileStoreFullAddress.value = selectedCity + ' - ' + selectedDistrict;
                } else {
                    mobileStoreFullAddress.value = selectedCity;
                }
            }
        });
    }

    // إضافة مستمع حدث لقائمة المناطق في واجهة الموبايل
    if (mobileDistrictSelect) {
        mobileDistrictSelect.addEventListener('change', function() {
            // تفعيل الأزرار عند تغيير المنطقة في النسخة المحمولة
            checkMobileFormFields();

            const selectedDistrict = this.value;
            const selectedCity = mobileCitySelect ? mobileCitySelect.value : '';

            // تحديث حقل عنوان المتجر
            if (mobileStoreFullAddress) {
                if (selectedCity && selectedDistrict) {
                    mobileStoreFullAddress.value = selectedCity + ' - ' + selectedDistrict;
                } else if (selectedCity) {
                    mobileStoreFullAddress.value = selectedCity;
                } else {
                    mobileStoreFullAddress.value = '';
                }
            }
        });
    }

    // إضافة مستمع حدث لحقل الصورة في واجهة الموبايل
    const mobileImageUpload = document.getElementById('mobileImageUpload');
    if (mobileImageUpload) {
        mobileImageUpload.addEventListener('focus', checkMobileFormFields);
    }

    // إضافة مستمع حدث لحقل عنوان المتجر في واجهة الموبايل
    if (mobileStoreFullAddress) {
        mobileStoreFullAddress.addEventListener('focus', checkMobileFormFields);
        mobileStoreFullAddress.addEventListener('click', checkMobileFormFields);
    }

    // التأكد من إخفاء زر التعديل عند تحميل الصفحة
    const editButton = document.getElementById('editStore');
    if (editButton) {
        editButton.classList.add('d-none');
    }

    const mobileEditButton = document.getElementById('mobileEditStore');
    if (mobileEditButton) {
        mobileEditButton.classList.add('d-none');
    }

    // استدعاء دوال التحقق عند تحميل الصفحة
    checkFormFields();
    checkMobileFormFields();

    // معالجة زر التبديل بين الخريطة والقائمة
    const toggleMapBtn = document.getElementById('toggleMap');
    if (toggleMapBtn) {
        toggleMapBtn.addEventListener('click', () => {
            // التبديل بين تبويب الخريطة وتبويب القائمة
            const mapTab = document.getElementById('map-tab');
            const listTab = document.getElementById('list-tab');

            if (mapTab && listTab) {
                if (mapTab.classList.contains('active')) {
                    listTab.click();
                    toggleMapBtn.innerHTML = '<i class="fas fa-map me-1"></i> <span class="d-none d-md-inline">عرض الخريطة</span>';
                } else {
                    mapTab.click();
                    toggleMapBtn.innerHTML = '<i class="fas fa-list me-1"></i> <span class="d-none d-md-inline">عرض القائمة</span>';
                }
            } else {
                // إذا كنا في وضع الموبايل
                const mobileMapTab = document.getElementById('mobile-map-tab');
                const mobileListTab = document.getElementById('mobile-list-tab');

                if (mobileMapTab && mobileListTab) {
                    if (mobileMapTab.classList.contains('active')) {
                        mobileListTab.click();
                        toggleMapBtn.innerHTML = '<i class="fas fa-map me-1"></i> <span class="d-none d-md-inline">عرض الخريطة</span>';
                    } else {
                        mobileMapTab.click();
                        toggleMapBtn.innerHTML = '<i class="fas fa-list me-1"></i> <span class="d-none d-md-inline">عرض القائمة</span>';
                    }
                }
            }
        });
    }

    // متغيرات لتتبع تغيير الموقع
    let originalLocation = null;
    let newLocation = null;
    let pendingStoreData = null;
    let pendingStoreId = null;
    let pendingStoreListId = null;

    // معالجة تقديم نموذج المتجر (إضافة متجر جديد فقط)
    if (storeForm) {
        storeForm.addEventListener('submit', (e) => {
            e.preventDefault();

            // جمع بيانات النموذج
            const storeId = document.getElementById('storeId').value;

            // إذا كان هناك معرف متجر، فهذا يعني أننا في وضع التعديل
            // وسيتم التعامل معه بواسطة زر التعديل، لذا نتوقف هنا
            if (storeId) {
                showAlert('يرجى استخدام زر التعديل لتحديث المتجر الحالي', 'info');
                return;
            }

            const name = document.getElementById('storeName').value;
            const phone = document.getElementById('storePhone').value;
            const imageFile = imageUpload && imageUpload.files.length > 0 ? imageUpload.files[0] : null;

            // الحصول على الموقع المحدد
            const selectedLocation = storeMap.getSelectedLocation();

            if (!selectedLocation) {
                showAlert('⚠️ لم يتم تحديد الموقع على الخريطة! يجب تحديد موقع المتجر قبل الإضافة', 'danger');
                // تسليط الضوء على الخريطة لجذب انتباه المستخدم
                const mapContainer = document.getElementById('map');
                if (mapContainer) {
                    mapContainer.classList.add('map-highlight');
                    setTimeout(() => {
                        mapContainer.classList.remove('map-highlight');
                    }, 1500);
                }

                // إظهار رسالة توجيهية للمستخدم
                const selectedLocationEl = document.getElementById('selectedLocation');
                if (selectedLocationEl) {
                    selectedLocationEl.classList.add('text-danger');
                    selectedLocationEl.classList.add('fw-bold');
                    selectedLocationEl.classList.remove('text-muted');

                    // إعادة التنسيق بعد فترة
                    setTimeout(() => {
                        selectedLocationEl.classList.remove('text-danger');
                        selectedLocationEl.classList.remove('fw-bold');
                        selectedLocationEl.classList.add('text-muted');
                    }, 3000);
                }

                return;
            }

            // التحقق من البيانات
            if (!name) {
                showAlert('يرجى إدخال اسم المتجر', 'warning');
                return;
            }

            // التحقق من عنوان المتجر
            const storeFullAddressValue = document.getElementById('storeFullAddress').value;
            if (!storeFullAddressValue || !storeFullAddressValue.trim()) {
                showAlert('يرجى إدخال عنوان المتجر', 'warning');
                return;
            }

            try {
                // إنشاء كائن بيانات المتجر
                const storeAddressValue = document.getElementById('storeAddress').value;

                // الحصول على بيانات المدينة والمنطقة
                const citySelect = document.getElementById('citySelect');
                const districtSelect = document.getElementById('districtSelect');
                const selectedCity = citySelect ? citySelect.value : '';
                const selectedDistrict = districtSelect ? districtSelect.value : '';

                // الحصول على أسماء المدينة والمنطقة
                const cityName = citySelect && citySelect.selectedOptions[0] ? citySelect.selectedOptions[0].text : '';
                const regionName = districtSelect && districtSelect.selectedOptions[0] ? districtSelect.selectedOptions[0].text : '';

                const storeData = {
                    name,
                    phone,
                    latitude: selectedLocation.lat,
                    longitude: selectedLocation.lng,
                    list_id: document.getElementById('storeListSelect').value,
                    type: document.getElementById('storeType').value,
                    full_address: storeFullAddressValue, // عنوان المتجر
                    address: storeAddressValue, // وصف المكان
                    city_name: cityName,
                    region_name: regionName,
                    city_id: selectedCity,
                    region_id: selectedDistrict
                };

                // إضافة الصورة إذا تم تحديدها
                if (imageFile) {
                    storeData.imageFile = imageFile;
                }

                // إضافة متجر جديد
                console.log('Sending store data to server:', storeData);
                storeManager.addStore(storeData)
                    .then(response => {
                        console.log('Server response:', response);

                        // التحقق من نجاح العملية
                        // اعتبار العملية ناجحة إذا كانت الاستجابة تحتوي على success أو store_id
                        if (response && (response.success || response.store_id)) {
                            const newStore = response;
                            // الحصول على رقم القائمة من المتجر الجديد
                            const listId = newStore.store_id ? newStore.list_id : storeData.list_id;

                            // عرض رسالة نجاح مختلفة حسب نوع الإضافة (معلقة أو مباشرة)
                            if (response.pending) {
                                showAlert(response.message || 'تم إضافة المتجر بنجاح وسيتم مراجعته من قبل المدير', 'info', 5000);
                            } else {
                                showAlert(`تم إضافة المتجر بنجاح إلى قائمة المتاجر`, 'success');
                            }

                            // إعادة تعيين النموذج
                            resetStoreForm();

                            // إذا كان المستخدم يعرض قائمة محددة، قم بتحديثها
                            if (storeManager.currentListId !== null && storeManager.currentListId === listId) {
                                setTimeout(() => {
                                    storeManager.loadStores(listId);
                                }, 500);
                            }
                        } else if (response && response.error) {
                            // عرض رسالة الخطأ من الخادم
                            showAlert(`خطأ: ${response.error}`, 'danger');
                        } else {
                            // رسالة خطأ عامة
                            showAlert('حدث خطأ أثناء إضافة المتجر', 'danger');
                        }
                    })
                    .catch(error => {
                        console.error('خطأ في إضافة المتجر:', error);
                        // عرض رسالة الخطأ إذا كانت متوفرة
                        if (error.message) {
                            showAlert(`خطأ: ${error.message}`, 'danger');
                        } else {
                            showAlert('حدث خطأ أثناء إضافة المتجر', 'danger');
                        }
                    });

                // إعادة تعيين النموذج سيتم معالجتها في دالة resetStoreForm
                document.getElementById('imagePreview').classList.add('d-none');
                document.getElementById('selectedLocation').textContent = 'لم يتم تحديد موقع';
                document.getElementById('formTitle').textContent = 'إضافة متجر جديد';
                document.getElementById('submitStore').innerHTML = '<i class="fas fa-plus-circle me-1"></i> إضافة متجر';

                // لا نقوم بالانتقال إلى تبويب القائمة تلقائيًا لجعل حركة القائمة مستقلة عن حقول الإدخال
                // يمكن للمستخدم النقر على زر القائمة يدويًا للانتقال إليها
            } catch (error) {
                console.error('خطأ في حفظ المتجر:', error);
                showAlert('حدث خطأ أثناء حفظ المتجر', 'danger');
            }
        });
    }

    // معالجة زر الحصول على الموقع الحالي
    const getCurrentLocationBtn = document.getElementById('getCurrentLocation');
    if (getCurrentLocationBtn) {
        getCurrentLocationBtn.addEventListener('click', () => {
            storeMap.getCurrentLocation();
        });
    }

    // معالجة زر عرض قائمة المتاجر
    const viewStoreListBtn = document.getElementById('viewStoreList');
    if (viewStoreListBtn) {
        viewStoreListBtn.addEventListener('click', () => {
            // الانتقال إلى تبويب قائمة المتاجر
            const listTab = document.getElementById('list-tab');
            if (listTab) {
                listTab.click();
            }
        });
    }

    // Clear form button - نستخدم المتغير المعرف مسبقًا
    // التحقق من وجود الزر وإضافة مستمع الحدث
    if (clearFormBtn) {
        clearFormBtn.addEventListener('click', function() {
            if (storeForm) storeForm.reset();

            const storeIdEl = document.getElementById('storeId');
            const locationAddressEl = document.getElementById('locationAddress');
            const selectedLocationEl = document.getElementById('selectedLocation');
            const formTitleEl = document.getElementById('formTitle');
            const submitStoreEl = document.getElementById('submitStore');

            if (storeIdEl) storeIdEl.value = '';
            if (locationAddressEl) locationAddressEl.value = '';
            if (selectedLocationEl) selectedLocationEl.textContent = 'لم يتم تحديد موقع';
            if (formTitleEl) formTitleEl.textContent = 'إضافة متجر جديد';
            if (submitStoreEl) submitStoreEl.innerHTML = '<i class="fas fa-plus-circle me-1"></i> إضافة متجر';

            // إخفاء زر مسح النموذج بعد الضغط عليه
            clearFormBtn.classList.add('d-none');

            // إخفاء زر التعديل أيضًا
            const editBtn = document.getElementById('editStore');
            if (editBtn) {
                editBtn.classList.add('d-none');
            }
        });
    }

    // Browse image button
    const browseImageBtn = document.getElementById('browseImage');
    const imageUploadInput = document.getElementById('imageUpload');

    if (browseImageBtn && imageUploadInput) {
        browseImageBtn.addEventListener('click', function() {
            imageUploadInput.click();
        });

        // معالجة تحميل الصور
        imageUploadInput.addEventListener('change', function() {
            const file = this.files[0];
            if (file) {
                // التحقق من نوع الملف
                const fileType = file.type;
                if (!fileType.match('image.*')) {
                    showAlert('يرجى اختيار ملف صورة صالح', 'warning');
                    this.value = '';
                    return;
                }

                // عرض معاينة الصورة
                const reader = new FileReader();
                reader.onload = function(e) {
                    const imagePreview = document.getElementById('imagePreview');
                    if (imagePreview) {
                        const img = imagePreview.querySelector('img');
                        if (img) {
                            img.src = e.target.result;
                        }
                        imagePreview.classList.remove('d-none');
                    }
                };
                reader.readAsDataURL(file);

                // إظهار رسالة نجاح
                showAlert('تم اختيار الصورة: ' + file.name, 'success');
            }
        });

        // إضافة مستمع أحداث لزر إزالة الصورة
        const removeImageBtn = document.querySelector('.btn-remove-image');
        if (removeImageBtn) {
            removeImageBtn.addEventListener('click', function() {
                const imagePreview = document.getElementById('imagePreview');
                if (imagePreview) {
                    imagePreview.classList.add('d-none');
                    const img = imagePreview.querySelector('img');
                    if (img) {
                        img.src = '#';
                    }
                }

                // إعادة تعيين حقل الصورة
                if (imageUploadInput) {
                    imageUploadInput.value = '';
                }
            });
        }
    }

    // Tab change handlers to sync toggle button text
    const mapTabEl = document.getElementById('map-tab');
    const listTabEl = document.getElementById('list-tab');

    if (mapTabEl) {
        mapTabEl.addEventListener('shown.bs.tab', function() {
            const toggleBtn = document.getElementById('toggleMap');
            if (toggleBtn) toggleBtn.innerHTML = '<i class="fas fa-list me-1"></i> عرض القائمة';

            // Refresh map when tab becomes visible
            if (storeMap && storeMap.map) storeMap.map.invalidateSize();
        });
    }

    if (listTabEl) {
        listTabEl.addEventListener('shown.bs.tab', function() {
            const toggleBtn = document.getElementById('toggleMap');
            if (toggleBtn) toggleBtn.innerHTML = '<i class="fas fa-map me-1"></i> عرض الخريطة';
        });
    }

    // Handle search location
    const searchLocationBtn = document.getElementById('searchLocation');
    if (searchLocationBtn) {
        searchLocationBtn.addEventListener('click', function() {
            const locationAddressEl = document.getElementById('locationAddress');
            if (!locationAddressEl) return;

            const address = locationAddressEl.value;
            if (address) {
                // Use Nominatim service for geocoding (free and requires no API key)
                const searchUrl = `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(address)}`;

                // Show searching message
                showAlert('جاري البحث عن العنوان...', 'info');

                fetch(searchUrl)
                    .then(response => response.json())
                    .then(data => {
                        if (data && data.length > 0) {
                            const location = data[0];
                            const latlng = L.latLng(location.lat, location.lon);

                            // Set the location on the map
                            if (storeMap) {
                                storeMap.setSelectedLocation(latlng);
                                storeMap.map.setView(latlng, 15);

                                // Update selected location text
                                const selectedLocationEl = document.getElementById('selectedLocation');
                                if (selectedLocationEl) {
                                    selectedLocationEl.textContent =
                                        `الموقع: ${latlng.lat.toFixed(6)}, ${latlng.lng.toFixed(6)}`;
                                }

                                showAlert('تم العثور على العنوان', 'success');
                            }
                        } else {
                            showAlert('لم يتم العثور على العنوان. يرجى تحديد الموقع يدويًا على الخريطة.', 'warning');
                        }
                    })
                    .catch(error => {
                        console.error('Error searching for address:', error);
                        showAlert('حدث خطأ أثناء البحث عن العنوان. يرجى تحديد الموقع يدويًا على الخريطة.', 'danger');
                    });
            } else {
                showAlert('يرجى إدخال عنوان للبحث عنه', 'warning');
            }
        });
    }

    // Listen for checkbox changes to update the counters
    document.addEventListener('change', function(e) {
        if (e.target && e.target.classList.contains('store-checkbox')) {
            if (storeManager) storeManager.updateSelectedStoresCount();
        }
    });

    // إضافة مستمع حدث لزر تحديد الكل
    const selectAllBtn = document.getElementById('selectAll');
    if (selectAllBtn) {
        selectAllBtn.addEventListener('click', function() {
            // تحديد جميع خانات الاختيار
            const checkboxes = document.querySelectorAll('.store-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = true;
            });

            // تحديث واجهة المستخدم
            if (storeManager) storeManager.updateSelectedStoresCount();
        });
    }

    // إضافة مستمع حدث لزر إلغاء التحديد
    const cancelSelectionBtn = document.getElementById('cancelSelection');
    if (cancelSelectionBtn) {
        cancelSelectionBtn.addEventListener('click', function() {
            // إلغاء تحديد جميع خانات الاختيار
            const checkboxes = document.querySelectorAll('.store-checkbox:checked');
            checkboxes.forEach(checkbox => {
                checkbox.checked = false;
            });

            // تحديث واجهة المستخدم
            if (storeManager) storeManager.updateSelectedStoresCount();

            // إخفاء الزر بعد النقر عليه
            cancelSelectionBtn.classList.remove('show');
            setTimeout(() => {
                cancelSelectionBtn.classList.add('d-none');
            }, 300);

            // إخفاء زر تحديد الكل أيضًا
            if (selectAllBtn) {
                selectAllBtn.classList.remove('show');
                setTimeout(() => {
                    selectAllBtn.classList.add('d-none');
                }, 300);
            }
        });
    }

    // Share selected stores button
    const shareSelectedStoresBtn = document.getElementById('shareSelectedStores');
    if (shareSelectedStoresBtn && storeManager) {
        // إزالة مستمعات الأحداث السابقة لتجنب التداخل
        const newShareBtn = shareSelectedStoresBtn.cloneNode(true);
        shareSelectedStoresBtn.parentNode.replaceChild(newShareBtn, shareSelectedStoresBtn);

        newShareBtn.addEventListener('click', function() {
            const selectedStoreIds = storeManager.getSelectedStoreIds();
            if (selectedStoreIds.length > 0) {
                storeManager.shareSelectedStoresOnWhatsApp();
            } else {
                showAlert('يرجى اختيار متجر واحد على الأقل للمشاركة', 'warning');
            }
        });
    }

    // Delete selected stores button
    const deleteSelectedBtn = document.getElementById('deleteSelectedStores');
    if (deleteSelectedBtn) {
        // إزالة مستمعات الأحداث السابقة لتجنب التداخل
        const newDeleteBtn = deleteSelectedBtn.cloneNode(true);
        deleteSelectedBtn.parentNode.replaceChild(newDeleteBtn, deleteSelectedBtn);

        newDeleteBtn.addEventListener('click', function() {
            // استخدام دالة الحذف الجماعي الجديدة
            storeManager.batchDeleteStores();
        });
    }
}

/**
 * إعداد وظيفة البحث
 */
function setupSearch() {
    const searchInput = document.getElementById('searchInput');
    const clearSearchBtn = document.getElementById('clearSearch');

    if (!searchInput) {
        console.warn('عنصر البحث غير موجود');
        return;
    }

    // البحث عند الكتابة
    searchInput.addEventListener('input', () => {
        const query = searchInput.value;
        storeManager.searchStores(query);
    });

    // البحث عند الضغط على Enter
    searchInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            e.preventDefault();
            const query = searchInput.value;
            storeManager.searchStores(query);
        }
    });

    // زر البحث
    const searchButton = document.getElementById('searchButton');
    if (searchButton) {
        searchButton.addEventListener('click', () => {
            const query = searchInput.value;
            storeManager.searchStores(query);
        });
    }

    // مسح البحث
    if (clearSearchBtn) {
        clearSearchBtn.addEventListener('click', () => {
            searchInput.value = '';
            storeManager.searchStores('');
            searchInput.focus();
        });
    }
}

/**
 * إعداد مستمعي أحداث نافذة تأكيد تغيير الموقع
 */
function setupLocationChangeHandlers() {
    // نافذة تأكيد تعديل الاسم أو الهاتف
    const basicChangeConfirmModalHTML = `
    <div class="modal fade" id="basicChangeConfirmModal" tabindex="-1" aria-labelledby="basicChangeConfirmLabel" aria-hidden="true">
      <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
          <div class="modal-header bg-warning text-dark">
            <h5 class="modal-title" id="basicChangeConfirmLabel">تأكيد التعديل</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
          </div>
          <div class="modal-body text-center">
            <p class="fs-5">لقد قمت بتعديل اسم المتجر أو رقم الهاتف.</p>
            <p>هل ترغب في حفظ التغييرات؟</p>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
            <button type="button" class="btn btn-warning" id="confirmBasicChange">
              <i class="fas fa-check me-1"></i> نعم، احفظ التعديلات
            </button>
          </div>
        </div>
      </div>
    </div>`;

    // إضافة نافذة تأكيد التعديلات البسيطة إلى الصفحة
    document.body.insertAdjacentHTML('beforeend', basicChangeConfirmModalHTML);

    // زر تأكيد التعديلات البسيطة
    const confirmBtn = document.getElementById('confirmBasicChange');
    if (confirmBtn) {
        confirmBtn.addEventListener('click', () => {
            const btn = document.getElementById('confirmBasicChange');
            btn.disabled = true;
            btn.innerHTML = `<i class="fas fa-spinner fa-spin me-1"></i> جاري الحفظ...`;

            storeManager.updateStore(pendingStoreData)
                .then((updatedStore) => {
                    const listId = updatedStore ? updatedStore.list_id : pendingStoreData.list_id;

                    showAlert(`تم حفظ التغييرات بنجاح في قائمة المتاجر`, 'success');
                    resetStoreForm();

                    if (storeManager.currentListId !== null && storeManager.currentListId === listId) {
                        setTimeout(() => {
                            storeManager.loadStores(listId);
                        }, 500);
                    }

                    const modal = bootstrap.Modal.getInstance(document.getElementById('basicChangeConfirmModal'));
                    modal.hide();

                    pendingStoreData = null;
                    btn.disabled = false;
                    btn.innerHTML = `<i class="fas fa-check me-1"></i> نعم، احفظ التعديلات`;
                })
                .catch(error => {
                    console.error('خطأ في الحفظ:', error);
                    showAlert('حدث خطأ أثناء حفظ التعديلات', 'danger');
                    btn.disabled = false;
                    btn.innerHTML = `<i class="fas fa-check me-1"></i> نعم، احفظ التعديلات`;
                });
        });
    }
    console.log('إعداد مستمعي أحداث نافذة تأكيد تغيير الموقع');
    // زر تأكيد الموقع الجديد
    const confirmLocationBtn = document.getElementById('confirmLocationChange');
    if (confirmLocationBtn) {
        // إزالة مستمعي الأحداث السابقة لتجنب التكرار
        const newConfirmBtn = confirmLocationBtn.cloneNode(true);
        confirmLocationBtn.parentNode.replaceChild(newConfirmBtn, confirmLocationBtn);

        newConfirmBtn.addEventListener('click', (e) => {
            // منع السلوك الافتراضي للزر وإغلاق النافذة
            e.preventDefault();

            console.log('تم النقر على زر تأكيد الموقع الجديد');
            console.log('pendingStoreData:', pendingStoreData);

            // إظهار حالة المعالجة
            const statusEl = document.getElementById('locationChangeStatus');
            if (statusEl) {
                statusEl.classList.remove('d-none');
            }

            // تعطيل الأزرار أثناء المعالجة
            newConfirmBtn.disabled = true;
            const keepOriginalBtn = document.getElementById('keepOriginalLocation');
            if (keepOriginalBtn) {
                keepOriginalBtn.disabled = true;
            }

            // المتابعة بالموقع الجديد
            if (pendingStoreData) {
                // نسخة من البيانات لتجنب التغييرات المرجعية
                const storeDataCopy = {...pendingStoreData};

                // التأكد من أن البيانات تحتوي على الموقع الجديد
                if (newLocation) {
                    storeDataCopy.latitude = newLocation.lat;
                    storeDataCopy.longitude = newLocation.lng;
                }

                console.log('إرسال بيانات التحديث مع الموقع الجديد:', storeDataCopy);

                storeManager.updateStore(storeDataCopy)
                    .then((updatedStore) => {
                        // الحصول على رقم القائمة من المتجر المحدث
                        const listId = updatedStore ? updatedStore.list_id : pendingStoreData.list_id;
                        const listName = listId ? `القائمة ${listId}` : 'القائمة الافتراضية';

                        // إغلاق النافذة بعد الحفظ بنجاح
                        const locationModal = bootstrap.Modal.getInstance(document.getElementById('locationChangeModal'));
                        if (locationModal) {
                            setTimeout(() => {
                                locationModal.hide();
                            }, 500); // تأخير للتأكد من إكمال المعالجة
                        }

                        showAlert(`تم تحديث المتجر وتغيير موقعه بنجاح في ${listName}`, 'success');

                        // إعادة تعيين النموذج
                        resetStoreForm();

                        // إذا كان المستخدم يعرض قائمة محددة، قم بتحديثها
                        if (storeManager.currentListId !== null && storeManager.currentListId === listId) {
                            setTimeout(() => {
                                storeManager.loadStores(listId);
                            }, 500);
                        }

                        // إعادة تعيين المتغيرات
                        originalLocation = null;
                        newLocation = null;
                        pendingStoreData = null;
                    })
                    .catch(error => {
                        console.error('خطأ في تحديث المتجر:', error);
                        showAlert('حدث خطأ أثناء تحديث المتجر', 'danger');

                        // إعادة تفعيل الأزرار في حالة الخطأ
                        newConfirmBtn.disabled = false;
                        const keepOriginalBtn = document.getElementById('keepOriginalLocation');
                        if (keepOriginalBtn) {
                            keepOriginalBtn.disabled = false;
                        }

                        // إخفاء حالة المعالجة
                        const statusEl = document.getElementById('locationChangeStatus');
                        if (statusEl) {
                            statusEl.classList.add('d-none');
                        }
                    });
            }
        });
    }

    // زر الاحتفاظ بالموقع الأصلي
    const keepOriginalBtn = document.getElementById('keepOriginalLocation');
    if (keepOriginalBtn) {
        // إزالة مستمعي الأحداث السابقة لتجنب التكرار
        const newKeepBtn = keepOriginalBtn.cloneNode(true);
        keepOriginalBtn.parentNode.replaceChild(newKeepBtn, keepOriginalBtn);

        newKeepBtn.addEventListener('click', (e) => {
            // منع السلوك الافتراضي للزر وإغلاق النافذة
            e.preventDefault();

            console.log('تم النقر على زر الاحتفاظ بالموقع الأصلي');
            console.log('pendingStoreData:', pendingStoreData);
            console.log('originalLocation:', originalLocation);

            // إظهار حالة المعالجة
            const statusEl = document.getElementById('locationChangeStatus');
            if (statusEl) {
                statusEl.classList.remove('d-none');
            }

            // تعطيل الأزرار أثناء المعالجة
            newKeepBtn.disabled = true;
            const confirmBtn = document.getElementById('confirmLocationChange');
            if (confirmBtn) {
                confirmBtn.disabled = true;
            }

            // لا نغلق النافذة هنا، سيتم إغلاقها بعد الحفظ بنجاح
            if (pendingStoreData && originalLocation) {
                // نسخة من البيانات لتجنب التغييرات المرجعية
                const storeDataCopy = {...pendingStoreData};

                // استعادة الموقع الأصلي
                storeDataCopy.latitude = originalLocation.lat;
                storeDataCopy.longitude = originalLocation.lng;

                console.log('إرسال بيانات التحديث مع الموقع الأصلي:', storeDataCopy);

                // تحديث المتجر بالموقع الأصلي
                storeManager.updateStore(storeDataCopy)
                    .then((updatedStore) => {
                        // الحصول على رقم القائمة من المتجر المحدث
                        const listId = updatedStore ? updatedStore.list_id : pendingStoreData.list_id;
                        const listName = listId ? `القائمة ${listId}` : 'القائمة الافتراضية';

                        // إغلاق النافذة بعد الحفظ بنجاح
                        const locationModal = bootstrap.Modal.getInstance(document.getElementById('locationChangeModal'));
                        if (locationModal) {
                            setTimeout(() => {
                                locationModal.hide();
                            }, 500); // تأخير للتأكد من إكمال المعالجة
                        }

                        showAlert(`تم تحديث المتجر مع الاحتفاظ بالموقع الأصلي في ${listName}`, 'success');

                        // إعادة تعيين النموذج
                        resetStoreForm();

                        // إذا كان المستخدم يعرض قائمة محددة، قم بتحديثها
                        if (storeManager.currentListId !== null && storeManager.currentListId === listId) {
                            setTimeout(() => {
                                storeManager.loadStores(listId);
                            }, 500);
                        }

                        // إعادة تعيين المتغيرات
                        originalLocation = null;
                        newLocation = null;
                        pendingStoreData = null;
                    })
                    .catch(error => {
                        console.error('خطأ في تحديث المتجر:', error);
                        showAlert('حدث خطأ أثناء تحديث المتجر', 'danger');

                        // إعادة تفعيل الأزرار في حالة الخطأ
                        newKeepBtn.disabled = false;
                        const confirmBtn = document.getElementById('confirmLocationChange');
                        if (confirmBtn) {
                            confirmBtn.disabled = false;
                        }

                        // إخفاء حالة المعالجة
                        const statusEl = document.getElementById('locationChangeStatus');
                        if (statusEl) {
                            statusEl.classList.add('d-none');
                        }
                    });
            }
        });
    }
}

/**
 * Set up mobile form handlers
 * @param {StoreMap} map - Map instance to use
 * @param {StoreManager} storeManager - Store manager instance
 */
function setupMobileFormHandlers(map, storeManager) {
    // Mobile form submission
    const mobileStoreForm = document.getElementById('mobileStoreForm');

    // تحقق من وجود نموذج المتجر للجوال قبل إضافة معالج الحدث
    if (!mobileStoreForm) {
        console.warn('لم يتم العثور على عنصر mobileStoreForm');
        return;
    }

    mobileStoreForm.addEventListener('submit', async function(e) {
        e.preventDefault();

        const storeIdEl = document.getElementById('mobileStoreId');
        const storeNameEl = document.getElementById('mobileStoreName');
        const storePhoneEl = document.getElementById('mobileStorePhone');
        const mobileImageUploadEl = document.getElementById('mobileImageUpload');

        if (!storeIdEl || !storeNameEl || !storePhoneEl) {
            showAlert('بعض عناصر النموذج غير موجودة', 'danger');
            return;
        }

        const storeId = storeIdEl.value;
        const name = storeNameEl.value;
        const phone = storePhoneEl.value;
        const location = map.getSelectedLocation();

        if (!location) {
            showAlert('يرجى تحديد موقع على الخريطة', 'danger');
            return;
        }

        // الحصول على ملف الصورة إذا تم اختياره
        const imageFile = mobileImageUploadEl && mobileImageUploadEl.files.length > 0 ? mobileImageUploadEl.files[0] : null;

        // إعداد بيانات المتجر
        const storeData = {
            name,
            phone,
            lat: location.lat,
            lng: location.lng,
            type: document.getElementById('mobile-store-type').value,
            address: document.getElementById('mobile-store-address').value
        };

        // إضافة الصورة إلى البيانات إذا تم اختيارها
        if (imageFile) {
            storeData.imageFile = imageFile;
        }

        try {
            if (storeId) {
                // تحديث متجر موجود
                storeManager.updateStore(storeId, storeData);
                showAlert('تم تحديث المتجر بنجاح', 'success');
            } else {
                // إضافة متجر جديد
                storeManager.addStore(storeData);
                showAlert('تم إضافة المتجر بنجاح', 'success');

                // إعادة تعيين معاينة الصورة
                storeManager.removeMobileImagePreview();
            }
        } catch (error) {
            console.error('خطأ في إضافة/تحديث المتجر:', error);
            showAlert('حدث خطأ أثناء حفظ المتجر', 'danger');
        }

        // Reset form
        mobileStoreForm.reset();
        if (storeIdEl) storeIdEl.value = '';

        const locationAddressEl = document.getElementById('mobileLocationAddress');
        const selectedLocationEl = document.getElementById('mobileSelectedLocation');
        const formTitleEl = document.getElementById('mobileFormTitle');
        const submitStoreEl = document.getElementById('mobileSubmitStore');

        if (locationAddressEl) locationAddressEl.value = '';
        if (selectedLocationEl) selectedLocationEl.textContent = 'لم يتم تحديد موقع';
        if (formTitleEl) formTitleEl.textContent = 'إضافة متجر جديد';
        if (submitStoreEl) submitStoreEl.innerHTML = '<i class="fas fa-plus-circle me-1"></i> إضافة متجر';

        // Switch to map tab
        const mapTabEl = document.getElementById('mobile-map-tab');
        if (mapTabEl) mapTabEl.click();
    });

    // Mobile: Clear form button
    const mobileClearFormEl = document.getElementById('mobileClearForm');
    if (mobileClearFormEl) {
        mobileClearFormEl.addEventListener('click', function() {
            if (mobileStoreForm) mobileStoreForm.reset();

            const storeIdEl = document.getElementById('mobileStoreId');
            const locationAddressEl = document.getElementById('mobileLocationAddress');
            const selectedLocationEl = document.getElementById('mobileSelectedLocation');
            const formTitleEl = document.getElementById('mobileFormTitle');
            const submitStoreEl = document.getElementById('mobileSubmitStore');

            if (storeIdEl) storeIdEl.value = '';
            if (locationAddressEl) locationAddressEl.value = '';
            if (selectedLocationEl) selectedLocationEl.textContent = 'لم يتم تحديد موقع';
            if (formTitleEl) formTitleEl.textContent = 'إضافة متجر جديد';
            if (submitStoreEl) submitStoreEl.innerHTML = '<i class="fas fa-plus-circle me-1"></i> إضافة متجر';

            // إخفاء زر مسح النموذج بعد الضغط عليه
            mobileClearFormEl.classList.add('d-none');

            // إخفاء زر التعديل أيضًا
            const mobileEditBtn = document.getElementById('mobileEditStore');
            if (mobileEditBtn) {
                mobileEditBtn.classList.add('d-none');
            }
        });
    }

    // Mobile: Browse image button
    const mobileBrowseImageEl = document.getElementById('mobileBrowseImage');
    const mobileImageUploadEl = document.getElementById('mobileImageUpload');

    if (mobileBrowseImageEl && mobileImageUploadEl) {
        mobileBrowseImageEl.addEventListener('click', function() {
            mobileImageUploadEl.click();
        });

        // تم تحديث معالج حدث تغيير الصورة في النموذج المحمول
        // الآن يستخدم معالج حدث التغيير في كائن StoreManager مباشرة
        // ليتم التعامل مع عرض معاينة الصورة
    }

    // Mobile: Search location
    const mobileSearchLocationEl = document.getElementById('mobileSearchLocation');
    if (mobileSearchLocationEl) {
        mobileSearchLocationEl.addEventListener('click', function() {
            const locationAddressEl = document.getElementById('mobileLocationAddress');
            if (!locationAddressEl) return;

            const address = locationAddressEl.value;
            if (address) {
                // Use Nominatim service for geocoding (free and requires no API key)
                const searchUrl = `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(address)}`;

                // Show searching message
                showAlert('جاري البحث عن العنوان...', 'info');

                fetch(searchUrl)
                    .then(response => response.json())
                    .then(data => {
                        if (data && data.length > 0) {
                            const location = data[0];
                            const latlng = L.latLng(location.lat, location.lon);

                            // Set the location on the map
                            map.setSelectedLocation(latlng);
                            map.map.setView(latlng, 15);

                            // Update location text
                            const selectedLocationEl = document.getElementById('mobileSelectedLocation');
                            if (selectedLocationEl) {
                                selectedLocationEl.textContent =
                                    `الموقع: ${latlng.lat.toFixed(6)}, ${latlng.lng.toFixed(6)}`;
                            }

                            showAlert('تم العثور على العنوان', 'success');
                        } else {
                            showAlert('لم يتم العثور على العنوان. يرجى تحديد الموقع يدويًا على الخريطة.', 'warning');
                        }
                    })
                    .catch(error => {
                        console.error('Error searching for address:', error);
                        showAlert('حدث خطأ أثناء البحث عن العنوان. يرجى تحديد الموقع يدويًا على الخريطة.', 'danger');
                    });
            } else {
                showAlert('يرجى إدخال عنوان للبحث عنه', 'warning');
            }
        });
    }

    // Mobile: Handle tab changes to refresh map
    const mobileMapTabEl = document.getElementById('mobile-map-tab');
    if (mobileMapTabEl) {
        mobileMapTabEl.addEventListener('shown.bs.tab', function() {
            // Refresh map when tab becomes visible
            if (map) map.map.invalidateSize();
        });
    }
}

/**
 * معالجة زر حفظ التعديل
 */
function handleStoreUpdate(storeId) {
    // التحقق من صلاحيات المستخدم
    const userRoleElement = document.querySelector('meta[name="user-role"]');
    const userRole = userRoleElement ? parseInt(userRoleElement.getAttribute('content')) : null;

    // إذا كان المستخدم زائراً (الدور 3)، لا يمكنه تعديل المتاجر
    if (userRole === 3) {
        return;
    }

    // جمع بيانات النموذج
    const name = document.getElementById('storeName').value;
    const phone = document.getElementById('storePhone').value;
    const listId = document.getElementById('storeListSelect').value;
    const type = document.getElementById('storeType').value;
    const address = document.getElementById('storeAddress').value;
    const imageFileInput = document.getElementById('imageUpload');
    const imageFile = imageFileInput && imageFileInput.files.length > 0 ? imageFileInput.files[0] : null;

    // الحصول على بيانات المدينة والمنطقة
    const citySelect = document.getElementById('citySelect');
    const districtSelect = document.getElementById('districtSelect');
    const selectedCity = citySelect ? citySelect.value : '';
    const selectedDistrict = districtSelect ? districtSelect.value : '';

    // الحصول على أسماء المدينة والمنطقة
    const cityName = citySelect && citySelect.selectedOptions[0] ? citySelect.selectedOptions[0].text : '';
    const regionName = districtSelect && districtSelect.selectedOptions[0] ? districtSelect.selectedOptions[0].text : '';

    // الحصول على المتجر الأصلي
    const store = storeManager.findStoreById(storeId);
    if (!store) {
        showAlert('تعذر العثور على المتجر', 'danger');
        return;
    }

    // استخدام الموقع الأصلي إذا لم يتم اختيار موقع جديد
    let selectedLocation = storeMap.getSelectedLocation();
    if (!selectedLocation) {
        // استخدام الدالة المساعدة للحصول على الموقع الحالي
        selectedLocation = storeUpdateHelpers.getCurrentLocation(store);
    }

    // التحقق من الاسم
    if (!name) {
        showAlert('يرجى إدخال اسم المتجر', 'warning');
        return;
    }

    // إنشاء كائن بيانات المتجر باستخدام الدالة المساعدة
    const storeData = storeUpdateHelpers.createStoreData(storeId, name, phone, selectedLocation, listId, imageFile, type, address, cityName, regionName, selectedCity, selectedDistrict);

    // التحقق من وجود تغييرات باستخدام الدالة المساعدة
    const changes = storeUpdateHelpers.checkForChanges(store, storeData);

    // إذا تغير الموقع، نعرض نافذة تأكيد تغيير الموقع
    if (changes.locationChanged) {
        // تخزين الموقع الأصلي والجديد للاستخدام لاحقًا
        originalLocation = storeUpdateHelpers.getCurrentLocation(store);
        newLocation = { lat: storeData.latitude, lng: storeData.longitude };
        pendingStoreData = { ...storeData };
        pendingStoreId = storeId;
        pendingStoreListId = listId;

        const locationModal = new bootstrap.Modal(document.getElementById('locationChangeModal'));
        locationModal.show();
        return;
    }
    // إذا تغير الاسم أو رقم الهاتف أو القائمة أو نوع المتجر أو وصف العنوان أو المدينة أو المنطقة أو الصورة، نعرض نافذة تأكيد التغييرات الأساسية
    else if (changes.nameChanged || changes.phoneChanged || changes.listChanged || changes.typeChanged || changes.addressChanged || changes.cityChanged || changes.regionChanged || imageFile) {
        pendingStoreData = { ...storeData };
        pendingStoreId = storeId;
        pendingStoreListId = listId;

        const basicModal = new bootstrap.Modal(document.getElementById('basicChangeConfirmModal'));
        basicModal.show();
        return;
    }
    // إذا لم يتم إجراء أي تغييرات
    else {
        showAlert('لم يتم إجراء أي تغييرات تستدعي الحفظ', 'info');
        return;
    }
}

/**
 * Show an alert message to the user
 */
function showAlert(message, type = 'info') {
    const alertContainer = document.getElementById('alertContainer');
    if (!alertContainer) {
        console.warn('عنصر alertContainer غير موجود، لا يمكن عرض التنبيه:', message);
        return;
    }

    const alert = document.createElement('div');
    alert.className = `alert alert-${type} alert-dismissible fade show`;
    alert.role = 'alert';

    alert.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;

    alertContainer.appendChild(alert);

    // Remove alert after 3 seconds
    setTimeout(() => {
        alert.classList.remove('show');
        setTimeout(() => alert.remove(), 150);
    }, 3000);
}

/**
 * Reset the store form
 */
function resetStoreForm() {
    const storeForm = document.getElementById('storeForm');
    if (!storeForm) return;

    // إعادة تعيين النموذج
    storeForm.reset();
    document.getElementById('storeId').value = '';

    // إعادة تعيين نوع المتجر إلى القيمة الافتراضية
    const storeType = document.getElementById('storeType');
    if (storeType) {
        storeType.value = 'A';
    }

    // إعادة تعيين وصف العنوان
    const storeAddress = document.getElementById('storeAddress');
    if (storeAddress) {
        storeAddress.value = '';
    }

    // إعادة تعيين الصورة
    const imagePreview = document.getElementById('imagePreview');
    if (imagePreview) {
        imagePreview.classList.add('d-none');
        imagePreview.src = '';
    }

    // إعادة تعيين الموقع
    const selectedLocation = document.getElementById('selectedLocation');
    if (selectedLocation) {
        selectedLocation.textContent = 'لم يتم تحديد موقع';
    }

    // إعادة تعيين عنوان النموذج
    const formTitle = document.getElementById('formTitle');
    if (formTitle) {
        formTitle.textContent = 'إضافة متجر جديد';
    }

    // إعادة تعيين زر الإرسال
    const submitButton = document.getElementById('submitStore');
    if (submitButton) {
        submitButton.innerHTML = '<i class="fas fa-plus-circle me-1"></i> إضافة متجر';
    }

    // إخفاء زر التعديل وإخفاء زر مسح النموذج
    const editButton = document.getElementById('editStore');
    if (editButton) {
        editButton.classList.add('d-none');
    }

    const clearButton = document.getElementById('clearForm');
    if (clearButton) {
        clearButton.classList.add('d-none');
    }

    // إعادة تعيين النموذج في واجهة الموبايل
    resetMobileStoreForm();
}

/**
 * Reset the mobile store form
 */
function resetMobileStoreForm() {
    // إعادة تعيين نموذج الموبايل
    const mobileStoreForm = document.getElementById('mobileStoreForm');
    if (mobileStoreForm) {
        mobileStoreForm.reset();
        const mobileStoreId = document.getElementById('mobileStoreId');
        if (mobileStoreId) {
            mobileStoreId.value = '';
        }

        // إعادة تعيين نوع المتجر إلى القيمة الافتراضية
        const mobileStoreType = document.getElementById('mobile-store-type');
        if (mobileStoreType) {
            mobileStoreType.value = 'A';
        }

        // إعادة تعيين وصف العنوان
        const mobileStoreAddress = document.getElementById('mobile-store-address');
        if (mobileStoreAddress) {
            mobileStoreAddress.value = '';
        }
    }

    // إعادة تعيين الصورة
    const mobileImagePreview = document.getElementById('mobileImagePreview');
    if (mobileImagePreview) {
        mobileImagePreview.classList.add('d-none');
    }

    // إعادة تعيين الموقع
    const mobileSelectedLocation = document.getElementById('mobileSelectedLocation');
    if (mobileSelectedLocation) {
        mobileSelectedLocation.textContent = 'لم يتم تحديد موقع';
        mobileSelectedLocation.classList.add('text-info');
        mobileSelectedLocation.classList.remove('text-success');
    }

    // إعادة تعيين عنوان النموذج
    const mobileFormTitle = document.getElementById('mobileFormTitle');
    if (mobileFormTitle) {
        mobileFormTitle.textContent = 'إضافة متجر جديد';
    }

    // إعادة تعيين زر الإرسال
    const mobileSubmitButton = document.getElementById('mobileSubmitStore');
    if (mobileSubmitButton) {
        mobileSubmitButton.innerHTML = '<i class="fas fa-plus-circle me-1"></i> إضافة متجر';
    }

    // إخفاء زر التعديل وإخفاء زر مسح النموذج
    const mobileEditButton = document.getElementById('mobileEditStore');
    if (mobileEditButton) {
        mobileEditButton.classList.add('d-none');
    }

    const mobileClearButton = document.getElementById('mobileClearForm');
    if (mobileClearButton) {
        mobileClearButton.classList.add('d-none');
    }

    // إعادة تعيين نموذج الموبايل الثاني
    const mobileStoreForm2 = document.getElementById('mobile-store-form');
    if (mobileStoreForm2) {
        mobileStoreForm2.reset();
        const mobileStoreId2 = document.getElementById('mobile-store-id');
        if (mobileStoreId2) {
            mobileStoreId2.value = '';
        }

        // إعادة تعيين الصورة
        const mobileImagePreview2 = document.getElementById('mobile-image-preview');
        if (mobileImagePreview2) {
            mobileImagePreview2.classList.add('d-none');
        }

        // إعادة تعيين الموقع
        const mobileSelectedLocation2 = document.getElementById('mobile-selected-location');
        if (mobileSelectedLocation2) {
            mobileSelectedLocation2.textContent = 'لم يتم تحديد موقع';
            mobileSelectedLocation2.classList.add('text-info');
            mobileSelectedLocation2.classList.remove('text-success');
        }

        // إعادة تعيين زر الإرسال
        const mobileSubmitButton2 = document.getElementById('mobile-submit-store');
        if (mobileSubmitButton2) {
            mobileSubmitButton2.innerHTML = '<i class="fas fa-plus-circle me-1"></i> إضافة متجر';
        }

        // إخفاء زر التعديل وإظهار زر مسح النموذج
        const mobileEditButton2 = document.getElementById('mobile-edit-store');
        if (mobileEditButton2) {
            mobileEditButton2.classList.add('d-none');
        }

        const mobileClearButton2 = document.getElementById('mobile-clear-form');
        if (mobileClearButton2) {
            mobileClearButton2.classList.remove('d-none');
        }
    }
}

// Function to fetch stores with caching
async function fetchStores() {
    const cachedStores = apiCache.get('stores');
    if (cachedStores) {
        return cachedStores;
    }

    try {
        const response = await fetch('/api/mobile/stores');
        const data = await response.json();
        apiCache.set('stores', data);
        return data;
    } catch (error) {
        console.error('Error fetching stores:', error);
        return [];
    }
}

// Function to fetch lists with caching
async function fetchLists() {
    const cachedLists = apiCache.get('lists');
    if (cachedLists) {
        return cachedLists;
    }

    try {
        const response = await fetch('/api/mobile/lists');
        const data = await response.json();
        apiCache.set('lists', data);
        return data;
    } catch (error) {
        console.error('Error fetching lists:', error);
        return [];
    }
}
