/**
 * تنسيقات إضافية لصفحة الإحصائيات
 * تحسينات وإصلاحات لمشاكل العرض
 */

/* تحسين علامات الخريطة */
.map-marker-alt {
    color: #d50000;
    font-size: 24px;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
    transform-origin: bottom center;
}

/* تأثير النبض للعلامات المحددة */
.pulse-animation {
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.3);
        opacity: 0.7;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* تحسين جداول الإحصائيات */
.table-responsive {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

#address-stats-table tr:hover,
#type-address-stats-table tr:hover,
#region-stats-table tr:hover {
    background-color: rgba(213, 0, 0, 0.1);
    transition: background-color 0.3s ease;
}

/* تنسيق صف المجموع */
.table-active.fw-bold {
    background-color: rgba(0, 0, 0, 0.1) !important;
    border-top: 2px solid #d50000;
}

/* تحسين عرض الخرائط */
#stats-map, #heatmap, #region-mini-map {
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    border: 2px solid #2a2a2a;
}

/* تحسين عرض النوافذ المنبثقة */
.store-popup {
    max-width: 200px;
    font-size: 13px;
}

.store-popup h6 {
    color: #d50000;
    margin-bottom: 5px;
    font-weight: bold;
}

/* تحسين عرض البطاقات */
.card {
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

/* تحسين عرض الرسوم البيانية */
.chart-container {
    padding: 10px;
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
}

/* تنسيقات خاصة لجدول تفاصيل المناطق */
.region-stats-container {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
}

/* شريط التمرير المخصص للجدول */
.region-stats-container::-webkit-scrollbar {
    width: 14px;
    height: 14px;
}

.region-stats-container::-webkit-scrollbar-track {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 10px;
    border: 2px solid #ffffff;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
    margin: 2px;
}

.region-stats-container::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #d50000 0%, #b71c1c 100%);
    border-radius: 10px;
    border: 3px solid #ffffff;
    box-shadow:
        0 2px 8px rgba(213, 0, 0, 0.3),
        inset 0 1px 2px rgba(255, 255, 255, 0.3);
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
}

.region-stats-container::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #b71c1c 0%, #8e0000 100%);
    border-color: #f1f3f4;
    box-shadow:
        0 4px 15px rgba(183, 28, 28, 0.4),
        inset 0 1px 3px rgba(255, 255, 255, 0.4);
    transform: scale(1.05);
}

.region-stats-container::-webkit-scrollbar-thumb:active {
    background: linear-gradient(135deg, #8e0000 0%, #6d0000 100%);
    box-shadow:
        inset 0 2px 6px rgba(0, 0, 0, 0.3),
        0 2px 4px rgba(142, 0, 0, 0.2);
    transform: scale(0.98);
    border-color: #e9ecef;
}

.region-stats-container::-webkit-scrollbar-corner {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 10px;
    border: 2px solid #ffffff;
}

/* تأثير إضافي لشريط التمرير */
.region-stats-container::-webkit-scrollbar-thumb::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 2px;
    height: 60%;
    background: rgba(255, 255, 255, 0.5);
    border-radius: 1px;
}

/* تحسين عرض الشاشات الصغيرة */
@media (max-width: 768px) {
    .stats-card {
        margin-bottom: 15px;
    }

    .chart-container {
        height: 220px;
    }

    #stats-map, #heatmap {
        height: 300px !important;
    }

    /* شريط تمرير أصغر للشاشات الصغيرة */
    .region-stats-container::-webkit-scrollbar {
        width: 10px;
        height: 10px;
    }

    .region-stats-container::-webkit-scrollbar-thumb {
        border: 2px solid #ffffff;
    }
}

/* تنسيقات إضافية للجدول */
.region-header-cell {
    position: sticky;
    top: 0;
    z-index: 10;
    padding: 15px 12px !important;
    font-weight: 600;
    font-size: 0.9rem;
    border-bottom: 2px solid #d50000 !important;
    text-align: center;
    white-space: nowrap;
}

.region-stats-table tbody tr {
    transition: all 0.3s ease;
    border-bottom: 1px solid #dee2e6;
}

.region-stats-table tbody tr:hover {
    background: linear-gradient(135deg, rgba(213, 0, 0, 0.05) 0%, rgba(183, 28, 28, 0.05) 100%);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(213, 0, 0, 0.1);
}

.region-stats-table tbody td {
    padding: 12px;
    vertical-align: middle;
    font-size: 0.9rem;
    border-bottom: 1px solid #dee2e6;
}

/* تنسيق خاص للصف الأول (المنطقة) */
.region-stats-table tbody td:first-child {
    font-weight: 600;
    color: #d50000;
    border-left: 3px solid #d50000;
}

/* تنسيق الأرقام */
.region-stats-table tbody td:not(:first-child):not(:nth-child(2)) {
    text-align: center;
    font-weight: 500;
}

/* تنسيق النسب المئوية */
.region-stats-table tbody td:last-child {
    font-weight: 600;
    color: #28a745;
}

/* تأثيرات الشارات في الرأس */
.region-header-cell .badge {
    font-size: 0.8rem;
    padding: 4px 8px;
    border-radius: 12px;
    font-weight: 600;
}

/* تحسين عداد المناطق */
#total-regions-badge {
    font-size: 0.8rem;
    padding: 6px 12px;
    border-radius: 15px;
    font-weight: 600;
    animation: pulse-badge 2s infinite;
}

@keyframes pulse-badge {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}
