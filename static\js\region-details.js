/**
 * وظائف عرض تفاصيل المناطق
 * يحتوي على الدوال اللازمة لعرض تفاصيل المناطق في نظام الإحصائيات
 */

// دالة لتحليل المتاجر حسب المناطق التفصيلية (باستخدام إحداثيات الخريطة فقط)
function analyzeStoresByDetailedRegion(stores) {
    // تحليل المتاجر حسب المناطق التفصيلية (بما في ذلك المناطق الفرعية)
    const regionMap = {};

    // إضافة جميع المناطق الفرعية في طرابلس وبنغازي
    const allRegions = LibyaRegions.getAllRegions(true);
    allRegions.forEach(region => {
        regionMap[region] = {
            total: 0,
            typeA: 0,
            typeB: 0,
            typeD: 0,
            isSubRegion: region.includes(' - '), // تحديد ما إذا كانت منطقة فرعية
            storesByType: {} // لتخزين المتاجر حسب النوع
        };
    });

    // عداد للمتاجر التي تم معالجتها بنجاح
    let processedStores = 0;
    let storesWithValidCoordinates = 0;

    // تحليل المتاجر
    stores.forEach((store, index) => {
        // التحقق من وجود إحداثيات صالحة
        if (!store.latitude || !store.longitude ||
            isNaN(parseFloat(store.latitude)) ||
            isNaN(parseFloat(store.longitude))) {
            console.warn(`Store at index ${index} has invalid coordinates:`, store.latitude, store.longitude);
            return; // تخطي هذا المتجر
        }

        storesWithValidCoordinates++;

        // استخدام قيمة ثابتة بدلاً من استخراج المنطقة
        const region = "المنطقة غير محددة";

        if (!regionMap[region]) {
            // إذا لم تكن المنطقة موجودة بالفعل، أضفها
            regionMap[region] = {
                total: 0,
                typeA: 0,
                typeB: 0,
                typeD: 0,
                isSubRegion: region.includes(' - '),
                storesByType: {}
            };
        }

        regionMap[region].total++;

        // تحليل حسب نوع المتجر
        if (store.type === 'A') regionMap[region].typeA++;
        else if (store.type === 'B') regionMap[region].typeB++;
        else if (store.type === 'D') regionMap[region].typeD++;

        // تحليل المتاجر حسب النوع
        const storeType = store.type || 'A';
        if (!regionMap[region].storesByType[storeType]) {
            regionMap[region].storesByType[storeType] = {
                count: 0
            };
        }
        regionMap[region].storesByType[storeType].count++;

        processedStores++;
    });

    console.log(`Successfully processed ${processedStores} out of ${stores.length} stores for detailed region analysis.`);
    console.log(`Stores with valid coordinates: ${storesWithValidCoordinates}`);
    console.log(`Unique regions found: ${Object.keys(regionMap).filter(r => regionMap[r].total > 0).length}`);

    return regionMap;
}

// دالة لتحليل المتاجر حسب وصف العنوان ونوع المتجر
function analyzeStoresByAddressAndType(stores) {
    // تحليل المتاجر حسب وصف العنوان ونوع المتجر
    console.log("Analyzing stores by address and type. Total stores:", stores ? stores.length : 0);

    // التحقق من صحة البيانات
    if (!stores || !Array.isArray(stores) || stores.length === 0) {
        console.error("Invalid or empty stores data in analyzeStoresByAddressAndType");
        // إنشاء بيانات تجريبية إذا لم تكن هناك بيانات
        stores = [
            { id: 1, name: "متجر 1", address: "شارع رئيسي", type: "A", latitude: 32.8872, longitude: 13.1913 },
            { id: 2, name: "متجر 2", address: "مركز تجاري", type: "B", latitude: 32.8872, longitude: 13.1913 },
            { id: 3, name: "متجر 3", address: "بجانب المسجد", type: "A", latitude: 32.1167, longitude: 20.0667 },
            { id: 4, name: "متجر 4", address: "قرب المدرسة", type: "D", latitude: 32.1167, longitude: 20.0667 },
            { id: 5, name: "متجر 5", address: "منطقة سكنية", type: "A", latitude: 32.3754, longitude: 15.0925 }
        ];
        console.log("Using sample data instead:", stores.length, "stores");
    }

    try {
        // طباعة عينة من البيانات للتشخيص
        if (stores.length > 0) {
            console.log("Sample store data:", stores[0]);
            console.log("Store keys:", Object.keys(stores[0]));
        }

        const addressMap = {};
        const typeMap = {
            'A': { count: 0, addressDescriptions: {}, regions: {} },
            'B': { count: 0, addressDescriptions: {}, regions: {} },
            'D': { count: 0, addressDescriptions: {}, regions: {} }
        };
        let storesWithAddress = 0;
        let storesWithType = 0;
        let storesWithCoordinates = 0;
        let storesWithValidRegion = 0;

        // تحليل المتاجر
        stores.forEach((store, index) => {
            // التحقق من أن المتجر كائن صالح
            if (!store || typeof store !== 'object') {
                console.warn(`Invalid store object at index ${index}:`, store);
                return; // تخطي هذا المتجر
            }

            // التحقق من وجود حقل العنوان
            if (store.address && typeof store.address === 'string' && store.address.trim() !== '') {
                storesWithAddress++;
                // تنظيف وصف العنوان
                store.address = store.address.trim();
            } else {
                // إضافة حقل العنوان إذا لم يكن موجوداً أو كان فارغاً
                store.address = 'غير محدد';
                console.log(`Added default address to store ${store.id || 'unknown'} at index ${index}`);
            }

            // التحقق من وجود حقل النوع
            if (store.type && ['A', 'B', 'D'].includes(store.type)) {
                storesWithType++;
            } else {
                // إضافة حقل النوع إذا لم يكن موجوداً أو كان غير صالح
                store.type = 'A';
                console.log(`Added default type to store ${store.id || 'unknown'} at index ${index}`);
            }

            // التحقق من وجود حقول الإحداثيات
            if (store.latitude && store.longitude &&
                !isNaN(parseFloat(store.latitude)) && !isNaN(parseFloat(store.longitude))) {
                storesWithCoordinates++;
                // تأكد من أن الإحداثيات أرقام
                store.latitude = parseFloat(store.latitude);
                store.longitude = parseFloat(store.longitude);
            } else {
                // استخدام إحداثيات افتراضية إذا لم تكن موجودة أو كانت غير صالحة
                store.latitude = 32.8872;
                store.longitude = 13.1913;
                console.log(`Added default coordinates to store ${store.id || 'unknown'} at index ${index}`);
            }

            const addressDesc = store.address;
            const storeType = store.type;

            // تحليل حسب وصف العنوان
            if (!addressMap[addressDesc]) {
                addressMap[addressDesc] = {
                    count: 0,
                    types: { A: 0, B: 0, D: 0 },
                    regions: {}
                };
            }
            addressMap[addressDesc].count++;

            // التأكد من أن النوع هو A أو B أو D
            if (['A', 'B', 'D'].includes(storeType)) {
                addressMap[addressDesc].types[storeType]++;
            } else {
                // إذا كان النوع غير معروف، اعتبره من النوع A
                addressMap[addressDesc].types['A']++;
            }

            // استخدام قيمة ثابتة بدلاً من استخراج المنطقة
            try {
                const region = "منطقة غير محددة";
                storesWithValidRegion++;
                if (!addressMap[addressDesc].regions[region]) {
                    addressMap[addressDesc].regions[region] = 0;
                }
                addressMap[addressDesc].regions[region]++;
            } catch (error) {
                console.warn(`Error processing region for store ${store.id || 'unknown'} at index ${index}:`, error);
            }

            // تحليل حسب نوع المتجر
            if (!typeMap[storeType]) {
                typeMap[storeType] = {
                    count: 0,
                    addressDescriptions: {},
                    regions: {}
                };
            }
            typeMap[storeType].count++;

            // تحليل وصف العنوان لكل نوع متجر
            if (!typeMap[storeType].addressDescriptions[addressDesc]) {
                typeMap[storeType].addressDescriptions[addressDesc] = 0;
            }
            typeMap[storeType].addressDescriptions[addressDesc]++;

            // استخدام قيمة ثابتة بدلاً من استخراج المنطقة
            try {
                // استخدام نفس المنطقة التي تم تحديدها سابقاً للاتساق
                const region = "منطقة غير محددة";
                if (!typeMap[storeType].regions[region]) {
                    typeMap[storeType].regions[region] = 0;
                }
                typeMap[storeType].regions[region]++;
            } catch (error) {
                console.warn(`Error processing region for store ${store.id || 'unknown'} at index ${index}:`, error);
            }
        });

        // إذا لم يتم العثور على أي عناوين، أضف بيانات تجريبية
        if (Object.keys(addressMap).length === 0) {
            console.warn("No address data found, adding sample data");
            addressMap["شارع رئيسي"] = { count: 3, types: { A: 2, B: 1, D: 0 }, regions: { "طرابلس": 3 } };
            addressMap["مركز تجاري"] = { count: 2, types: { A: 0, B: 1, D: 1 }, regions: { "بنغازي": 2 } };
            addressMap["منطقة سكنية"] = { count: 1, types: { A: 1, B: 0, D: 0 }, regions: { "مصراتة": 1 } };
        }

        // إذا لم يتم العثور على أي أنواع، أضف بيانات تجريبية
        if (Object.values(typeMap).every(type => type.count === 0)) {
            console.warn("No type data found, adding sample data");
            typeMap["A"] = {
                count: 3,
                addressDescriptions: { "شارع رئيسي": 2, "منطقة سكنية": 1 },
                regions: { "طرابلس": 2, "مصراتة": 1 }
            };
            typeMap["B"] = {
                count: 2,
                addressDescriptions: { "شارع رئيسي": 1, "مركز تجاري": 1 },
                regions: { "طرابلس": 1, "بنغازي": 1 }
            };
            typeMap["D"] = {
                count: 1,
                addressDescriptions: { "مركز تجاري": 1 },
                regions: { "بنغازي": 1 }
            };
        }

        console.log(`Analysis complete. Found ${Object.keys(addressMap).length} unique addresses.`);
        console.log(`${storesWithAddress} stores have address field, ${storesWithType} stores have type field.`);
        console.log(`${storesWithCoordinates} stores have valid coordinates, ${storesWithValidRegion} stores have valid region.`);
        console.log(`Address map keys:`, Object.keys(addressMap));

        return { addressMap, typeMap };
    } catch (error) {
        console.error("Error in analyzeStoresByAddressAndType:", error);
        // إرجاع بيانات تجريبية في حالة حدوث خطأ
        const sampleAddressMap = {
            "شارع رئيسي": { count: 3, types: { A: 2, B: 1, D: 0 }, regions: { "طرابلس": 3 } },
            "مركز تجاري": { count: 2, types: { A: 0, B: 1, D: 1 }, regions: { "بنغازي": 2 } }
        };
        const sampleTypeMap = {
            "A": { count: 2, addressDescriptions: { "شارع رئيسي": 2 }, regions: { "طرابلس": 2 } },
            "B": { count: 2, addressDescriptions: { "شارع رئيسي": 1, "مركز تجاري": 1 }, regions: { "طرابلس": 1, "بنغازي": 1 } },
            "D": { count: 1, addressDescriptions: { "مركز تجاري": 1 }, regions: { "بنغازي": 1 } }
        };
        return { addressMap: sampleAddressMap, typeMap: sampleTypeMap };
    }
}

// دالة لعرض تفاصيل المنطقة
function showRegionDetails(regionName, stores) {
    console.log(`عرض تفاصيل المنطقة: ${regionName}`);
    console.log(`عدد المتاجر المتاحة: ${stores ? stores.length : 0}`);

    if (!stores || stores.length === 0) {
        alert('لا توجد بيانات متاجر متاحة');
        return;
    }

    // تصفية المتاجر حسب المنطقة المحددة
    let storesInRegion = [];
    let regionStats = {
        total: 0,
        typeA: 0,
        typeB: 0,
        typeD: 0
    };

    // معالجة حالة الإجمالي
    if (regionName === "الإجمالي") {
        storesInRegion = stores;
        regionStats = {
            total: stores.length,
            typeA: stores.filter(store => store.type === 'A').length,
            typeB: stores.filter(store => store.type === 'B').length,
            typeD: stores.filter(store => store.type === 'D').length
        };

        // استخدام نفس دالة عرض التفاصيل العادية
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.id = 'regionDetailsModal';
        modal.setAttribute('tabindex', '-1');
        modal.setAttribute('aria-labelledby', 'regionDetailsModalLabel');
        modal.setAttribute('aria-hidden', 'true');

        modal.innerHTML = `
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="regionDetailsModalLabel">
                            <i class="fas fa-exclamation-circle text-warning me-1"></i>
                            تفاصيل إجمالي المتاجر
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card mb-3">
                                    <div class="card-header bg-primary text-white">
                                        <h6 class="mb-0">إحصائيات المتاجر</h6>
                                    </div>
                                    <div class="card-body">
                                        <p><strong>إجمالي المتاجر:</strong> ${totalStats.total}</p>
                                        <p><strong>متاجر نوع A:</strong> ${totalStats.typeA}</p>
                                        <p><strong>متاجر نوع B:</strong> ${totalStats.typeB}</p>
                                        <p><strong>متاجر نوع D:</strong> ${totalStats.typeD}</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header bg-success text-white">
                                        <h6 class="mb-0">توزيع أنواع المتاجر</h6>
                                    </div>
                                    <div class="card-body">
                                        <canvas id="region-type-chart" width="100%" height="200"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mt-3">
                            <h6>قائمة جميع المتاجر:</h6>
                            <div class="table-responsive">
                                <table class="table table-striped table-sm">
                                    <thead>
                                        <tr>
                                            <th>اسم المتجر</th>
                                            <th>النوع</th>
                                            <th>المنطقة</th>
                                            <th>رقم الهاتف</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody id="region-stores-list">
                                        <!-- سيتم ملؤها بواسطة JavaScript -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        const modalInstance = new bootstrap.Modal(modal);
        modalInstance.show();

        // ملء جدول المتاجر
        const storesList = document.getElementById('region-stores-list');
        stores.forEach(store => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${store.name}</td>
                <td><span class="badge bg-${store.type === 'A' ? 'success' : store.type === 'B' ? 'primary' : 'warning text-dark'}">${store.type}</span></td>
                <td>منطقة غير محددة</td>
                <td>${store.phone || 'غير متوفر'}</td>
                <td>
                    <button class="btn btn-sm btn-outline-info view-store-btn" data-store-id="${store.id}">
                        <i class="fas fa-eye"></i>
                    </button>
                </td>
            `;
            storesList.appendChild(row);
        });

        // إنشاء الرسم البياني
        const ctx = document.getElementById('region-type-chart').getContext('2d');
        new Chart(ctx, {
            type: 'pie',
            data: {
                labels: ['نوع A', 'نوع B', 'نوع D'],
                datasets: [{
                    data: [totalStats.typeA, totalStats.typeB, totalStats.typeD],
                    backgroundColor: ['#28a745', '#007bff', '#ffc107']
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });

        // إضافة مستمعي الأحداث لأزرار عرض المتجر
        document.querySelectorAll('.view-store-btn').forEach(button => {
            button.addEventListener('click', function() {
                const storeId = this.getAttribute('data-store-id');
                const store = stores.find(s => s.id == storeId);
                if (store) {
                    // إغلاق النافذة المنبثقة الحالية
                    modalInstance.hide();

                    // الانتقال إلى خريطة الإحصائيات وتحديد المتجر
                    setTimeout(() => {
                        // الحصول على عنصر الخريطة
                        const statsMap = document.getElementById('stats-map');
                        if (statsMap) {
                            // التمرير إلى الخريطة
                            statsMap.scrollIntoView({ behavior: 'smooth' });

                            // الحصول على كائن الخريطة
                            if (window.statsMapInstance) {
                                // تحريك الخريطة إلى موقع المتجر
                                window.statsMapInstance.setView([store.latitude, store.longitude], 15);

                                // إنشاء علامة مميزة للمتجر المحدد
                                const highlightedMarker = L.marker([store.latitude, store.longitude], {
                                    icon: L.divIcon({
                                        className: 'store-marker-highlighted',
                                        html: `<div class="map-marker-highlighted pulse-animation"><i class="fas fa-store"></i></div>`,
                                        iconSize: [40, 40],
                                        iconAnchor: [20, 40]
                                    })
                                }).addTo(window.statsMapInstance);

                                // إضافة نافذة منبثقة للمتجر المحدد
                                highlightedMarker.bindPopup(`
                                    <div class="store-popup">
                                        <h5>${store.name}</h5>
                                        <p class="small mb-1" style="color: #dc3545; font-weight: 500;">المنطقة غير محددة</p>
                                        <p class="mb-1">${store.phone || 'لا يوجد رقم هاتف'}</p>
                                        <div class="d-flex justify-content-between">
                                            <button class="btn btn-sm btn-primary" onclick="window.location.href='/?store=${store.id}'" style="background: linear-gradient(135deg, var(--loacker-red-dark), var(--loacker-red)); border: none;">
                                                <i class="fas fa-eye"></i> عرض التفاصيل
                                            </button>
                                        </div>
                                    </div>
                                `).openPopup();

                                // إضافة تأثير تدوير الكاميرا 360 درجة
                                // تحقق من وجود دالة setBearing في كائن الخريطة
                                if (typeof window.statsMapInstance.setBearing === 'function') {
                                    let angle = 0;
                                    const rotationInterval = setInterval(() => {
                                        angle += 5;
                                        if (angle >= 360) {
                                            clearInterval(rotationInterval);
                                            // إعادة الخريطة إلى الوضع الطبيعي بعد الانتهاء من التدوير
                                            window.statsMapInstance.setBearing(0);
                                        } else {
                                            window.statsMapInstance.setBearing(angle);
                                        }
                                    }, 50);
                                } else {
                                    console.log("Map rotation not supported in this Leaflet version");
                                    // استخدام تأثير بديل - تكبير وتصغير الخريطة
                                    const currentZoom = window.statsMapInstance.getZoom();
                                    window.statsMapInstance.setZoom(currentZoom + 2);
                                    setTimeout(() => {
                                        window.statsMapInstance.setZoom(currentZoom);
                                    }, 1000);
                                }

                                // إزالة العلامة المميزة بعد 10 ثوانٍ
                                setTimeout(() => {
                                    window.statsMapInstance.removeLayer(highlightedMarker);
                                }, 10000);
                            }
                        }
                    }, 500);
                }
            });
        });

        // تنظيف عند إغلاق النافذة المنبثقة
        modal.addEventListener('hidden.bs.modal', function() {
            document.body.removeChild(modal);
        });

        return;
    } else {
        // تصفية المتاجر حسب المنطقة المحددة
        // البحث في عنوان المتجر أو استخدام الإحداثيات
        storesInRegion = stores.filter(store => {
            // البحث في حقل العنوان أولاً
            if (store.address && store.address.includes(regionName)) {
                return true;
            }

            // البحث في اسم المتجر
            if (store.name && store.name.includes(regionName)) {
                return true;
            }

            // إذا كانت المنطقة تحتوي على " - " فهي منطقة فرعية
            if (regionName.includes(' - ')) {
                const [mainCity, subRegion] = regionName.split(' - ');
                return (store.address && store.address.includes(mainCity) && store.address.includes(subRegion)) ||
                       (store.address && store.address.includes(regionName));
            }

            return false;
        });

        // حساب الإحصائيات للمنطقة
        regionStats = {
            total: storesInRegion.length,
            typeA: storesInRegion.filter(store => store.type === 'A').length,
            typeB: storesInRegion.filter(store => store.type === 'B').length,
            typeD: storesInRegion.filter(store => store.type === 'D').length
        };

        console.log(`تم العثور على ${storesInRegion.length} متجر في منطقة ${regionName}`);
        console.log('إحصائيات المنطقة:', regionStats);

        if (storesInRegion.length === 0) {
            alert(`لا توجد متاجر في منطقة ${regionName}`);
            return;
        }
    }

    // إنشاء نافذة منبثقة لعرض التفاصيل
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.id = 'regionDetailsModal';
    modal.setAttribute('tabindex', '-1');
    modal.setAttribute('aria-labelledby', 'regionDetailsModalLabel');
    modal.setAttribute('aria-hidden', 'true');

    // محتوى النافذة المنبثقة
    modal.innerHTML = `
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="regionDetailsModalLabel">
                        تفاصيل منطقة: ${regionName}
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card mb-3">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0">إحصائيات المتاجر</h6>
                                </div>
                                <div class="card-body">
                                    <p><strong>إجمالي المتاجر:</strong> ${regionStats.total}</p>
                                    <p><strong>متاجر نوع A:</strong> ${regionStats.typeA}</p>
                                    <p><strong>متاجر نوع B:</strong> ${regionStats.typeB}</p>
                                    <p><strong>متاجر نوع D:</strong> ${regionStats.typeD}</p>
                                    <hr>
                                    <h6 class="mb-2">توزيع المتاجر حسب وصف العنوان:</h6>
                                    <div class="address-descriptions-list" style="max-height: 150px; overflow-y: auto;">
                                        <ul class="list-group list-group-flush">
                                            ${Object.entries(regionStats.addressDescriptions || {}).map(([address, data]) => `
                                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                                    <span>${address}</span>
                                                    <span class="badge bg-primary rounded-pill">${data.count}</span>
                                                </li>
                                            `).join('')}
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0">معلومات الموقع</h6>
                                </div>
                                <div class="card-body">
                                    <div id="region-mini-map" style="height: 300px; border-radius: 5px;"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mt-3">
                        <h6>قائمة المتاجر في المنطقة:</h6>
                        <div class="table-responsive">
                            <table class="table table-striped table-sm">
                                <thead>
                                    <tr>
                                        <th>اسم المتجر</th>
                                        <th>النوع</th>
                                        <th>وصف العنوان</th>
                                        <th>رقم الهاتف</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="region-stores-list">
                                    <!-- سيتم ملؤها بواسطة JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                    <button type="button" class="btn btn-primary" onclick="filterStoresByRegion('${regionName}')">
                        <i class="fas fa-filter"></i> تصفية المتاجر حسب هذه المنطقة
                    </button>
                </div>
            </div>
        </div>
    `;

    // إضافة النافذة المنبثقة إلى الصفحة
    document.body.appendChild(modal);

    // إنشاء كائن النافذة المنبثقة
    const modalInstance = new bootstrap.Modal(document.getElementById('regionDetailsModal'));

    // عرض النافذة المنبثقة
    modalInstance.show();

    // إنشاء خريطة مصغرة للمنطقة
    setTimeout(() => {
        const coordinates = LibyaRegions.getRegionCoordinates(regionName);
        if (coordinates) {
            // إزالة الخريطة السابقة إذا كانت موجودة
            const miniMapContainer = document.getElementById('region-mini-map');
            if (miniMapContainer._leaflet_id) {
                miniMapContainer._leaflet_id = null;
            }

            // إنشاء خريطة جديدة
            const miniMap = L.map('region-mini-map', {
                preferCanvas: true,
                zoomControl: true,
                attributionControl: true,
                dragging: true,
                scrollWheelZoom: true,
                doubleClickZoom: true,
                touchZoom: true
            }).setView([coordinates.lat, coordinates.lng], 10);

            // تخزين مرجع الخريطة في متغير عام
            window.regionMiniMap = miniMap;

            // إضافة طبقة الخريطة (بالوضع العادي)
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
            }).addTo(miniMap);

            // إضافة علامة للمنطقة
            L.marker([coordinates.lat, coordinates.lng], {
                icon: L.divIcon({
                    className: 'region-marker',
                    html: `<div class="region-marker-icon">${regionStats.total}</div>`,
                    iconSize: [40, 40],
                    iconAnchor: [20, 20]
                })
            }).addTo(miniMap);

            // إضافة المتاجر في المنطقة إلى الخريطة
            const storesInRegion = stores.filter(store => {
                // التحقق من وجود إحداثيات صالحة
                if (!store.latitude || !store.longitude ||
                    isNaN(parseFloat(store.latitude)) ||
                    isNaN(parseFloat(store.longitude))) {
                    return false;
                }
                // استخدام إحداثيات الخريطة فقط لتحديد المنطقة
                return LibyaRegions.getRegionFromCoordinates(store.latitude, store.longitude) === regionName;
            });

            // إضافة المتاجر إلى الخريطة
            storesInRegion.forEach(store => {
                if (store.latitude && store.longitude) {
                    // استخراج المنطقة من إحداثيات الخريطة
                    const storeRegion = LibyaRegions.getRegionFromCoordinates(store.latitude, store.longitude);

                    // إضافة علامة دبوس أحمر للمتجر
                    L.marker([store.latitude, store.longitude], {
                        icon: L.divIcon({
                            className: 'store-marker',
                            html: `<i class="fas fa-map-marker-alt" style="color: #d50000; font-size: 24px; text-shadow: 1px 1px 2px rgba(0,0,0,0.5);"></i>`,
                            iconSize: [24, 24],
                            iconAnchor: [12, 24]
                        })
                    }).addTo(miniMap)
                    .bindPopup(`
                        <div class="store-popup" style="max-width: 150px; font-size: 12px;">
                            <h6 style="font-size: 14px; margin-bottom: 5px;">${store.name}</h6>
                            <p class="text-muted small mb-1" style="margin-bottom: 3px;">منطقة غير محددة</p>
                            <p class="mb-1" style="margin-bottom: 5px;">${store.phone || 'لا يوجد رقم هاتف'}</p>
                        </div>
                    `);
                }
            });

            // ملء جدول المتاجر
            const storesList = document.getElementById('region-stores-list');
            if (storesList) {
                storesList.innerHTML = '';

                storesInRegion.forEach(store => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${store.name || 'غير محدد'}</td>
                        <td><span class="badge ${store.type === 'A' ? 'bg-success' : store.type === 'B' ? 'bg-primary' : 'bg-warning text-dark'}">${store.type || 'غير محدد'}</span></td>
                        <td>${store.address || regionName}</td>
                        <td>${store.phone || 'غير متوفر'}</td>
                        <td>
                            <button class="btn btn-sm btn-outline-success show-on-map-btn" data-lat="${store.latitude}" data-lng="${store.longitude}" data-name="${store.name}" title="عرض على الخريطة">
                                <i class="fas fa-map-marker-alt"></i>
                            </button>
                        </td>
                    `;

                // إضافة مستمع حدث لزر عرض المتجر على الخريطة
                const showOnMapBtn = row.querySelector('.show-on-map-btn');
                if (showOnMapBtn) {
                    showOnMapBtn.addEventListener('click', function() {
                        const lat = parseFloat(this.getAttribute('data-lat'));
                        const lng = parseFloat(this.getAttribute('data-lng'));
                        const name = this.getAttribute('data-name');

                        // التمرير إلى الخريطة في النافذة
                        const miniMapContainer = document.getElementById('region-mini-map');
                        if (miniMapContainer) {
                            // التمرير إلى الخريطة بتأثير سلس
                            miniMapContainer.scrollIntoView({ behavior: 'smooth', block: 'center' });
                        }

                        // استخدام المتغير العام للخريطة
                        if (window.regionMiniMap) {
                            // تحريك الخريطة إلى موقع المتجر
                            window.regionMiniMap.setView([lat, lng], 15);

                            // إزالة العلامات المميزة السابقة
                            if (window.selectedStoreMarker) {
                                window.regionMiniMap.removeLayer(window.selectedStoreMarker);
                            }

                            // إضافة علامة مميزة للمتجر المحدد
                            window.selectedStoreMarker = L.marker([lat, lng], {
                                icon: L.divIcon({
                                    className: 'selected-store-marker',
                                    html: `<i class="fas fa-map-marker-alt pulse-animation" style="color: #d50000; font-size: 30px;"></i>`,
                                    iconSize: [30, 30],
                                    iconAnchor: [15, 30]
                                })
                            }).addTo(window.regionMiniMap);

                            // إضافة نافذة منبثقة للمتجر
                            window.selectedStoreMarker.bindPopup(`
                                <div class="store-popup" style="max-width: 200px; font-size: 12px;">
                                    <h6 style="font-size: 14px; margin-bottom: 5px;">${name}</h6>
                                    <p class="text-muted small mb-1" style="margin-bottom: 3px;">${store.address || 'غير محدد'}</p>
                                    <p class="mb-1" style="margin-bottom: 5px;">${store.phone || 'لا يوجد رقم هاتف'}</p>
                                </div>
                            `).openPopup();

                            // إضافة تأثير تدوير الكاميرا 360 درجة
                            // تحقق من وجود دالة setRotationAngle في كائن العلامة
                            if (window.selectedStoreMarker && typeof window.selectedStoreMarker.setRotationAngle === 'function') {
                                let angle = 0;
                                const rotationInterval = setInterval(() => {
                                    angle += 5;
                                    if (angle >= 360) {
                                        clearInterval(rotationInterval);
                                        // إعادة العلامة إلى الوضع الطبيعي بعد الانتهاء من التدوير
                                        window.selectedStoreMarker.setRotationAngle(0);
                                    } else {
                                        // تدوير العلامة بدلاً من الخريطة (لأن Leaflet لا يدعم تدوير الخريطة مباشرة)
                                        window.selectedStoreMarker.setRotationAngle(angle);
                                    }
                                }, 50);
                            } else {
                                console.log("Marker rotation not supported, using animation instead");
                                // استخدام تأثير نبض بديل
                                const markerIcon = window.selectedStoreMarker.getElement();
                                if (markerIcon) {
                                    markerIcon.classList.add('pulse-animation');
                                    setTimeout(() => {
                                        markerIcon.classList.remove('pulse-animation');
                                    }, 2000);
                                }
                            }

                            // تحريك الخريطة بتأثير انتقالي سلس
                            window.regionMiniMap.flyTo([lat, lng], 15, {
                                duration: 1.5,
                                easeLinearity: 0.5
                            });
                        }
                    });
                }
                storesList.appendChild(row);
            });

            console.log(`تم إضافة ${storesInRegion.length} متجر إلى الجدول`);
        } else {
            console.error('لم يتم العثور على عنصر جدول المتاجر (region-stores-list)');
        }
        }
    }, 500);
}

// دالة لإضافة أزرار عرض التفاصيل إلى جدول المناطق
function addRegionDetailsButtons(stores) {
    // إضافة زر عرض التفاصيل لكل منطقة في الجدول
    const regionRows = document.querySelectorAll('#region-stats-table tr');

    regionRows.forEach(row => {
        const regionNameCell = row.querySelector('td:first-child');
        if (regionNameCell) {
            const regionName = regionNameCell.querySelector('.fw-bold').textContent;

            // إنشاء زر عرض التفاصيل
            const detailsBtn = document.createElement('button');
            detailsBtn.className = 'btn btn-sm btn-outline-info ms-2';
            detailsBtn.innerHTML = '<i class="fas fa-info-circle"></i>';
            detailsBtn.title = 'عرض تفاصيل المنطقة';
            detailsBtn.onclick = function(e) {
                e.preventDefault();
                showRegionDetails(regionName, stores);
            };

            // إضافة الزر إلى الخلية
            regionNameCell.appendChild(detailsBtn);
        }
    });
}

// تم استبدال هذه الدالة بالاستدعاء المباشر لـ LibyaRegions.getRegionFromCoordinates
