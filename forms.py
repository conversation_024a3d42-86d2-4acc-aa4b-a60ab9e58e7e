from flask_wtf import FlaskForm
from wtforms import StringField, PasswordField, BooleanField, SelectField, SubmitField
from wtforms.validators import DataRequired, Length, EqualTo, ValidationError, Regexp
from models import User

class LoginForm(FlaskForm):
    """نموذج تسجيل الدخول"""
    username = StringField('اسم المستخدم', validators=[DataRequired(message='يرجى إدخال اسم المستخدم')])
    password = PasswordField('كلمة المرور', validators=[DataRequired(message='يرجى إدخال كلمة المرور')])
    remember = BooleanField('تذكرني')
    submit = SubmitField('تسجيل الدخول')

class RegisterForm(FlaskForm):
    """نموذج تسجيل مستخدم جديد"""
    username = StringField('اسم المستخدم', validators=[
        DataRequired(message='يرجى إدخال اسم المستخدم'),
        Length(min=3, max=20, message='يجب أن يكون اسم المستخدم بين 3 و 20 حرفًا')
    ])
    phone = StringField('رقم الهاتف', validators=[
        DataRequired(message='يرجى إدخال رقم الهاتف'),
        Regexp(r'^[0-9]{10,15}$', message='يرجى إدخال رقم هاتف صحيح من 10 إلى 15 رقم')
    ])
    password = PasswordField('كلمة المرور', validators=[
        DataRequired(message='يرجى إدخال كلمة المرور'),
        Length(min=6, message='يجب أن تكون كلمة المرور 6 أحرف على الأقل')
    ])
    confirm_password = PasswordField('تأكيد كلمة المرور', validators=[
        DataRequired(message='يرجى تأكيد كلمة المرور'),
        EqualTo('password', message='كلمات المرور غير متطابقة')
    ])
    submit = SubmitField('تسجيل')

    def validate_username(self, username):
        """التحقق من عدم وجود اسم المستخدم مسبقًا"""
        user = User.get_by_username(username.data)
        if user:
            raise ValidationError('اسم المستخدم موجود بالفعل، يرجى اختيار اسم آخر')

    def validate_phone(self, phone):
        """التحقق من عدم وجود رقم الهاتف مسبقًا"""
        user = User.get_by_phone(phone.data)
        if user:
            raise ValidationError('رقم الهاتف موجود بالفعل، يرجى استخدام رقم آخر')

class UserForm(FlaskForm):
    """نموذج إدارة المستخدمين"""
    username = StringField('اسم المستخدم', validators=[
        DataRequired(message='يرجى إدخال اسم المستخدم'),
        Length(min=3, max=20, message='يجب أن يكون اسم المستخدم بين 3 و 20 حرفًا')
    ])
    phone = StringField('رقم الهاتف', validators=[
        DataRequired(message='يرجى إدخال رقم الهاتف'),
        Regexp(r'^[0-9]{10,15}$', message='يرجى إدخال رقم هاتف صحيح من 10 إلى 15 رقم')
    ])
    password = PasswordField('كلمة المرور', validators=[
        Length(min=6, message='يجب أن تكون كلمة المرور 6 أحرف على الأقل')
    ])
    role_id = SelectField('الدور', coerce=int)
    is_active = BooleanField('نشط')
    submit = SubmitField('حفظ')
