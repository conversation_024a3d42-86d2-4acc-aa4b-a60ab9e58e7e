/**
 * دالة إنشاء جدول تحليل المتاجر حسب المناطق (المستخرجة من إحداثيات الخريطة)
 * تستخدم لعرض بيانات المتاجر مصنفة حسب المناطق
 */
function createAddressTable(addressMap) {
    // إنشاء جدول تحليل المتاجر حسب المناطق
    console.log("Creating region-based address table with data:", addressMap);

    const tableBody = document.getElementById('address-stats-table');
    if (!tableBody) {
        console.error("Could not find address-stats-table element");
        return;
    }

    tableBody.innerHTML = '';

    // التحقق من وجود بيانات
    if (!addressMap || typeof addressMap !== 'object' || Object.keys(addressMap).length === 0) {
        console.warn("No region data available in createAddressTable");
        const row = document.createElement('tr');
        row.innerHTML = '<td colspan="5" class="text-center">لا توجد بيانات للمناطق</td>';
        tableBody.appendChild(row);
        return;
    }

    try {
        console.log("Found " + Object.keys(addressMap).length + " region entries to display in table");

        // ترتيب المناطق حسب عدد المتاجر (تنازلياً)
        const sortedRegions = Object.entries(addressMap)
            .sort((a, b) => {
                // التأكد من وجود حقل count
                const countA = a[1] && typeof a[1] === 'object' && 'count' in a[1] ? a[1].count : 0;
                const countB = b[1] && typeof b[1] === 'object' && 'count' in b[1] ? b[1].count : 0;
                return countB - countA;
            });

        console.log("Sorted regions:", sortedRegions);

        // حساب المجاميع
        let totalCount = 0;
        let totalTypeA = 0;
        let totalTypeB = 0;
        let totalTypeD = 0;

        // إضافة صفوف الجدول بعد الترتيب
        sortedRegions.forEach(([region, data]) => {
            // التأكد من أن البيانات صالحة
            if (!data || typeof data !== 'object') {
                console.warn(`Invalid data for region: ${region}`);
                return;
            }

            // التأكد من وجود حقل types
            const types = data.types || { A: 0, B: 0, D: 0, 'أخرى': 0 };

            // تحديث المجاميع
            totalCount += (data.count || 0);
            totalTypeA += (types.A || 0);
            totalTypeB += (types.B || 0);
            totalTypeD += (types.D || 0);

            // تحديد ما إذا كانت منطقة فرعية
            const isSubRegion = region.includes(' - ');
            const rowClass = isSubRegion ? 'subregion-row' : '';

            // إنشاء صف الجدول
            const row = document.createElement('tr');
            row.className = rowClass;

            // إذا كانت منطقة فرعية، إضافة مسافة بادئة
            const regionDisplay = isSubRegion ?
                `<span class="ms-3">- ${region.split(' - ')[1]}</span>` :
                `<span>${region || 'غير محدد'}</span>`;

            row.innerHTML = `
                <td>${regionDisplay}</td>
                <td>${data.count || 0}</td>
                <td>${types.A || 0}</td>
                <td>${types.B || 0}</td>
                <td>${types.D || 0}</td>
            `;
            tableBody.appendChild(row);
        });

        // إضافة صف المجموع
        const totalRow = document.createElement('tr');
        totalRow.className = 'table-active fw-bold';
        totalRow.innerHTML = `
            <td>المجموع</td>
            <td>${totalCount}</td>
            <td>${totalTypeA}</td>
            <td>${totalTypeB}</td>
            <td>${totalTypeD}</td>
        `;
        tableBody.appendChild(totalRow);

        console.log("Region-based address table created with", sortedRegions.length, "rows");
    } catch (error) {
        console.error("Error creating region-based address table:", error);
        const row = document.createElement('tr');
        row.innerHTML = '<td colspan="5" class="text-center">حدث خطأ أثناء إنشاء الجدول</td>';
        tableBody.appendChild(row);
    }
}
