/**
 * إدارة خريطة المتاجر المعلقة في صفحة المدير
 */
class AdminMapManager {
    constructor() {
        this.map = null;
        this.markers = [];
        this.mapContainer = null;
        this.mapModal = null;
        this.currentStoreId = null;

        // إنشاء عناصر الخريطة
        this.createMapElements();

        // تهيئة الخريطة
        this.initMap();
    }

    /**
     * إنشاء عناصر الخريطة في DOM
     */
    createMapElements() {
        // إنشاء النافذة المنبثقة للخريطة
        const mapModalHTML = `
            <div class="modal fade" id="storeLocationModal" tabindex="-1" aria-labelledby="storeLocationModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="storeLocationModalLabel">موقع المتجر</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body p-0">
                            <div id="admin-map" style="height: 500px; width: 100%;"></div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                            <button type="button" class="btn btn-primary" id="approveStoreFromMap">
                                <i class="fas fa-check"></i> موافقة على المتجر
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // إضافة النافذة المنبثقة إلى DOM
        document.body.insertAdjacentHTML('beforeend', mapModalHTML);

        // تخزين مراجع العناصر
        this.mapContainer = document.getElementById('admin-map');
        this.mapModal = document.getElementById('storeLocationModal');

        // إضافة مستمع حدث لزر الموافقة من الخريطة
        document.getElementById('approveStoreFromMap').addEventListener('click', () => {
            if (this.currentStoreId) {
                // البحث عن زر الموافقة للمتجر الحالي وتنفيذ النقر عليه
                const approveBtn = document.querySelector(`.approve-store-btn[data-id="${this.currentStoreId}"]`);
                if (approveBtn) {
                    approveBtn.click();
                }

                // إغلاق النافذة المنبثقة
                bootstrap.Modal.getInstance(this.mapModal).hide();
            }
        });
    }

    /**
     * تهيئة الخريطة
     */
    initMap() {
        // التحقق من تحميل مكتبة Leaflet
        if (typeof L === 'undefined') {
            console.error('Leaflet library is not loaded');
            return;
        }

        // إضافة مستمع حدث لتهيئة الخريطة عند فتح النافذة المنبثقة
        this.mapModal.addEventListener('shown.bs.modal', () => {
            if (!this.map) {
                // إنشاء الخريطة (طرابلس، ليبيا)
                this.map = L.map('admin-map').setView([32.8872, 13.1913], 13);

                // إضافة طبقة الخريطة العادية
                L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                    attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                }).addTo(this.map);

                // إضافة طبقة القمر الصناعي
                const satelliteLayer = L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', {
                    attribution: 'Tiles &copy; Esri &mdash; Source: Esri, i-cubed, USDA, USGS, AEX, GeoEye, Getmapping, Aerogrid, IGN, IGP, UPR-EGP, and the GIS User Community'
                });

                // إضافة متحكم الطبقات
                const baseLayers = {
                    'خريطة': L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                        attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                    }).addTo(this.map),
                    'قمر صناعي': satelliteLayer
                };

                L.control.layers(baseLayers, null, {
                    position: 'topright',
                    collapsed: true
                }).addTo(this.map);
            }

            // تحديث حجم الخريطة
            this.map.invalidateSize();
        });
    }

    /**
     * عرض موقع متجر على الخريطة
     * @param {Object} store - بيانات المتجر
     */
    showStoreLocation(store) {
        // تخزين معرف المتجر الحالي
        this.currentStoreId = store.id;

        // التحقق من وجود إحداثيات
        if (!store.latitude || !store.longitude) {
            console.error('Store location coordinates are missing');
            return;
        }

        // فتح النافذة المنبثقة
        const modal = new bootstrap.Modal(this.mapModal);
        modal.show();

        // تحديث عنوان النافذة المنبثقة
        document.getElementById('storeLocationModalLabel').textContent = `موقع المتجر: ${store.name}`;

        // الانتظار حتى تكتمل تهيئة الخريطة
        this.mapModal.addEventListener('shown.bs.modal', () => {
            // إزالة العلامات السابقة
            this.clearMarkers();

            // إضافة علامة للمتجر
            const marker = L.marker([store.latitude, store.longitude], {
                icon: L.divIcon({
                    html: '<i class="fas fa-map-marker-alt" style="color: #d50000; font-size: 24px;"></i>',
                    iconSize: [24, 24],
                    iconAnchor: [12, 24],
                    className: 'store-marker-container'
                })
            }).addTo(this.map);

            // إضافة نافذة منبثقة للعلامة
            marker.bindPopup(`
                <div class="store-popup text-center">
                    <h5 class="mb-2">${store.name}</h5>
                    <div class="d-flex align-items-center justify-content-center mb-2">
                        <i class="fas fa-phone-alt text-primary me-2"></i>
                        <span>${store.phone || 'لا يوجد رقم هاتف'}</span>
                    </div>
                    <div class="d-flex align-items-center justify-content-center mb-2">
                        <i class="fas fa-user text-secondary me-2"></i>
                        <span>المسوق: ${store.marketer_name || 'غير معروف'}</span>
                    </div>
                    <div class="d-flex align-items-center justify-content-center mb-3">
                        <i class="fas fa-map-pin text-danger me-2"></i>
                        <span>${store.latitude.toFixed(6)}, ${store.longitude.toFixed(6)}</span>
                    </div>
                </div>
            `).openPopup();

            // تخزين العلامة
            this.markers.push(marker);

            // الانتقال إلى موقع المتجر
            this.map.setView([store.latitude, store.longitude], 16, {
                animate: true,
                duration: 1
            });
        }, { once: true });
    }

    /**
     * إزالة جميع العلامات من الخريطة
     */
    clearMarkers() {
        this.markers.forEach(marker => {
            this.map.removeLayer(marker);
        });
        this.markers = [];
    }
}

// إنشاء كائن مدير الخريطة عند تحميل الصفحة
let adminMapManager;
document.addEventListener('DOMContentLoaded', function() {
    adminMapManager = new AdminMapManager();
});
