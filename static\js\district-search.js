/**
 * ملف JavaScript لإضافة وظيفة البحث في قائمة المناطق المنسدلة
 */

document.addEventListener('DOMContentLoaded', function() {
    // تهيئة وظيفة البحث في القائمة المنسدلة للمناطق
    initDistrictSearch('districtSelect', 'citySelect');
    initDistrictSearch('mobile-district-select', 'mobile-city-select');
    initDistrictSearch('mobileDistrictSelect', 'mobileCitySelect');
});

/**
 * تهيئة وظيفة البحث في القائمة المنسدلة للمناطق
 * @param {string} districtSelectId - معرف عنصر قائمة المناطق المنسدلة
 * @param {string} citySelectId - معرف عنصر قائمة المدن المنسدلة
 */
function initDistrictSearch(districtSelectId, citySelectId) {
    const districtSelect = document.getElementById(districtSelectId);
    const citySelect = document.getElementById(citySelectId);

    if (!districtSelect) return;

    // إنشاء حاوية القائمة المنسدلة المخصصة
    const customSelectContainer = document.createElement('div');
    customSelectContainer.className = 'custom-select-container';
    customSelectContainer.id = `${districtSelectId}-container`;

    // إنشاء حقل البحث
    const searchContainer = document.createElement('div');
    searchContainer.className = 'district-search-container';

    const searchInput = document.createElement('input');
    searchInput.type = 'text';
    searchInput.className = 'district-search-input';
    searchInput.placeholder = 'ابحث عن منطقة...';
    searchInput.id = `${districtSelectId}-search`;

    searchContainer.appendChild(searchInput);

    // إنشاء القائمة المنسدلة المخصصة
    const customDropdown = document.createElement('div');
    customDropdown.className = 'custom-select-dropdown';
    customDropdown.id = `${districtSelectId}-dropdown`;

    // إضافة العناصر إلى الصفحة
    customSelectContainer.appendChild(searchContainer);
    customSelectContainer.appendChild(customDropdown);

    // إخفاء القائمة المنسدلة الأصلية وإضافة القائمة المخصصة
    districtSelect.style.display = 'none';
    districtSelect.parentNode.insertBefore(customSelectContainer, districtSelect.nextSibling);

    // إنشاء عنصر الإدخال الرئيسي
    const mainInput = document.createElement('input');
    mainInput.type = 'text';
    mainInput.className = 'form-control';
    mainInput.readOnly = true;
    mainInput.placeholder = 'المنطقة غير محددة';
    mainInput.style.color = '#dc3545'; // اللون الأحمر
    mainInput.id = `${districtSelectId}-input`;

    // إضافة عنصر الإدخال الرئيسي قبل حاوية القائمة المخصصة
    districtSelect.parentNode.insertBefore(mainInput, customSelectContainer);

    // تحديث القائمة المنسدلة المخصصة بناءً على المدينة المحددة
    function updateCustomDropdown() {
        // الحصول على المدينة المحددة
        const selectedCity = citySelect ? citySelect.value : '';

        // تفريغ القائمة المنسدلة المخصصة
        customDropdown.innerHTML = '';

        // تعطيل/تفعيل عنصر الإدخال الرئيسي بناءً على حالة القائمة المنسدلة الأصلية
        mainInput.disabled = districtSelect.disabled;

        if (districtSelect.disabled) {
            searchInput.disabled = true;
            return;
        } else {
            searchInput.disabled = false;
        }

        // الحصول على مجموعات الخيارات من القائمة المنسدلة الأصلية
        const optgroups = districtSelect.querySelectorAll('optgroup');

        optgroups.forEach(optgroup => {
            // التحقق مما إذا كانت المجموعة مرئية بناءً على المدينة المحددة
            const isVisible = optgroup.style.display !== 'none';
            if (!isVisible) return;

            // إنشاء عنصر مجموعة في القائمة المخصصة
            const groupElement = document.createElement('div');
            groupElement.className = 'custom-select-group';
            groupElement.textContent = optgroup.label;
            customDropdown.appendChild(groupElement);

            // إضافة خيارات المجموعة
            const options = optgroup.querySelectorAll('option');
            options.forEach(option => {
                if (option.value === '') return; // تخطي الخيار الافتراضي

                const optionElement = document.createElement('div');
                optionElement.className = 'custom-select-option';
                optionElement.textContent = option.textContent;
                optionElement.dataset.value = option.value;

                // إضافة مستمع النقر
                optionElement.addEventListener('click', function() {
                    // تحديث قيمة القائمة المنسدلة الأصلية
                    districtSelect.value = this.dataset.value;

                    // تحديث عنصر الإدخال الرئيسي
                    mainInput.value = this.textContent;

                    // إخفاء القائمة المنسدلة المخصصة
                    customDropdown.classList.remove('show');

                    // تشغيل حدث التغيير على القائمة المنسدلة الأصلية
                    const event = new Event('change');
                    districtSelect.dispatchEvent(event);
                });

                customDropdown.appendChild(optionElement);
            });
        });
    }

    // مستمع النقر على عنصر الإدخال الرئيسي
    mainInput.addEventListener('click', function() {
        if (this.disabled) return;

        // تحديث القائمة المنسدلة المخصصة
        updateCustomDropdown();

        // إظهار/إخفاء القائمة المنسدلة المخصصة
        customDropdown.classList.toggle('show');

        // مسح حقل البحث
        searchInput.value = '';

        // إظهار جميع الخيارات
        const options = customDropdown.querySelectorAll('.custom-select-option');
        options.forEach(option => {
            option.classList.remove('hidden');
        });

        // التركيز على حقل البحث
        searchInput.focus();
    });

    // مستمع حدث البحث
    searchInput.addEventListener('input', function() {
        const searchText = this.value.trim().toLowerCase();

        // البحث في الخيارات
        const options = customDropdown.querySelectorAll('.custom-select-option');
        options.forEach(option => {
            const optionText = option.textContent.toLowerCase();
            if (optionText.includes(searchText)) {
                option.classList.remove('hidden');
            } else {
                option.classList.add('hidden');
            }
        });
    });

    // مستمع حدث تغيير المدينة
    if (citySelect) {
        citySelect.addEventListener('change', function() {
            // إعادة تعيين عنصر الإدخال الرئيسي
            mainInput.value = '';

            // تحديث القائمة المنسدلة المخصصة
            updateCustomDropdown();
        });
    }

    // إغلاق القائمة المنسدلة عند النقر خارجها
    document.addEventListener('click', function(event) {
        if (!customSelectContainer.contains(event.target) && !mainInput.contains(event.target)) {
            customDropdown.classList.remove('show');
        }
    });

    // تهيئة القائمة المنسدلة المخصصة
    updateCustomDropdown();
}
