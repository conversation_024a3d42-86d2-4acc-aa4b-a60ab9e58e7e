/**
 * MapManager class for handling map operations
 * Created: 15-04-2024
 */
class MapManager {
    constructor(options = {}) {
        // Default options
        this.options = {
            mapId: 'map',
            initialLat: 24.7136, // Default to Saudi Arabia
            initialLng: 46.6753,
            initialZoom: 12,
            max<PERSON>oom: 18,
            minZoom: 5,
            ...options
        };

        this.map = null;
        this.markers = new Map(); // Using Map to store markers with store ID as key
        this.infoWindow = null;
        this.deviceDetector = window.deviceDetector || null;

        // Initialize map
        this.initMap();

        console.log('🗺️ Map Manager initialized');
    }

    /**
     * Initialize the map instance
     */
    initMap() {
        try {
            // Get map container
            const mapContainer = document.getElementById(this.options.mapId);
            if (!mapContainer) {
                console.error(`Map container with ID '${this.options.mapId}' not found.`);
                return;
            }

            // Set map height based on device
            this.setMapHeight();

            // Initialize Leaflet map
            this.map = L.map(this.options.mapId, {
                center: [this.options.initialLat, this.options.initialLng],
                zoom: this.options.initialZoom,
                zoomControl: false,
                attributionControl: true
            });

            // Add zoom control to top right
            L.control.zoom({
                position: 'topright'
            }).addTo(this.map);

            // Add tile layer (OpenStreetMap)
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
                maxZoom: this.options.maxZoom,
                minZoom: this.options.minZoom
            }).addTo(this.map);

            // Add click handler for adding new stores
            this.map.on('click', this.handleMapClick.bind(this));

            // Add current location control
            this.addCurrentLocationControl();

            // Try to get user's current location
            this.getUserLocation();

        } catch (error) {
            console.error('Error initializing map:', error);
        }
    }

    /**
     * Set map height based on device type
     */
    setMapHeight() {
        const mapContainer = document.getElementById(this.options.mapId);
        if (!mapContainer) return;

        // Get device type if DeviceDetector is available
        let isMobile = false;
        if (this.deviceDetector) {
            isMobile = this.deviceDetector.isMobile || this.deviceDetector.isTablet;
        } else {
            // Fallback to window width check
            isMobile = window.innerWidth < 768;
        }

        // Set appropriate height
        if (isMobile) {
            mapContainer.style.height = '300px';
        } else {
            mapContainer.style.height = '600px';
        }
    }

    /**
     * Add current location control to map
     */
    addCurrentLocationControl() {
        const locationControl = L.Control.extend({
            options: {
                position: 'topright'
            },

            onAdd: () => {
                const container = L.DomUtil.create('div', 'leaflet-bar leaflet-control leaflet-control-custom');
                container.innerHTML = '<a href="#" title="موقعك الحالي" role="button" aria-label="موقعك الحالي"><i class="fas fa-location-arrow"></i></a>';

                L.DomEvent.on(container, 'click', L.DomEvent.stopPropagation);
                L.DomEvent.on(container, 'click', L.DomEvent.preventDefault);
                L.DomEvent.on(container, 'click', () => {
                    this.getUserLocation();
                });

                return container;
            }
        });

        this.map.addControl(new locationControl());
    }

    /**
     * Get user's current location
     */
    getUserLocation() {
        if ('geolocation' in navigator) {
            navigator.geolocation.getCurrentPosition(
                (position) => {
                    const { latitude, longitude } = position.coords;

                    // Center map on user location
                    this.map.setView([latitude, longitude], 14);

                    // Add user marker
                    const userMarker = L.marker([latitude, longitude], {
                        icon: L.divIcon({
                            className: 'user-location-marker',
                            html: '<div class="user-marker-icon"><i class="fas fa-user-circle"></i></div>',
                            iconSize: [30, 30],
                            iconAnchor: [15, 15]
                        })
                    }).addTo(this.map);

                    userMarker.bindPopup('موقعك الحالي').openPopup();

                    // Add accuracy circle
                    const accuracy = position.coords.accuracy;
                    L.circle([latitude, longitude], {
                        radius: accuracy,
                        fillColor: 'rgba(66, 133, 244, 0.2)',
                        fillOpacity: 0.4,
                        stroke: false
                    }).addTo(this.map);

                    console.log('📍 User location acquired:', latitude, longitude);
                },
                (error) => {
                    console.error('Error getting user location:', error);
                    this.showLocationError(error.message);
                },
                {
                    enableHighAccuracy: true,
                    timeout: 10000,
                    maximumAge: 0
                }
            );
        } else {
            console.error('Geolocation is not supported by this browser.');
            this.showLocationError('خدمة تحديد الموقع غير متوفرة في متصفحك.');
        }
    }

    /**
     * Display location error message
     * @param {string} message - Error message to display
     */
    showLocationError(message) {
        const errorDiv = document.createElement('div');
        errorDiv.classList.add('map-error');
        errorDiv.innerHTML = `
            <div class="alert alert-warning" role="alert">
                <i class="fas fa-exclamation-triangle"></i> ${message}
                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
        `;

        // Add to map container
        const mapContainer = document.getElementById(this.options.mapId);
        if (mapContainer) {
            mapContainer.appendChild(errorDiv);

            // Auto remove after 5 seconds
            setTimeout(() => {
                if (errorDiv.parentNode) {
                    errorDiv.parentNode.removeChild(errorDiv);
                }
            }, 5000);
        }
    }

    /**
     * Handle map click events
     * @param {Object} e - Leaflet click event
     */
    handleMapClick(e) {
        // Check if we're in "add store" mode
        const addStoreMode = document.body.classList.contains('add-store-mode');
        if (!addStoreMode) return;

        const { lat, lng } = e.latlng;

        // Populate store form with coordinates
        this.populateCoordinatesForm(lat, lng);

        // Add a temporary marker
        this.addTemporaryMarker(lat, lng);

        // Show store modal if not already visible
        this.showStoreModal();
    }

    /**
     * Populate coordinates in the store form
     * @param {number} lat - Latitude
     * @param {number} lng - Longitude
     */
    populateCoordinatesForm(lat, lng) {
        const latField = document.querySelector('input[name="latitude"]');
        const lngField = document.querySelector('input[name="longitude"]');

        if (latField) latField.value = lat.toFixed(6);
        if (lngField) lngField.value = lng.toFixed(6);

        // Get address for the coordinates
        this.reverseGeocode(lat, lng);
    }

    /**
     * Add a temporary marker to the map
     * @param {number} lat - Latitude
     * @param {number} lng - Longitude
     */
    addTemporaryMarker(lat, lng) {
        // Remove existing temporary marker if any
        if (this.tempMarker) {
            this.map.removeLayer(this.tempMarker);
        }

        // Create new temporary marker
        this.tempMarker = L.marker([lat, lng], {
            icon: L.divIcon({
                className: 'temp-store-marker',
                html: '<div class="marker-pin new-marker"><i class="fas fa-map-marker-alt" style="color: #d50000;"></i></div>',
                iconSize: [35, 35],
                iconAnchor: [17, 35]
            }),
            draggable: true
        }).addTo(this.map);

        // Update form when marker is dragged
        this.tempMarker.on('dragend', (e) => {
            const marker = e.target;
            const position = marker.getLatLng();
            this.populateCoordinatesForm(position.lat, position.lng);
        });

        // Open popup
        this.tempMarker.bindPopup('اضغط لتأكيد الموقع أو اسحب لتغيير الموقع').openPopup();
    }

    /**
     * Show the store modal for adding a new store
     */
    showStoreModal() {
        const modal = document.getElementById('store-modal');
        if (modal && typeof bootstrap !== 'undefined') {
            const modalTitle = modal.querySelector('.modal-title');
            if (modalTitle) modalTitle.textContent = 'إضافة متجر جديد';

            // Clear form
            const form = document.getElementById('store-form');
            if (form) {
                form.reset();
                form.querySelector('input[name="store_id"]').value = '';
            }

            // Show modal
            const bsModal = new bootstrap.Modal(modal);
            bsModal.show();
        }
    }

    /**
     * Reverse geocode to get address from coordinates
     * @param {number} lat - Latitude
     * @param {number} lng - Longitude
     */
    async reverseGeocode(lat, lng) {
        try {
            const response = await fetch(`https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}&accept-language=ar`);
            const data = await response.json();

            if (data && data.display_name) {
                const addressField = document.querySelector('input[name="address"]');
                if (addressField) {
                    addressField.value = data.display_name;
                }
            }
        } catch (error) {
            console.error('Error reverse geocoding:', error);
        }
    }

    /**
     * Add store markers to the map
     * @param {Array} stores - Array of store objects
     */
    addStoreMarkers(stores) {
        if (!this.map || !stores || !Array.isArray(stores)) return;

        // Clear existing markers
        this.clearMarkers();

        stores.forEach(store => {
            if (!store.latitude || !store.longitude) return;

            const marker = L.marker([store.latitude, store.longitude], {
                icon: L.divIcon({
                    className: 'store-marker',
                    html: `<div class="marker-pin"><i class="fas fa-map-marker-alt" style="color: #d50000;"></i></div>`,
                    iconSize: [35, 35],
                    iconAnchor: [17, 35]
                })
            }).addTo(this.map);

            // Create popup content
            const popupContent = `
                <div class="map-popup">
                    <h5>${store.name}</h5>
                    <p>${store.address}</p>
                    <button class="btn btn-sm btn-primary select-store-btn" data-store-id="${store.id}">
                        عرض التفاصيل
                    </button>
                </div>
            `;

            // Add popup
            marker.bindPopup(popupContent);

            // Add click event
            marker.on('click', () => {
                this.onMarkerClick(store.id);
            });

            // Store marker in map
            this.markers.set(store.id, marker);
        });

        // Fit map to markers bounds if there are markers
        if (stores.length > 0) {
            this.fitMapToMarkers();
        }
    }

    /**
     * Handle marker click
     * @param {string} storeId - ID of the store
     */
    onMarkerClick(storeId) {
        // Find the marker
        const marker = this.markers.get(storeId);
        if (!marker) return;

        // Open popup
        marker.openPopup();

        // Add click event to the select store button
        const popup = marker.getPopup();
        if (popup) {
            popup.on('add', () => {
                const selectBtn = document.querySelector(`.select-store-btn[data-store-id="${storeId}"]`);
                if (selectBtn) {
                    selectBtn.addEventListener('click', () => {
                        // Trigger store selection in StoreManager
                        if (window.storeManager) {
                            window.storeManager.selectStore(storeId);
                        }

                        // Close popup
                        marker.closePopup();
                    });
                }
            });
        }
    }

    /**
     * Focus on a specific store on the map
     * @param {Object} store - Store object
     */
    focusStoreOnMap(store) {
        if (!this.map || !store || !store.latitude || !store.longitude) return;

        // Center map on store
        this.map.setView([store.latitude, store.longitude], 16);

        // Find and open popup for the store
        const marker = this.markers.get(store.id);
        if (marker) {
            marker.openPopup();

            // Highlight marker
            this.highlightMarker(marker);
        }
    }

    /**
     * Highlight a marker with animation
     * @param {Object} marker - Leaflet marker to highlight
     */
    highlightMarker(marker) {
        // Get marker element
        const markerElement = marker.getElement();
        if (!markerElement) return;

        // Add highlight class
        markerElement.classList.add('marker-highlight');

        // Remove class after animation
        setTimeout(() => {
            markerElement.classList.remove('marker-highlight');
        }, 2000);
    }

    /**
     * Clear all markers from the map
     */
    clearMarkers() {
        // Remove all markers from the map
        this.markers.forEach(marker => {
            if (this.map) {
                this.map.removeLayer(marker);
            }
        });

        // Clear markers map
        this.markers.clear();
    }

    /**
     * Filter markers to show only those in the filtered stores list
     * @param {Array} filteredStores - Array of filtered store objects
     */
    filterMarkers(filteredStores) {
        if (!this.map || !filteredStores) return;

        // Get all store IDs from filtered stores
        const filteredIds = new Set(filteredStores.map(store => store.id));

        // Show/hide markers based on filter
        this.markers.forEach((marker, storeId) => {
            if (filteredIds.has(storeId)) {
                // If marker is not on map, add it
                if (!this.map.hasLayer(marker)) {
                    marker.addTo(this.map);
                }
            } else {
                // If marker is on map, remove it
                if (this.map.hasLayer(marker)) {
                    this.map.removeLayer(marker);
                }
            }
        });

        // Fit map to visible markers if there are any
        if (filteredStores.length > 0) {
            this.fitMapToMarkers(filteredStores);
        }
    }

    /**
     * Fit map to show all markers
     * @param {Array} stores - Optional array of stores to fit (defaults to all)
     */
    fitMapToMarkers(stores = null) {
        if (!this.map) return;

        // Create a bounds object
        const bounds = L.latLngBounds();

        if (stores && Array.isArray(stores)) {
            // Add coordinates from provided stores
            stores.forEach(store => {
                if (store.latitude && store.longitude) {
                    bounds.extend([store.latitude, store.longitude]);
                }
            });
        } else {
            // Add all marker positions to bounds
            this.markers.forEach(marker => {
                bounds.extend(marker.getLatLng());
            });
        }

        // If bounds are valid (not empty), fit map to bounds
        if (bounds.isValid()) {
            this.map.fitBounds(bounds, {
                padding: [50, 50],
                maxZoom: 16
            });
        }
    }

    /**
     * Resize the map when window size changes
     */
    handleResize() {
        if (this.map) {
            this.setMapHeight();
            this.map.invalidateSize();
        }
    }

    /**
     * Clean up resources when destroying the instance
     */
    destroy() {
        // Remove event listeners
        if (this.map) {
            this.map.off();
            this.map.remove();
        }

        // Clear markers
        this.clearMarkers();

        console.log('🗺️ Map Manager destroyed');
    }
}

// Initialize the map manager when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM loaded, initializing MapManager');
    const mapManager = new MapManager();
    mapManager.init();

    // Make mapManager available globally
    window.mapManager = mapManager;

    // Connect search location button
    const searchLocationBtn = document.getElementById('searchLocation');
    if (searchLocationBtn) {
        searchLocationBtn.addEventListener('click', () => {
            const searchInput = document.getElementById('locationSearch');
            if (searchInput && searchInput.value) {
                mapManager.searchLocation(searchInput.value.trim());
            } else {
                alert('الرجاء إدخال موقع للبحث عنه');
            }
        });
    }
});

// Add CSS for highlighted markers
const style = document.createElement('style');
style.textContent = `
.highlighted-marker {
    transform: scale(1.3);
    filter: hue-rotate(120deg);
    transition: all 0.3s ease;
    z-index: 1000 !important;
}

.map-popup {
    padding: 5px;
    text-align: center;
}

.map-popup h5 {
    margin: 5px 0;
    font-weight: bold;
}

.map-popup button {
    margin-top: 8px;
}

.search-result-popup {
    text-align: center;
    padding: 5px;
}
`;
document.head.appendChild(style);