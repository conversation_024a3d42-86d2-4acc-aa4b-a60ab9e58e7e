/* تنسيقات شارة الإشعارات */
.notification-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background-color: #d50000;
    color: white;
    border-radius: 50%;
    min-width: 20px;
    height: 20px;
    padding: 0 4px;
    font-size: 11px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    animation: pulse-animation 2s infinite;
    z-index: 100;
    border: 2px solid #212529;
}

.notification-badge-mobile {
    position: absolute;
    top: 2px;
    right: 2px;
    background-color: #d50000;
    color: white;
    border-radius: 50%;
    min-width: 18px;
    height: 18px;
    padding: 0 3px;
    font-size: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    border: 1px solid #333;
    z-index: 100;
}

/* تأثير النبض للجرس */
@keyframes pulse-animation {
    0% {
        box-shadow: 0 0 0 0 rgba(213, 0, 0, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(213, 0, 0, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(213, 0, 0, 0);
    }
}

/* تنسيق زر الجرس */
#notificationBell {
    transition: all 0.3s ease;
    position: relative;
}

#notificationBell:hover {
    transform: scale(1.1);
}

#notificationBell.has-notifications {
    animation: shake 0.5s ease-in-out;
}

/* تأثير الاهتزاز للجرس */
@keyframes shake {
    0% { transform: rotate(0); }
    15% { transform: rotate(15deg); }
    30% { transform: rotate(-15deg); }
    45% { transform: rotate(10deg); }
    60% { transform: rotate(-10deg); }
    75% { transform: rotate(5deg); }
    85% { transform: rotate(-5deg); }
    92% { transform: rotate(2deg); }
    100% { transform: rotate(0); }
}

/* تنسيق قسم المتاجر المعلقة في لوحة التحكم */
#pendingStores {
    scroll-margin-top: 70px;
}
