/**
 * AppInitializer - Coordinates the initialization of various app components
 * Created: 15-04-2024
 */
class AppInitializer {
    constructor() {
        this.deviceDetector = null;
        this.buttonManager = null;
        this.mapManager = null;
        this.storeManager = null;
        this.notificationManager = null;
        this.lastWidth = window.innerWidth;
        this.isInitialized = false;
    }

    /**
     * Initialize all application components
     */
    init() {
        if (this.isInitialized) return;
        console.log("🚀 Initializing application components...");

        // Initialize device detector
        this.initDeviceDetector();
        
        // Initialize notification manager
        this.initNotificationManager();
        
        // Initialize button manager
        this.initButtonManager();
        
        // Initialize map and store manager
        this.initMapManager();
        this.initStoreManager();
        
        // Set up global event listeners
        this.setupGlobalEvents();
        
        // Set up keyboard shortcuts
        this.setupKeyboardShortcuts();
        
        this.isInitialized = true;
        console.log("✅ Application initialized successfully!");
        
        // Show welcome notification
        this.showNotification("تم تهيئة التطبيق بنجاح", "success");
    }

    /**
     * Initialize device detector
     */
    initDeviceDetector() {
        try {
            this.deviceDetector = new DeviceDetector();
            console.log("🔍 Device detector initialized:", this.deviceDetector.isMobile ? "Mobile" : "Desktop");
        } catch (error) {
            console.error("❌ Error initializing device detector:", error);
        }
    }

    /**
     * Initialize notification manager
     */
    initNotificationManager() {
        try {
            // Use existing global notification manager if available
            if (window.notificationManager) {
                this.notificationManager = window.notificationManager;
                console.log("🔔 Using existing notification manager");
            } else {
                // Create new notification manager if needed
                this.notificationManager = new NotificationManager();
                window.notificationManager = this.notificationManager;
                this.notificationManager.init();
                console.log("🔔 Notification manager initialized");
            }
        } catch (error) {
            console.error("❌ Error initializing notification manager:", error);
        }
    }

    /**
     * Initialize button manager
     */
    initButtonManager() {
        try {
            this.buttonManager = new ButtonManager();
            this.buttonManager.init();
            console.log("🔘 Button manager initialized");
        } catch (error) {
            console.error("❌ Error initializing button manager:", error);
        }
    }

    /**
     * Initialize map manager
     */
    initMapManager() {
        try {
            this.mapManager = new MapManager();
            this.mapManager.init();
            console.log("🗺️ Map manager initialized");
        } catch (error) {
            console.error("❌ Error initializing map manager:", error);
        }
    }

    /**
     * Initialize store manager
     */
    initStoreManager() {
        try {
            this.storeManager = new StoreManager();
            this.storeManager.init();
            console.log("🏪 Store manager initialized");
        } catch (error) {
            console.error("❌ Error initializing store manager:", error);
        }
    }

    /**
     * Set up global event listeners
     */
    setupGlobalEvents() {
        // Handle window resize
        window.addEventListener('resize', this.handleResize.bind(this));
        
        // Handle orientation change
        window.addEventListener('orientationchange', this.handleOrientationChange.bind(this));
        
        // Handle online/offline status
        window.addEventListener('online', () => this.showNotification("تم استعادة الاتصال", "success"));
        window.addEventListener('offline', () => this.showNotification("انقطع الاتصال", "error"));
    }

    /**
     * Handle window resize events
     */
    handleResize() {
        const currentWidth = window.innerWidth;
        
        // Only update if width changed significantly (to avoid multiple triggers)
        if (Math.abs(this.lastWidth - currentWidth) > 50) {
            this.lastWidth = currentWidth;
            
            if (this.deviceDetector) {
                this.deviceDetector.checkDeviceType();
                
                // Update UI based on current screen size
                this.updateUI();
                
                console.log(`📱 Screen size changed: ${currentWidth}px - Device: ${
                    this.deviceDetector.isMobile ? "Mobile" : "Desktop"
                }`);
            }
        }
    }

    /**
     * Handle orientation change events
     */
    handleOrientationChange() {
        setTimeout(() => {
            if (this.mapManager) {
                this.mapManager.updateMapSize();
            }
            this.updateUI();
            console.log("🔄 Orientation changed, UI updated");
        }, 300); // Small delay to allow browser to complete rotation
    }

    /**
     * Update UI based on current device type
     */
    updateUI() {
        if (!this.deviceDetector) return;
        
        const body = document.body;
        
        if (this.deviceDetector.isMobile) {
            body.classList.add('mobile-view');
            body.classList.remove('desktop-view');
        } else {
            body.classList.add('desktop-view');
            body.classList.remove('mobile-view');
        }
        
        // Adjust map size if available
        if (this.mapManager) {
            this.mapManager.updateMapSize();
        }
    }

    /**
     * Set up keyboard shortcuts
     */
    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (event) => {
            // Ctrl+F or Command+F to focus search
            if ((event.ctrlKey || event.metaKey) && event.key === 'f') {
                event.preventDefault();
                const searchInput = document.querySelector('.search-input');
                if (searchInput) {
                    searchInput.focus();
                    console.log("🔍 Search input focused via keyboard shortcut");
                }
            }
            
            // Esc to close modals
            if (event.key === 'Escape') {
                const modals = document.querySelectorAll('.modal.show');
                if (modals.length > 0) {
                    event.preventDefault();
                    $(modals[0]).modal('hide');
                    console.log("❌ Modal closed via keyboard shortcut");
                }
            }
            
            // Ctrl+M to show map
            if ((event.ctrlKey || event.metaKey) && event.key === 'm') {
                event.preventDefault();
                const mapTab = document.querySelector('[data-action="show-map"]');
                if (mapTab) {
                    mapTab.click();
                    console.log("🗺️ Map view activated via keyboard shortcut");
                }
            }
            
            // Ctrl+L to show list
            if ((event.ctrlKey || event.metaKey) && event.key === 'l') {
                event.preventDefault();
                const listTab = document.querySelector('[data-action="show-list"]');
                if (listTab) {
                    listTab.click();
                    console.log("📋 List view activated via keyboard shortcut");
                }
            }
        });
    }

    /**
     * Show a notification message
     * @param {string} message - The message to display
     * @param {string} type - The type of notification (success, error, warning, info)
     * @param {number} duration - Duration in milliseconds
     */
    showNotification(message, type = 'info', duration = 3000) {
        if (this.notificationManager) {
            const title = this.getNotificationTitle(type);
            this.notificationManager.show(title, message, type, duration);
        } else {
            console.log(`${type.toUpperCase()}: ${message}`);
        }
    }
    
    /**
     * Get appropriate title for notification based on type
     * @param {string} type - Notification type
     * @returns {string} - Appropriate title
     */
    getNotificationTitle(type) {
        switch (type) {
            case 'success': return 'تم بنجاح';
            case 'error': return 'خطأ';
            case 'warning': return 'تحذير';
            case 'info': return 'معلومات';
            default: return '';
        }
    }
}

// Initialize application when DOM is fully loaded
document.addEventListener('DOMContentLoaded', function() {
    // Create global app initializer
    window.appInitializer = new AppInitializer();
    window.appInitializer.init();
    
    console.log('Application ready!');
}); 