/**
 * إدارة القوائم المخصصة في لوحة التحكم
 */

// تحميل عدد المتاجر لكل قائمة
function loadStoreCounts() {
    const storeCountBadges = document.querySelectorAll('.store-count');

    storeCountBadges.forEach(badge => {
        const listId = badge.getAttribute('data-list-id');

        // إظهار حالة التحميل
        badge.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';

        // استدعاء API للحصول على المتاجر في هذه القائمة
        fetch(`/api/stores?list_id=${listId}`)
            .then(response => response.json())
            .then(data => {
                // تحديث العداد بعدد المتاجر
                if (Array.isArray(data)) {
                    badge.textContent = data.length;
                } else {
                    badge.textContent = '0';
                    console.error('Invalid data format for stores:', data);
                }
            })
            .catch(error => {
                console.error(`Error fetching stores for list ${listId}:`, error);
                badge.textContent = 'خطأ';
            });
    });
}

// متغيرات عامة لنقل المتاجر
let allStores = [];
let allLists = [];
let allMarketers = [];
let filteredStores = [];
let selectedStores = new Set();

// تحميل المتاجر لنقلها بين المسوقين
async function loadStoresForTransfer() {
    try {
        // إظهار حالة التحميل
        const tbody = document.querySelector('#storesTransferTable tbody');
        tbody.innerHTML = '<tr><td colspan="7" class="text-center"><i class="fas fa-spinner fa-spin me-2"></i>جاري التحميل...</td></tr>';

        // تحميل البيانات المطلوبة
        const [storesResponse, listsResponse, marketersResponse] = await Promise.all([
            fetch('/api/stores'),
            fetch('/api/custom-lists'),
            fetch('/api/marketers')
        ]);

        const stores = await storesResponse.json();
        const listsData = await listsResponse.json();
        const marketersData = await marketersResponse.json();

        allStores = Array.isArray(stores) ? stores : [];
        allLists = listsData.lists || [];
        allMarketers = marketersData.marketers || [];
        filteredStores = [...allStores]; // نسخة للفلترة
        selectedStores.clear(); // مسح التحديد السابق

        // تحديث الإحصائيات
        updateStatistics();

        // عرض المتاجر في الجدول
        displayStoresForTransfer();

    } catch (error) {
        console.error('Error loading stores for transfer:', error);
        const tbody = document.querySelector('#storesTransferTable tbody');
        tbody.innerHTML = '<tr><td colspan="7" class="text-center text-danger">حدث خطأ في تحميل البيانات</td></tr>';
    }
}

// تحديث الإحصائيات
function updateStatistics() {
    // تحديث إجمالي المتاجر
    const totalStoresElement = document.getElementById('totalStoresCount');
    if (totalStoresElement) {
        totalStoresElement.textContent = allStores.length;
    }

    // تحديث عدد المسوقين النشطين
    const totalMarketersElement = document.getElementById('totalMarketersCount');
    if (totalMarketersElement) {
        totalMarketersElement.textContent = allMarketers.length;
    }

    // تحديث عدد المتاجر المحددة
    const selectedStoresElement = document.getElementById('selectedStoresCount');
    if (selectedStoresElement) {
        selectedStoresElement.textContent = selectedStores.size;
    }

    // تحديث عدد المتاجر المعروضة
    const filteredStoresElement = document.getElementById('filteredStoresCount');
    if (filteredStoresElement) {
        filteredStoresElement.textContent = filteredStores.length;
    }

    // تحديث حالة زر النقل الجماعي
    const bulkTransferBtn = document.getElementById('bulkTransferBtn');
    if (bulkTransferBtn) {
        bulkTransferBtn.disabled = selectedStores.size === 0;
    }
}

// عرض المتاجر في جدول النقل
async function displayStoresForTransfer() {
    const tbody = document.querySelector('#storesTransferTable tbody');

    if (filteredStores.length === 0) {
        tbody.innerHTML = '<tr><td colspan="8" class="text-center text-muted">لا توجد متاجر</td></tr>';
        updateStatistics();
        return;
    }

    // إنشاء خريطة للمسوقين حسب القائمة
    const listMarketers = {};

    // تحميل المسوقين لكل قائمة
    for (const list of allLists) {
        try {
            const response = await fetch(`/api/list-marketer/${list.id}`);
            const data = await response.json();
            if (data.success && data.marketer) {
                listMarketers[list.id] = data.marketer;
            }
        } catch (error) {
            console.error(`Error loading marketer for list ${list.id}:`, error);
        }
    }

    let html = '';
    filteredStores.forEach((store, index) => {
        // العثور على القائمة الحالية للمتجر
        const currentList = allLists.find(list => list.id === store.list_id);
        const listName = currentList ? currentList.name : 'القائمة الافتراضية';

        // العثور على المسوق الحالي للمتجر
        let currentMarketer = 'غير محدد';
        let marketerDisplay = 'غير محدد';

        if (currentList && listMarketers[currentList.id]) {
            const marketer = listMarketers[currentList.id];
            currentMarketer = marketer.username;
            marketerDisplay = `${marketer.username} (${marketer.phone})`;
        } else if (currentList) {
            marketerDisplay = `قائمة: ${listName}`;
            currentMarketer = listName;
        }

        // تنسيق المدينة والمنطقة
        let cityRegion = 'غير محدد';
        if (store.city_name && store.region_name) {
            cityRegion = `${store.city_name} - ${store.region_name}`;
        } else if (store.city_name) {
            cityRegion = store.city_name;
        } else if (store.address) {
            // استخراج المدينة والمنطقة من العنوان إذا كان متوفراً
            const addressParts = store.address.split(' - ');
            if (addressParts.length >= 2) {
                cityRegion = `${addressParts[0]} - ${addressParts[1]}`;
            } else {
                cityRegion = store.address;
            }
        }

        const isSelected = selectedStores.has(store.id);

        html += `
            <tr class="${isSelected ? 'table-info' : ''}">
                <td>
                    <input type="checkbox" class="form-check-input store-checkbox"
                           value="${store.id}" ${isSelected ? 'checked' : ''}
                           onchange="toggleStoreSelection('${store.id}')">
                </td>
                <td>${index + 1}</td>
                <td><strong>${store.name}</strong></td>
                <td>${store.phone || 'غير محدد'}</td>
                <td>${marketerDisplay}</td>
                <td><span class="badge bg-secondary">${listName}</span></td>
                <td>${cityRegion}</td>
                <td>
                    <button type="button" class="btn btn-sm btn-primary"
                            onclick="openTransferModal('${store.id}', '${store.name}', '${marketerDisplay}')">
                        <i class="fas fa-exchange-alt"></i> نقل
                    </button>
                </td>
            </tr>
        `;
    });

    tbody.innerHTML = html;

    // تحديث الإحصائيات
    updateStatistics();

    // تحديث حالة checkbox "تحديد الكل"
    updateSelectAllCheckbox();
}

// تحديث حالة checkbox "تحديد الكل"
function updateSelectAllCheckbox() {
    const selectAllCheckbox = document.getElementById('selectAllCheckbox');
    if (selectAllCheckbox && filteredStores.length > 0) {
        const visibleStoreIds = filteredStores.map(store => store.id);
        const selectedVisibleStores = visibleStoreIds.filter(id => selectedStores.has(id));

        if (selectedVisibleStores.length === 0) {
            selectAllCheckbox.checked = false;
            selectAllCheckbox.indeterminate = false;
        } else if (selectedVisibleStores.length === visibleStoreIds.length) {
            selectAllCheckbox.checked = true;
            selectAllCheckbox.indeterminate = false;
        } else {
            selectAllCheckbox.checked = false;
            selectAllCheckbox.indeterminate = true;
        }
    }
}

// تبديل تحديد متجر
function toggleStoreSelection(storeId) {
    if (selectedStores.has(storeId)) {
        selectedStores.delete(storeId);
    } else {
        selectedStores.add(storeId);
    }

    // إعادة عرض الجدول لتحديث التمييز
    displayStoresForTransfer();
}

// تحديد/إلغاء تحديد جميع المتاجر المعروضة
function toggleSelectAll() {
    const visibleStoreIds = filteredStores.map(store => store.id);
    const selectedVisibleStores = visibleStoreIds.filter(id => selectedStores.has(id));

    if (selectedVisibleStores.length === visibleStoreIds.length) {
        // إلغاء تحديد جميع المتاجر المعروضة
        visibleStoreIds.forEach(id => selectedStores.delete(id));
    } else {
        // تحديد جميع المتاجر المعروضة
        visibleStoreIds.forEach(id => selectedStores.add(id));
    }

    displayStoresForTransfer();
}

// البحث في المتاجر
function searchStores(searchTerm) {
    if (!searchTerm || searchTerm.trim() === '') {
        filteredStores = [...allStores];
    } else {
        const term = searchTerm.toLowerCase().trim();
        filteredStores = allStores.filter(store => {
            return (
                store.name.toLowerCase().includes(term) ||
                (store.phone && store.phone.includes(term)) ||
                (store.city_name && store.city_name.toLowerCase().includes(term)) ||
                (store.region_name && store.region_name.toLowerCase().includes(term)) ||
                (store.address && store.address.toLowerCase().includes(term))
            );
        });
    }

    displayStoresForTransfer();
}

// فتح modal نقل المتجر
function openTransferModal(storeId, storeName, currentMarketer) {
    try {
        // إغلاق أي modal مفتوح حالياً
        const openModals = document.querySelectorAll('.modal.show');
        openModals.forEach(modal => {
            const modalInstance = bootstrap.Modal.getInstance(modal);
            if (modalInstance) {
                modalInstance.hide();
            }
        });

        // انتظار قصير للتأكد من إغلاق النوافذ السابقة
        setTimeout(() => {
            // تعبئة بيانات المتجر
            document.getElementById('transfer_store_id').value = storeId;
            document.getElementById('transfer_store_name').textContent = storeName;
            document.getElementById('transfer_current_marketer').textContent = currentMarketer;

            // تحميل قائمة المسوقين
            const marketerSelect = document.getElementById('transfer_target_marketer');
            marketerSelect.innerHTML = '<option value="">اختر مسوق...</option>';

            if (allMarketers.length === 0) {
                marketerSelect.innerHTML += '<option value="" disabled>لا توجد مسوقين متاحين</option>';
            } else {
                allMarketers.forEach(marketer => {
                    marketerSelect.innerHTML += `<option value="${marketer.id}">${marketer.username} (${marketer.phone})</option>`;
                });
            }

            // إظهار الـ modal
            const transferModal = document.getElementById('transferStoreModal');
            const modal = new bootstrap.Modal(transferModal, {
                backdrop: 'static',
                keyboard: false,
                focus: false
            });
            modal.show();
        }, 300);

    } catch (error) {
        console.error('Error opening transfer modal:', error);
        alert('حدث خطأ في فتح نافذة النقل');
    }
}

// تنفيذ نقل المتجر
async function transferStore() {
    const storeId = document.getElementById('transfer_store_id').value;
    let targetMarketerId = document.getElementById('transfer_target_marketer').value;

    if (!targetMarketerId) {
        alert('يرجى اختيار المسوق الجديد');
        return;
    }

    // التأكد من أن معرف المسوق صحيح

    const transferBtn = document.getElementById('transferStoreBtn');
    const originalText = transferBtn.textContent;

    try {
        // إظهار حالة التحميل
        transferBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري النقل...';
        transferBtn.disabled = true;

        console.log('Sending transfer request:', { store_id: storeId, target_marketer_id: targetMarketerId });

        const response = await fetch('/api/transfer-store', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                store_id: storeId,
                target_marketer_id: targetMarketerId
            })
        });

        if (!response.ok) {
            // محاولة قراءة رسالة الخطأ من الاستجابة
            try {
                const errorData = await response.json();
                throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
            } catch (parseError) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
        }

        const result = await response.json();

        if (result.success) {
            // إظهار رسالة نجاح
            showAlert('تم نقل المتجر بنجاح', 'success');

            // إغلاق النافذة المنبثقة بشكل صحيح
            forceCloseModal('transferStoreModal');

            // إعادة تحميل قائمة المتاجر
            setTimeout(() => {
                loadStoresForTransfer();
            }, 1000);
        } else {
            showAlert(result.error || 'حدث خطأ أثناء نقل المتجر', 'danger');
        }

    } catch (error) {
        console.error('Error transferring store:', error);
        showAlert(`حدث خطأ أثناء نقل المتجر: ${error.message}`, 'danger');

        // إغلاق النافذة في حالة الخطأ أيضاً
        forceCloseModal('transferStoreModal');

    } finally {
        // إعادة تعيين الزر
        transferBtn.textContent = originalText;
        transferBtn.disabled = false;
    }
}

// فتح modal النقل الجماعي
function openBulkTransferModal() {
    if (selectedStores.size === 0) {
        alert('يرجى تحديد متجر واحد على الأقل للنقل');
        return;
    }

    // تحديث عدد المتاجر المحددة
    document.getElementById('bulkTransferCount').textContent = selectedStores.size;

    // تحميل قائمة المسوقين
    const marketerSelect = document.getElementById('bulk_target_marketer');
    marketerSelect.innerHTML = '<option value="">اختر مسوق...</option>';

    if (allMarketers.length === 0) {
        marketerSelect.innerHTML += '<option value="" disabled>لا توجد مسوقين متاحين</option>';
    } else {
        allMarketers.forEach(marketer => {
            marketerSelect.innerHTML += `<option value="${marketer.id}">${marketer.username} (${marketer.phone})</option>`;
        });
    }

    // عرض قائمة المتاجر المحددة
    const selectedStoresList = document.getElementById('selectedStoresList');
    let storesListHtml = '';

    selectedStores.forEach(storeId => {
        const store = allStores.find(s => s.id === storeId);
        if (store) {
            storesListHtml += `
                <div class="d-flex justify-content-between align-items-center mb-2 p-3 border rounded shadow-sm"
                     style="background-color: #1c2333; border-color: #495057; min-height: 60px;">
                    <div class="flex-grow-1">
                        <div class="fw-bold mb-1" style="color: #f5f9fc; font-size: 15px; text-shadow: none;">
                            <i class="fas fa-store me-2" style="color: #f5f9fc;"></i>${store.name}
                        </div>
                        <div class="d-flex flex-wrap gap-2">
                            <small class="badge" style="background-color: #495057; color: #f5f9fc; font-size: 11px;">
                                <i class="fas fa-phone me-1"></i>${store.phone || 'غير محدد'}
                            </small>
                            ${store.city_name ? `
                                <small class="badge" style="background-color: #0d6efd; color: #f5f9fc; font-size: 11px;">
                                    <i class="fas fa-map-marker-alt me-1"></i>${store.city_name}${store.region_name ? ' - ' + store.region_name : ''}
                                </small>
                            ` : ''}
                        </div>
                    </div>
                    <button type="button" class="btn btn-sm ms-3"
                            onclick="removeFromBulkSelection('${store.id}')"
                            title="إزالة من التحديد"
                            style="min-width: 35px; background-color: #dc3545; border-color: #dc3545; color: #f5f9fc;">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;
        }
    });

    selectedStoresList.innerHTML = storesListHtml || `
        <div class="text-center py-4" style="background-color: #1c2333; border-radius: 8px;">
            <i class="fas fa-store fa-3x mb-3" style="color: #f5f9fc; opacity: 0.7;"></i>
            <p class="mb-0" style="color: #f5f9fc;">لا توجد متاجر محددة للنقل</p>
            <small style="color: #f5f9fc; opacity: 0.8;">يرجى تحديد المتاجر من الجدول أولاً</small>
        </div>
    `;

    // إظهار النافذة المنبثقة
    const bulkTransferModal = new bootstrap.Modal(document.getElementById('bulkTransferModal'));
    bulkTransferModal.show();
}

// إزالة متجر من التحديد الجماعي
function removeFromBulkSelection(storeId) {
    selectedStores.delete(storeId);
    displayStoresForTransfer();

    // تحديث النافذة المنبثقة إذا كانت مفتوحة
    const modal = document.getElementById('bulkTransferModal');
    if (modal.classList.contains('show')) {
        openBulkTransferModal();
    }
}

// تنفيذ النقل الجماعي
async function performBulkTransfer() {
    const targetMarketerId = document.getElementById('bulk_target_marketer').value;

    if (!targetMarketerId) {
        alert('يرجى اختيار المسوق الجديد');
        return;
    }

    if (selectedStores.size === 0) {
        alert('لا توجد متاجر محددة للنقل');
        return;
    }

    const confirmBtn = document.getElementById('confirmBulkTransferBtn');
    const originalText = confirmBtn.innerHTML;

    try {
        // إظهار حالة التحميل
        confirmBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري النقل...';
        confirmBtn.disabled = true;

        const storeIds = Array.from(selectedStores);

        const response = await fetch('/api/bulk-transfer-stores', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                store_ids: storeIds,
                target_marketer_id: targetMarketerId
            })
        });

        if (!response.ok) {
            try {
                const errorData = await response.json();
                throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
            } catch (parseError) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
        }

        const result = await response.json();

        if (result.success) {
            // إظهار رسالة نجاح
            showAlert(result.message, 'success');

            // إغلاق النافذة المنبثقة
            forceCloseModal('bulkTransferModal');

            // مسح التحديد
            selectedStores.clear();

            // إعادة تحميل قائمة المتاجر
            setTimeout(() => {
                loadStoresForTransfer();
            }, 1000);
        } else {
            showAlert(result.error || 'حدث خطأ أثناء النقل الجماعي', 'danger');
        }

    } catch (error) {
        console.error('Error in bulk transfer:', error);
        showAlert(`حدث خطأ أثناء النقل الجماعي: ${error.message}`, 'danger');

        // إغلاق النافذة في حالة الخطأ
        forceCloseModal('bulkTransferModal');

    } finally {
        // إعادة تعيين الزر
        confirmBtn.innerHTML = originalText;
        confirmBtn.disabled = false;
    }
}

// دالة لإغلاق النوافذ المنبثقة بشكل صحيح
function forceCloseModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        const modalInstance = bootstrap.Modal.getInstance(modal);
        if (modalInstance) {
            modalInstance.hide();
        }

        // إزالة جميع backdrops
        setTimeout(() => {
            const backdrops = document.querySelectorAll('.modal-backdrop');
            backdrops.forEach(backdrop => backdrop.remove());

            // تنظيف body
            document.body.classList.remove('modal-open');
            document.body.style.overflow = '';
            document.body.style.paddingRight = '';

            // إخفاء النافذة يدوياً إذا لم تختف
            modal.style.display = 'none';
            modal.classList.remove('show');
            modal.setAttribute('aria-hidden', 'true');
            modal.removeAttribute('aria-modal');
        }, 300);
    }
}

// دالة لإظهار التنبيهات
function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;

    // إضافة التنبيه في أعلى الصفحة
    const container = document.querySelector('.container');
    container.insertBefore(alertDiv, container.firstChild);

    // إزالة التنبيه تلقائياً بعد 5 ثوان
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// تحميل القوائم عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تحميل عدد المتاجر لكل قائمة
    loadStoreCounts();

    // ربط حدث النقل بالزر
    const transferBtn = document.getElementById('transferStoreBtn');
    if (transferBtn) {
        transferBtn.addEventListener('click', transferStore);
    }

    // ربط أحداث البحث والتحديد
    const searchInput = document.getElementById('storeSearchInput');
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            searchStores(this.value);
        });
    }

    const clearSearchBtn = document.getElementById('clearSearchBtn');
    if (clearSearchBtn) {
        clearSearchBtn.addEventListener('click', function() {
            searchInput.value = '';
            searchStores('');
        });
    }

    const selectAllCheckbox = document.getElementById('selectAllCheckbox');
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', toggleSelectAll);
    }

    const selectAllBtn = document.getElementById('selectAllStoresBtn');
    if (selectAllBtn) {
        selectAllBtn.addEventListener('click', function() {
            filteredStores.forEach(store => selectedStores.add(store.id));
            displayStoresForTransfer();
        });
    }

    const deselectAllBtn = document.getElementById('deselectAllStoresBtn');
    if (deselectAllBtn) {
        deselectAllBtn.addEventListener('click', function() {
            selectedStores.clear();
            displayStoresForTransfer();
        });
    }

    const bulkTransferBtn = document.getElementById('bulkTransferBtn');
    if (bulkTransferBtn) {
        bulkTransferBtn.addEventListener('click', openBulkTransferModal);
    }

    const confirmBulkTransferBtn = document.getElementById('confirmBulkTransferBtn');
    if (confirmBulkTransferBtn) {
        confirmBulkTransferBtn.addEventListener('click', performBulkTransfer);
    }

    // تحميل المتاجر عند فتح modal النقل
    const storesTransferModal = document.getElementById('storesTransferModal');
    if (storesTransferModal) {
        storesTransferModal.addEventListener('show.bs.modal', function() {
            loadStoresForTransfer();
        });

        // إضافة معالج لإغلاق النافذة بشكل صحيح
        storesTransferModal.addEventListener('hidden.bs.modal', function() {
            // تنظيف أي نوافذ فرعية مفتوحة
            const transferModal = document.getElementById('transferStoreModal');
            const transferModalInstance = bootstrap.Modal.getInstance(transferModal);
            if (transferModalInstance) {
                transferModalInstance.hide();
            }
        });
    }

    // معالج لنافذة النقل
    const transferStoreModal = document.getElementById('transferStoreModal');
    if (transferStoreModal) {
        transferStoreModal.addEventListener('hidden.bs.modal', function() {
            // إعادة تعيين النموذج
            document.getElementById('transfer_target_marketer').value = '';
            document.getElementById('transfer_store_id').value = '';
            document.getElementById('transfer_store_name').textContent = '';
            document.getElementById('transfer_current_marketer').textContent = '';
        });
    }
});
