/* RTL Support */
[dir="rtl"] .notification,
html[dir="rtl"] .notification,
.notification {
    direction: rtl;
    text-align: right;
} 

/* Notification Test Panel */
.notification-test-panel {
    position: fixed;
    bottom: 20px;
    left: 20px;
    z-index: 9990;
    display: flex;
    flex-direction: column;
}

.notification-test-toggle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--notification-info);
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    box-shadow: var(--notification-shadow);
    color: white;
    margin-bottom: 10px;
    opacity: 0.8;
    transition: all 0.3s ease;
}

.notification-test-toggle:hover {
    opacity: 1;
    transform: scale(1.05);
}

.notification-test-content {
    background: #222;
    border-radius: 10px;
    padding: 15px;
    box-shadow: var(--notification-shadow);
    display: none;
    width: 250px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.notification-test-panel:hover .notification-test-content {
    display: block;
    animation: fadeIn 0.3s ease;
}

.notification-test-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 10px;
}

.notification-test-buttons button {
    flex: 1 0 calc(50% - 8px);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
}

.notification-test-footer {
    margin-top: 10px;
    text-align: center;
    font-size: 12px;
    color: #aaa;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Mobile adjustments for notification test panel */
@media (max-width: 576px) {
    .notification-test-panel {
        bottom: 10px;
        left: 10px;
    }
    
    .notification-test-content {
        width: 220px;
    }
}
