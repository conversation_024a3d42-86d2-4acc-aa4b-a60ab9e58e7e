// Cache for API responses
const apiCache = {
    stores: new Map(),
    lists: new Map(),
    lastUpdated: new Map(),

    get: function(key) {
        const data = this[key].get('data');
        const timestamp = this.lastUpdated.get(key);
        if (!data || !timestamp) return null;

        const now = Date.now();
        if (now - timestamp > 5 * 60 * 1000) {
            this[key].delete('data');
            this.lastUpdated.delete(key);
            return null;
        }

        return data;
    },

    set: function(key, data) {
        this[key].set('data', data);
        this.lastUpdated.set(key, Date.now());
    }
};

// Device Detection
function isMobileDevice() {
    const userAgent = navigator.userAgent || navigator.vendor || window.opera;
    const mobileKeywords = [
        'Android', 'webOS', 'iPhone', 'iPad', 'iPod', 'BlackBerry',
        'IEMobile', 'Opera Mini', 'Mobile', 'mobile', 'Tablet', 'tablet'
    ];
    const isMobile = mobileKeywords.some(keyword => userAgent.indexOf(keyword) !== -1);
    const isSmallScreen = window.innerWidth < 768;
    return isMobile || isSmallScreen;
}

// Map Management
class StoreMap {
    constructor(containerId) {
        this.containerId = containerId;
        this.map = null;
        this.markers = new Map();
        this.selectedMarker = null;
    }

    init() {
        if (isMobileDevice()) {
            this.initMobileMap(this.containerId);
        } else {
            this.initDesktopMap(this.containerId);
        }
    }

    // ... باقي كود الخريطة ...
}

// Store Management
class StoreManager {
    constructor(map) {
        this.map = map;
        this.stores = [];
        this.selectedStores = new Set();
    }

    async loadStores() {
        const cachedStores = apiCache.get('stores');
        if (cachedStores) {
            this.stores = cachedStores;
            return;
        }

        try {
            const response = await fetch('/api/mobile/stores');
            const data = await response.json();
            this.stores = data;
            apiCache.set('stores', data);
        } catch (error) {
            console.error('Error loading stores:', error);
        }
    }

    // ... باقي كود إدارة المتاجر ...
}

// مدير الأحداث - للتعامل مع إدارة الأحداث بشكل مركزي
class EventManager {
    constructor() {
        this.eventHandlers = {};
    }
    
    // إضافة معالج حدث
    addHandler(eventType, element, handler) {
        if (!this.eventHandlers[eventType]) {
            this.eventHandlers[eventType] = [];
        }
        
        element.addEventListener(eventType, handler);
        this.eventHandlers[eventType].push({ element, handler });
        
        return this;
    }
    
    // إزالة جميع معالجات الأحداث
    removeAllHandlers() {
        for (const eventType in this.eventHandlers) {
            for (const { element, handler } of this.eventHandlers[eventType]) {
                element.removeEventListener(eventType, handler);
            }
        }
        
        this.eventHandlers = {};
        return this;
    }
}

// مدير الأزرار - للتعامل مع تهيئة وإدارة أحداث الأزرار
class ButtonManager {
    constructor() {
        this.initialized = false;
        this.storeButtons = [];
        this.navButtons = [];
        this.formButtons = [];
        this.listSelectorButtons = [];
    }

    init() {
        if (this.initialized) return;
        
        console.log('Initializing ButtonManager');
        
        // Store form buttons
        this.formButtons = [
            document.getElementById('submitStore'),
            document.getElementById('viewStoreList'),
            document.getElementById('editStore'),
            document.getElementById('clearForm'),
            document.getElementById('searchLocation')
        ].filter(btn => btn !== null);

        // Navigation buttons
        this.navButtons = [
            document.getElementById('map-tab'),
            document.getElementById('list-tab-direct'),
            document.getElementById('list-selector'),
            document.getElementById('view-all-lists'),
            document.getElementById('searchButton'),
            document.getElementById('clearSearch'),
            document.getElementById('cancelSelection')
        ].filter(btn => btn !== null);

        // List selector dropdowns
        this.initListSelectors();
        
        // Add event listeners to all buttons
        this.addButtonListeners();
        
        this.initialized = true;
        console.log('ButtonManager initialized with:', {
            formButtons: this.formButtons.length,
            navButtons: this.navButtons.length,
            listSelectorButtons: this.listSelectorButtons.length
        });
    }

    initListSelectors() {
        // Primary list selector in the tabs
        const listSelectorMenu = document.getElementById('list-selector-menu');
        if (listSelectorMenu) {
            const buttons = listSelectorMenu.querySelectorAll('button[data-list-id]');
            this.listSelectorButtons = [...buttons];
            console.log(`Found ${buttons.length} list selector buttons`);
        }

        // Quick list menu in store list view
        const quickListMenu = document.getElementById('quick-list-menu');
        if (quickListMenu) {
            const quickButtons = quickListMenu.querySelectorAll('button[data-list-id]');
            this.listSelectorButtons = [...this.listSelectorButtons, ...quickButtons];
        }
    }

    addButtonListeners() {
        // Add click listeners to all buttons
        [...this.formButtons, ...this.navButtons].forEach(btn => {
            if (btn) {
                this.addButtonEffects(btn);
                btn.addEventListener('click', (e) => this.handleButtonClick(e));
            }
        });

        // Add list selector button listeners
        this.listSelectorButtons.forEach(btn => {
            if (btn) {
                this.addButtonEffects(btn);
                btn.addEventListener('click', (e) => this.handleListSelectorClick(e));
            }
        });

        // Listen for dynamically added store buttons
        document.addEventListener('storeButtonsUpdated', () => {
            this.refreshStoreButtons();
        });

        console.log('Button listeners added');
    }

    refreshStoreButtons() {
        const storeList = document.getElementById('storeList');
        if (storeList) {
            const storeButtons = storeList.querySelectorAll('.store-item');
            storeButtons.forEach(btn => {
                // Only add listeners if they don't already have them
                if (!btn.hasAttribute('data-has-listeners')) {
                    this.addButtonEffects(btn);
                    btn.addEventListener('click', (e) => this.handleStoreButtonClick(e));
                    btn.setAttribute('data-has-listeners', 'true');
                }
            });
            console.log(`Refreshed ${storeButtons.length} store buttons`);
        }
    }

    addButtonEffects(button) {
        // Add scaling effect on press
        button.addEventListener('mousedown', () => {
            button.style.transform = 'scale(0.97)';
        });
        
        button.addEventListener('mouseup', () => {
            button.style.transform = 'scale(1)';
        });
        
        button.addEventListener('mouseleave', () => {
            button.style.transform = 'scale(1)';
        });

        // Add touch events for mobile
        button.addEventListener('touchstart', () => {
            button.style.transform = 'scale(0.97)';
        }, { passive: true });
        
        button.addEventListener('touchend', () => {
            button.style.transform = 'scale(1)';
        }, { passive: true });
    }

    handleButtonClick(event) {
        const button = event.currentTarget;
        console.log('Button clicked:', button.id || button.textContent.trim());
        
        // Handle specific button actions
        if (button.id === 'cancelSelection') {
            this.handleCancelSelection();
        } else if (button.id === 'viewStoreList') {
            // Ensure the list tab is selected
            const listTab = document.getElementById('list-tab-direct');
            if (listTab) {
                listTab.click();
            }
        }

        // Log action for buttons with data-action attribute
        const action = button.getAttribute('data-action');
        if (action) {
            console.log('Action triggered:', action);
        }
    }

    handleListSelectorClick(event) {
        const button = event.currentTarget;
        const listId = button.getAttribute('data-list-id');
        console.log('List selected:', listId);
        
        // Update UI to reflect selected list
        document.getElementById('list-selector').textContent = button.textContent;
        
        // Dispatch custom event to filter stores by list
        const filterEvent = new CustomEvent('filterStoresByList', {
            detail: { listId: listId }
        });
        document.dispatchEvent(filterEvent);
        
        // Switch to list tab if not already there
        const listTab = document.getElementById('list-tab-direct');
        if (listTab) {
            listTab.click();
        }
    }

    handleStoreButtonClick(event) {
        const storeItem = event.currentTarget;
        const storeId = storeItem.getAttribute('data-store-id');
        
        console.log('Store selected:', storeId);
        
        // Remove selection from all other store items
        document.querySelectorAll('.store-item.selected').forEach(item => {
            if (item !== storeItem) {
                item.classList.remove('selected');
            }
        });
        
        // Toggle selection on the clicked item
        storeItem.classList.toggle('selected');
        
        // Show cancel selection button if a store is selected
        const cancelButton = document.getElementById('cancelSelection');
        if (cancelButton) {
            cancelButton.classList.toggle('d-none', !storeItem.classList.contains('selected'));
        }
        
        // Dispatch custom event for store selection
        const selectEvent = new CustomEvent('storeSelected', {
            detail: { 
                storeId: storeId,
                selected: storeItem.classList.contains('selected')
            }
        });
        document.dispatchEvent(selectEvent);
    }

    handleCancelSelection() {
        // Remove selection from all store items
        document.querySelectorAll('.store-item.selected').forEach(item => {
            item.classList.remove('selected');
        });
        
        // Hide the cancel button
        const cancelButton = document.getElementById('cancelSelection');
        if (cancelButton) {
            cancelButton.classList.add('d-none');
        }
        
        // Dispatch custom event for store deselection
        const deselectEvent = new CustomEvent('storeDeselected');
        document.dispatchEvent(deselectEvent);
        
        console.log('Store selection canceled');
    }
}

// Initialize the button manager when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM loaded, initializing ButtonManager');
    const buttonManager = new ButtonManager();
    buttonManager.init();
    
    // Make buttonManager available globally
    window.buttonManager = buttonManager;
});

/**
 * مدير تأثيرات الأزرار
 * يضيف تأثيرات تفاعلية للأزرار مثل تأثير الموجة عند النقر
 */
class ButtonEffects {
    constructor() {
        this.init();
    }

    init() {
        this.addRippleEffect();
        this.enhanceHoverEffects();
    }

    /**
     * إضافة تأثير موجة الماء (ripple) عند النقر على الأزرار
     */
    addRippleEffect() {
        const buttons = document.querySelectorAll('.btn');
        
        buttons.forEach(button => {
            button.addEventListener('click', function(e) {
                const x = e.clientX - this.getBoundingClientRect().left;
                const y = e.clientY - this.getBoundingClientRect().top;
                
                const ripple = document.createElement('span');
                ripple.classList.add('ripple-effect');
                ripple.style.left = `${x}px`;
                ripple.style.top = `${y}px`;
                
                this.appendChild(ripple);
                
                setTimeout(() => {
                    ripple.remove();
                }, 700);
            });
        });

        // إضافة نمط CSS للموجة
        if (!document.getElementById('ripple-style')) {
            const style = document.createElement('style');
            style.id = 'ripple-style';
            style.textContent = `
                .btn {
                    position: relative;
                    overflow: hidden;
                }
                .ripple-effect {
                    position: absolute;
                    background: rgba(255, 255, 255, 0.4);
                    border-radius: 50%;
                    pointer-events: none;
                    width: 10px;
                    height: 10px;
                    transform: scale(0);
                    animation: ripple 0.7s ease-out;
                }
                @keyframes ripple {
                    to {
                        transform: scale(20);
                        opacity: 0;
                    }
                }
            `;
            document.head.appendChild(style);
        }
    }

    /**
     * تحسين تأثيرات التحويم على الأزرار
     */
    enhanceHoverEffects() {
        const buttons = document.querySelectorAll('.btn');
        
        buttons.forEach(button => {
            // متابعة حركة المؤشر على الزر
            button.addEventListener('mousemove', function(e) {
                const rect = this.getBoundingClientRect();
                const x = e.clientX - rect.left; // x position within the element
                const y = e.clientY - rect.top;  // y position within the element
                
                // حساب الموضع النسبي (0-1)
                const xPercent = x / rect.width;
                const yPercent = y / rect.height;
                
                // تطبيق تأثير ظل متحرك
                this.style.boxShadow = `0 5px 15px rgba(0,0,0,0.2), 
                                      ${(xPercent - 0.5) * 5}px ${(yPercent - 0.5) * 5}px 5px rgba(0,0,0,0.1)`;
                
                // تأثير خفيف للإضاءة
                this.style.background = this.style.background.includes('gradient') ? 
                    this.style.background : 
                    `radial-gradient(circle at ${x}px ${y}px, rgba(255,255,255,0.2), rgba(255,255,255,0) 60%)`;
            });
            
            // إعادة تعيين التأثيرات عند مغادرة الزر
            button.addEventListener('mouseleave', function() {
                this.style.boxShadow = '';
                // الحفاظ على الخلفية الأصلية
                if (!this.style.background.includes('gradient')) {
                    this.style.background = '';
                }
            });
        });
    }
}

// تهيئة تأثيرات الأزرار عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // ... existing initialization code ...
    
    // تهيئة مدير تأثيرات الأزرار
    new ButtonEffects();
    
    console.log('تم تحميل تأثيرات الأزرار بنجاح');
});

// ... باقي الكود ... 