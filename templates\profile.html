<!DOCTYPE html>
<html lang="ar" data-bs-theme="dark" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="theme-color" content="#212529">
    <title>الملف الشخصي - Loacker</title>

    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://cdn.replit.com/agent/bootstrap-agent-dark-theme.min.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">

    <!-- Google Fonts - Tajawal & Playfair Display (Classic European Font) -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&family=Playfair+Display:wght@400;700;900&display=swap">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">

    <style>
        .profile-header {
            background: linear-gradient(135deg, #2a2a2a 0%, #1a1a1a 100%);
            padding: 20px 0;
            margin-bottom: 30px;
            border-bottom: 1px solid #333;
        }

        .profile-avatar {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 40px;
            color: white;
            margin: 0 auto 20px;
        }

        .text-gradient {
            background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            font-family: 'Playfair Display', serif;
        }

        .card {
            background-color: #2a2a2a;
            border: 1px solid #333;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .card-header {
            background-color: #333;
            border-bottom: 1px solid #444;
            font-weight: bold;
        }

        .badge-role {
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 0.8rem;
        }

        .badge-admin {
            background-color: #dc3545;
        }

        .badge-marketer {
            background-color: #fd7e14;
        }

        .badge-visitor {
            background-color: #6c757d;
        }
    </style>
</head>
<body>
    <header class="profile-header">
        <div class="container">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <h1 class="h4 mb-0">
                        <span class="text-gradient">Loacker</span>
                        <span class="text-muted ms-2">الملف الشخصي</span>
                    </h1>
                </div>
                <div>
                    <a href="{{ url_for('index') }}" class="btn btn-outline-light me-2">
                        <i class="fas fa-home"></i> الرئيسية
                    </a>
                    <a href="{{ url_for('logout') }}" class="btn btn-outline-danger">
                        <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
                    </a>
                </div>
            </div>
        </div>
    </header>

    <div class="container">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <div class="row">
            <div class="col-md-4 mb-4">
                <div class="card">
                    <div class="card-body text-center">
                        <div class="profile-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                        <h3 class="mb-2">{{ user.username }}</h3>
                        <p class="text-muted mb-2">
                            <a href="https://wa.me/{{ user.phone }}" target="_blank" class="text-decoration-none">
                                <i class="fab fa-whatsapp text-success me-1"></i> {{ user.phone }}
                            </a>
                        </p>

                        {% if user.role_id == 1 %}
                            <span class="badge badge-role badge-admin">مدير</span>
                        {% elif user.role_id == 2 %}
                            <span class="badge badge-role badge-marketer">مسوّق</span>
                        {% else %}
                            <span class="badge badge-role badge-visitor">زائر</span>
                        {% endif %}

                        <div class="mt-3">
                            <button class="btn btn-outline-primary btn-sm" data-bs-toggle="modal" data-bs-target="#editProfileModal">
                                <i class="fas fa-edit"></i> تعديل الملف الشخصي
                            </button>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-shield-alt me-2"></i> الأمان
                    </div>
                    <div class="card-body">
                        <button class="btn btn-outline-warning btn-sm w-100" data-bs-toggle="modal" data-bs-target="#changePasswordModal">
                            <i class="fas fa-key me-2"></i> تغيير كلمة المرور
                        </button>
                    </div>
                </div>

                {% if user.role_id == 1 %}
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-cog me-2"></i> الإدارة
                    </div>
                    <div class="card-body">
                        <a href="{{ url_for('admin_panel') }}" class="btn btn-outline-danger btn-sm w-100">
                            <i class="fas fa-users-cog me-2"></i> لوحة التحكم
                        </a>
                    </div>
                </div>
                {% endif %}
            </div>

            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-info-circle me-2"></i> معلومات الحساب
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-4 fw-bold">اسم المستخدم:</div>
                            <div class="col-md-8">{{ user.username }}</div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-4 fw-bold">رقم الهاتف:</div>
                            <div class="col-md-8">
                                <a href="https://wa.me/{{ user.phone }}" target="_blank" class="text-decoration-none">
                                    <i class="fab fa-whatsapp text-success me-1"></i> {{ user.phone }}
                                </a>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-4 fw-bold">الدور:</div>
                            <div class="col-md-8">
                                {% if user.role_id == 1 %}
                                    <span class="badge badge-role badge-admin">مدير</span>
                                {% elif user.role_id == 2 %}
                                    <span class="badge badge-role badge-marketer">مسوّق</span>
                                {% else %}
                                    <span class="badge badge-role badge-visitor">زائر</span>
                                {% endif %}
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-4 fw-bold">تاريخ الإنشاء:</div>
                            <div class="col-md-8">{{ user.created_at }}</div>
                        </div>
                        <div class="row">
                            <div class="col-md-4 fw-bold">آخر تحديث:</div>
                            <div class="col-md-8">{{ user.updated_at }}</div>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-history me-2"></i> النشاط الأخير
                    </div>
                    <div class="card-body">
                        <p class="text-muted text-center">لا يوجد نشاط حديث</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal: تعديل الملف الشخصي -->
    <div class="modal fade" id="editProfileModal" tabindex="-1" aria-labelledby="editProfileModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editProfileModalLabel">تعديل الملف الشخصي</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="editProfileForm" method="POST" action="{{ url_for('profile_update') }}">
                        <div class="mb-3">
                            <label for="username" class="form-label">اسم المستخدم</label>
                            <input type="text" class="form-control" id="username" name="username" value="{{ user.username }}" required>
                        </div>
                        <div class="mb-3">
                            <label for="phone" class="form-label">رقم الهاتف</label>
                            <input type="tel" class="form-control" id="phone" name="phone" value="{{ user.phone }}" required>
                            <div class="form-text">أدخل رقم الهاتف مع رمز الدولة بدون علامة + (مثل: 966512345678)</div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" form="editProfileForm" class="btn btn-primary">حفظ التغييرات</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal: تغيير كلمة المرور -->
    <div class="modal fade" id="changePasswordModal" tabindex="-1" aria-labelledby="changePasswordModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="changePasswordModalLabel">تغيير كلمة المرور</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="changePasswordForm" method="POST" action="{{ url_for('change_password') }}">
                        <div class="mb-3">
                            <label for="current_password" class="form-label">كلمة المرور الحالية</label>
                            <input type="password" class="form-control" id="current_password" name="current_password" required>
                        </div>
                        <div class="mb-3">
                            <label for="new_password" class="form-label">كلمة المرور الجديدة</label>
                            <input type="password" class="form-control" id="new_password" name="new_password" required>
                        </div>
                        <div class="mb-3">
                            <label for="confirm_password" class="form-label">تأكيد كلمة المرور الجديدة</label>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" form="changePasswordForm" class="btn btn-warning">تغيير كلمة المرور</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
