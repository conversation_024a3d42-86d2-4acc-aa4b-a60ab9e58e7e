/**
 * إدارة المدن والمناطق في لوحة التحكم
 * Created: 2024
 */

document.addEventListener('DOMContentLoaded', function() {
    // المتغيرات العامة
    let cities = [];
    let districts = {};
    let currentCityId = null;

    // تهيئة إدارة المدن والمناطق
    initRegionsManager();

    /**
     * تهيئة إدارة المدن والمناطق
     */
    function initRegionsManager() {
        console.log('🏙️ تهيئة إدارة المدن والمناطق');

        // جلب المدن والمناطق
        fetchCities();

        // إضافة مستمعي الأحداث للأزرار
        addEventListeners();
    }

    /**
     * إضافة مستمعي الأحداث للأزرار
     */
    function addEventListeners() {
        // إضافة مدينة جديدة
        document.getElementById('addCityBtn').addEventListener('click', addCity);

        // تحديث مدينة
        document.getElementById('updateCityBtn').addEventListener('click', updateCity);

        // حذف مدينة
        document.getElementById('deleteCityBtn').addEventListener('click', deleteCity);

        // إضافة منطقة فرعية
        document.getElementById('addDistrictBtn').addEventListener('click', function() {
            // تعيين معرف المدينة الحالية في نموذج إضافة المنطقة الفرعية
            document.getElementById('add_district_city_id').value = currentCityId;
            // فتح النافذة المنبثقة
            const modal = new bootstrap.Modal(document.getElementById('addDistrictModal'));
            modal.show();
        });

        // إضافة مجموعة مناطق فرعية
        document.getElementById('addDistrictGroupBtn').addEventListener('click', function() {
            // تعيين معرف المدينة الحالية في نموذج إضافة مجموعة المناطق الفرعية
            document.getElementById('add_district_group_city_id').value = currentCityId;

            // تعيين اسم المدينة في عنوان النافذة المنبثقة
            const city = cities.find(c => c.id == currentCityId);
            if (city) {
                document.getElementById('city_name_group_title').textContent = city.name;
            }

            // فتح النافذة المنبثقة
            const modal = new bootstrap.Modal(document.getElementById('addDistrictGroupModal'));
            modal.show();
        });

        // حفظ منطقة فرعية جديدة
        document.getElementById('saveDistrictBtn').addEventListener('click', addDistrict);

        // حفظ مجموعة مناطق فرعية جديدة
        document.getElementById('saveDistrictGroupBtn').addEventListener('click', addDistrictGroup);

        // تحديث منطقة فرعية
        document.getElementById('updateDistrictBtn').addEventListener('click', updateDistrict);

        // حذف منطقة فرعية
        document.getElementById('deleteDistrictBtn').addEventListener('click', deleteDistrict);
    }

    /**
     * جلب المدن من الخادم
     */
    function fetchCities() {
        // إظهار مؤشر التحميل
        const tableBody = document.querySelector('#citiesTable tbody');
        tableBody.innerHTML = '<tr><td colspan="6" class="text-center"><div class="spinner-border spinner-border-sm" role="status"></div> جاري تحميل المدن...</td></tr>';

        // جلب المدن من الخادم
        fetch('/api/regions?type=main')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    cities = data.regions;
                    renderCities();
                } else {
                    showAlert('حدث خطأ أثناء تحميل المدن: ' + data.error, 'danger');
                }
            })
            .catch(error => {
                console.error('Error fetching cities:', error);
                showAlert('حدث خطأ أثناء الاتصال بالخادم', 'danger');
            });
    }

    /**
     * عرض المدن في الجدول
     */
    function renderCities() {
        const tableBody = document.querySelector('#citiesTable tbody');
        tableBody.innerHTML = '';

        if (cities.length === 0) {
            tableBody.innerHTML = '<tr><td colspan="6" class="text-center">لا توجد مدن متاحة</td></tr>';
            return;
        }

        cities.forEach((city, index) => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${index + 1}</td>
                <td>${city.name}</td>
                <td><span class="badge bg-info">${city.districts_count || 0}</span></td>
                <td>${city.latitude !== null ? city.latitude.toFixed(4) : '0.0000'}, ${city.longitude !== null ? city.longitude.toFixed(4) : '0.0000'}</td>
                <td>${city.created_at}</td>
                <td>
                    <button type="button" class="btn btn-sm btn-info manage-districts-btn" data-id="${city.id}" data-name="${city.name}">
                        <i class="fas fa-list"></i>
                    </button>
                    <button type="button" class="btn btn-sm btn-primary edit-city-btn" data-id="${city.id}">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button type="button" class="btn btn-sm btn-danger delete-city-btn" data-id="${city.id}" data-name="${city.name}">
                        <i class="fas fa-trash-alt"></i>
                    </button>
                </td>
            `;
            tableBody.appendChild(row);
        });

        // إضافة مستمعي الأحداث للأزرار
        addCityButtonListeners();
    }

    /**
     * إضافة مستمعي الأحداث لأزرار المدن
     */
    function addCityButtonListeners() {
        // زر إدارة المناطق الفرعية
        document.querySelectorAll('.manage-districts-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const cityId = this.getAttribute('data-id');
                const cityName = this.getAttribute('data-name');
                currentCityId = cityId;

                // تعيين اسم المدينة في عنوان النافذة المنبثقة
                document.getElementById('city_name_title').textContent = cityName;

                // جلب المناطق الفرعية للمدينة
                fetchDistricts(cityId);

                // فتح النافذة المنبثقة
                const modal = new bootstrap.Modal(document.getElementById('manageDistrictsModal'));
                modal.show();
            });
        });

        // زر تعديل المدينة
        document.querySelectorAll('.edit-city-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const cityId = this.getAttribute('data-id');
                editCity(cityId);
            });
        });

        // زر حذف المدينة
        document.querySelectorAll('.delete-city-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const cityId = this.getAttribute('data-id');
                const cityName = this.getAttribute('data-name');

                // تعيين معرف واسم المدينة في نموذج الحذف
                document.getElementById('delete_city_id').value = cityId;
                document.getElementById('delete_city_name').textContent = cityName;

                // فتح النافذة المنبثقة
                const modal = new bootstrap.Modal(document.getElementById('deleteCityModal'));
                modal.show();
            });
        });
    }

    /**
     * جلب المناطق الفرعية لمدينة معينة
     * @param {string} cityId - معرف المدينة
     */
    function fetchDistricts(cityId) {
        // إظهار مؤشر التحميل
        const tableBody = document.querySelector('#districtsTable tbody');
        tableBody.innerHTML = '<tr><td colspan="5" class="text-center"><div class="spinner-border spinner-border-sm" role="status"></div> جاري تحميل المناطق الفرعية...</td></tr>';

        // جلب المناطق الفرعية من الخادم
        fetch(`/api/regions?parent_id=${cityId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    districts[cityId] = data.regions;
                    renderDistricts(cityId);
                } else {
                    showAlert('حدث خطأ أثناء تحميل المناطق الفرعية: ' + data.error, 'danger');
                }
            })
            .catch(error => {
                console.error('Error fetching districts:', error);
                showAlert('حدث خطأ أثناء الاتصال بالخادم', 'danger');
            });
    }

    /**
     * عرض المناطق الفرعية في الجدول
     * @param {string} cityId - معرف المدينة
     */
    function renderDistricts(cityId) {
        const tableBody = document.querySelector('#districtsTable tbody');
        tableBody.innerHTML = '';

        const cityDistricts = districts[cityId] || [];

        if (cityDistricts.length === 0) {
            tableBody.innerHTML = '<tr><td colspan="5" class="text-center">لا توجد مناطق فرعية لهذه المدينة</td></tr>';
            return;
        }

        cityDistricts.forEach((district, index) => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${index + 1}</td>
                <td>${district.name}</td>
                <td>${district.latitude !== null ? district.latitude.toFixed(4) : '0.0000'}, ${district.longitude !== null ? district.longitude.toFixed(4) : '0.0000'}</td>
                <td>${district.created_at}</td>
                <td>
                    <button type="button" class="btn btn-sm btn-primary edit-district-btn" data-id="${district.id}">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button type="button" class="btn btn-sm btn-danger delete-district-btn" data-id="${district.id}" data-name="${district.name}">
                        <i class="fas fa-trash-alt"></i>
                    </button>
                </td>
            `;
            tableBody.appendChild(row);
        });

        // إضافة مستمعي الأحداث للأزرار
        addDistrictButtonListeners();
    }

    /**
     * إضافة مستمعي الأحداث لأزرار المناطق الفرعية
     */
    function addDistrictButtonListeners() {
        // زر تعديل المنطقة الفرعية
        document.querySelectorAll('.edit-district-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const districtId = this.getAttribute('data-id');
                editDistrict(districtId);
            });
        });

        // زر حذف المنطقة الفرعية
        document.querySelectorAll('.delete-district-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const districtId = this.getAttribute('data-id');
                const districtName = this.getAttribute('data-name');

                // تعيين معرف واسم المنطقة الفرعية في نموذج الحذف
                document.getElementById('delete_district_id').value = districtId;
                document.getElementById('delete_district_name').textContent = districtName;

                // فتح النافذة المنبثقة
                const modal = new bootstrap.Modal(document.getElementById('deleteDistrictModal'));
                modal.show();
            });
        });
    }

    /**
     * إضافة مدينة جديدة
     */
    function addCity() {
        // جمع بيانات النموذج
        const name = document.getElementById('add_city_name').value.trim();
        const lat = parseFloat(document.getElementById('add_city_lat').value);
        const lng = parseFloat(document.getElementById('add_city_lng').value);
        const minLat = parseFloat(document.getElementById('add_city_min_lat').value);
        const maxLat = parseFloat(document.getElementById('add_city_max_lat').value);
        const minLng = parseFloat(document.getElementById('add_city_min_lng').value);
        const maxLng = parseFloat(document.getElementById('add_city_max_lng').value);

        // التحقق من صحة البيانات
        if (!name) {
            showAlert('يرجى إدخال اسم المدينة', 'warning');
            return;
        }

        // إنشاء كائن البيانات
        const cityData = {
            name,
            region_type: 'main',
            latitude: lat,
            longitude: lng,
            min_lat: minLat,
            max_lat: maxLat,
            min_lng: minLng,
            max_lng: maxLng
        };

        // إرسال طلب إضافة المدينة
        fetch('/api/regions', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('input[name="csrf_token"]').value
            },
            body: JSON.stringify(cityData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // إغلاق النافذة المنبثقة
                bootstrap.Modal.getInstance(document.getElementById('addCityModal')).hide();

                // إعادة تحميل المدن
                fetchCities();

                // إظهار رسالة نجاح
                showAlert('تم إضافة المدينة بنجاح', 'success');

                // إعادة تعيين النموذج
                document.getElementById('addCityForm').reset();
            } else {
                showAlert('حدث خطأ أثناء إضافة المدينة: ' + data.error, 'danger');
            }
        })
        .catch(error => {
            console.error('Error adding city:', error);
            showAlert('حدث خطأ أثناء الاتصال بالخادم', 'danger');
        });
    }

    /**
     * تحرير مدينة
     * @param {string} cityId - معرف المدينة
     */
    function editCity(cityId) {
        // البحث عن المدينة في المصفوفة
        const city = cities.find(c => c.id == cityId);
        if (!city) {
            showAlert('لم يتم العثور على المدينة', 'danger');
            return;
        }

        // ملء النموذج ببيانات المدينة
        document.getElementById('edit_city_id').value = city.id;
        document.getElementById('edit_city_name').value = city.name;
        document.getElementById('edit_city_lat').value = city.latitude;
        document.getElementById('edit_city_lng').value = city.longitude;
        document.getElementById('edit_city_min_lat').value = city.min_lat;
        document.getElementById('edit_city_max_lat').value = city.max_lat;
        document.getElementById('edit_city_min_lng').value = city.min_lng;
        document.getElementById('edit_city_max_lng').value = city.max_lng;

        // فتح النافذة المنبثقة
        const modal = new bootstrap.Modal(document.getElementById('editCityModal'));
        modal.show();
    }

    /**
     * تحديث مدينة
     */
    function updateCity() {
        // جمع بيانات النموذج
        const cityId = document.getElementById('edit_city_id').value;
        const name = document.getElementById('edit_city_name').value.trim();
        const lat = parseFloat(document.getElementById('edit_city_lat').value);
        const lng = parseFloat(document.getElementById('edit_city_lng').value);
        const minLat = parseFloat(document.getElementById('edit_city_min_lat').value);
        const maxLat = parseFloat(document.getElementById('edit_city_max_lat').value);
        const minLng = parseFloat(document.getElementById('edit_city_min_lng').value);
        const maxLng = parseFloat(document.getElementById('edit_city_max_lng').value);

        // التحقق من صحة البيانات
        if (!name) {
            showAlert('يرجى إدخال اسم المدينة', 'warning');
            return;
        }

        // إنشاء كائن البيانات
        const cityData = {
            id: cityId,
            name,
            region_type: 'main',
            latitude: lat,
            longitude: lng,
            min_lat: minLat,
            max_lat: maxLat,
            min_lng: minLng,
            max_lng: maxLng
        };

        // إرسال طلب تحديث المدينة
        fetch('/api/regions', {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('input[name="csrf_token"]').value
            },
            body: JSON.stringify(cityData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // إغلاق النافذة المنبثقة
                bootstrap.Modal.getInstance(document.getElementById('editCityModal')).hide();

                // إعادة تحميل المدن
                fetchCities();

                // إظهار رسالة نجاح
                showAlert('تم تحديث المدينة بنجاح', 'success');
            } else {
                showAlert('حدث خطأ أثناء تحديث المدينة: ' + data.error, 'danger');
            }
        })
        .catch(error => {
            console.error('Error updating city:', error);
            showAlert('حدث خطأ أثناء الاتصال بالخادم', 'danger');
        });
    }

    /**
     * حذف مدينة
     */
    function deleteCity() {
        // الحصول على معرف المدينة
        const cityId = document.getElementById('delete_city_id').value;

        // إرسال طلب حذف المدينة
        fetch(`/api/regions?id=${cityId}`, {
            method: 'DELETE',
            headers: {
                'X-CSRFToken': document.querySelector('input[name="csrf_token"]').value
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // إغلاق النافذة المنبثقة
                bootstrap.Modal.getInstance(document.getElementById('deleteCityModal')).hide();

                // إعادة تحميل المدن
                fetchCities();

                // إظهار رسالة نجاح
                showAlert('تم حذف المدينة بنجاح', 'success');
            } else {
                showAlert('حدث خطأ أثناء حذف المدينة: ' + data.error, 'danger');
            }
        })
        .catch(error => {
            console.error('Error deleting city:', error);
            showAlert('حدث خطأ أثناء الاتصال بالخادم', 'danger');
        });
    }

    /**
     * إضافة منطقة فرعية
     */
    function addDistrict() {
        // جمع بيانات النموذج
        const cityId = document.getElementById('add_district_city_id').value;
        const name = document.getElementById('add_district_name').value.trim();
        const lat = parseFloat(document.getElementById('add_district_lat').value);
        const lng = parseFloat(document.getElementById('add_district_lng').value);
        const minLat = parseFloat(document.getElementById('add_district_min_lat').value);
        const maxLat = parseFloat(document.getElementById('add_district_max_lat').value);
        const minLng = parseFloat(document.getElementById('add_district_min_lng').value);
        const maxLng = parseFloat(document.getElementById('add_district_max_lng').value);

        // التحقق من صحة البيانات
        if (!name) {
            showAlert('يرجى إدخال اسم المنطقة الفرعية', 'warning');
            return;
        }

        // البحث عن المدينة في المصفوفة
        const city = cities.find(c => c.id == cityId);
        if (!city) {
            showAlert('لم يتم العثور على المدينة', 'danger');
            return;
        }

        // إنشاء كائن البيانات
        const districtData = {
            name: `${city.name} - ${name}`,
            parent_id: cityId,
            region_type: 'sub',
            latitude: lat,
            longitude: lng,
            min_lat: minLat,
            max_lat: maxLat,
            min_lng: minLng,
            max_lng: maxLng
        };

        // إرسال طلب إضافة المنطقة الفرعية
        fetch('/api/regions', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('input[name="csrf_token"]').value
            },
            body: JSON.stringify(districtData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // إغلاق النافذة المنبثقة
                bootstrap.Modal.getInstance(document.getElementById('addDistrictModal')).hide();

                // إعادة تحميل المناطق الفرعية
                fetchDistricts(cityId);

                // إعادة تحميل المدن لتحديث عدد المناطق الفرعية
                fetchCities();

                // إظهار رسالة نجاح
                showAlert('تم إضافة المنطقة الفرعية بنجاح', 'success');

                // إعادة تعيين النموذج
                document.getElementById('addDistrictForm').reset();
            } else {
                showAlert('حدث خطأ أثناء إضافة المنطقة الفرعية: ' + data.error, 'danger');
            }
        })
        .catch(error => {
            console.error('Error adding district:', error);
            showAlert('حدث خطأ أثناء الاتصال بالخادم', 'danger');
        });
    }

    /**
     * تحرير منطقة فرعية
     * @param {string} districtId - معرف المنطقة الفرعية
     */
    function editDistrict(districtId) {
        // البحث عن المنطقة الفرعية في المصفوفة
        let district = null;
        let cityId = null;

        // البحث في جميع المدن
        for (const cid in districts) {
            const found = districts[cid].find(d => d.id == districtId);
            if (found) {
                district = found;
                cityId = cid;
                break;
            }
        }

        if (!district) {
            showAlert('لم يتم العثور على المنطقة الفرعية', 'danger');
            return;
        }

        // استخراج اسم المنطقة الفرعية بدون اسم المدينة
        let districtName = district.name;
        if (districtName.includes(' - ')) {
            districtName = districtName.split(' - ')[1];
        }

        // ملء النموذج ببيانات المنطقة الفرعية
        document.getElementById('edit_district_id').value = district.id;
        document.getElementById('edit_district_city_id').value = cityId;
        document.getElementById('edit_district_name').value = districtName;
        document.getElementById('edit_district_lat').value = district.latitude;
        document.getElementById('edit_district_lng').value = district.longitude;
        document.getElementById('edit_district_min_lat').value = district.min_lat;
        document.getElementById('edit_district_max_lat').value = district.max_lat;
        document.getElementById('edit_district_min_lng').value = district.min_lng;
        document.getElementById('edit_district_max_lng').value = district.max_lng;

        // فتح النافذة المنبثقة
        const modal = new bootstrap.Modal(document.getElementById('editDistrictModal'));
        modal.show();
    }

    /**
     * تحديث منطقة فرعية
     */
    function updateDistrict() {
        // جمع بيانات النموذج
        const districtId = document.getElementById('edit_district_id').value;
        const cityId = document.getElementById('edit_district_city_id').value;
        const name = document.getElementById('edit_district_name').value.trim();
        const lat = parseFloat(document.getElementById('edit_district_lat').value);
        const lng = parseFloat(document.getElementById('edit_district_lng').value);
        const minLat = parseFloat(document.getElementById('edit_district_min_lat').value);
        const maxLat = parseFloat(document.getElementById('edit_district_max_lat').value);
        const minLng = parseFloat(document.getElementById('edit_district_min_lng').value);
        const maxLng = parseFloat(document.getElementById('edit_district_max_lng').value);

        // التحقق من صحة البيانات
        if (!name) {
            showAlert('يرجى إدخال اسم المنطقة الفرعية', 'warning');
            return;
        }

        // البحث عن المدينة في المصفوفة
        const city = cities.find(c => c.id == cityId);
        if (!city) {
            showAlert('لم يتم العثور على المدينة', 'danger');
            return;
        }

        // إنشاء كائن البيانات
        const districtData = {
            id: districtId,
            name: `${city.name} - ${name}`,
            parent_id: cityId,
            region_type: 'sub',
            latitude: lat,
            longitude: lng,
            min_lat: minLat,
            max_lat: maxLat,
            min_lng: minLng,
            max_lng: maxLng
        };

        // إرسال طلب تحديث المنطقة الفرعية
        fetch('/api/regions', {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('input[name="csrf_token"]').value
            },
            body: JSON.stringify(districtData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // إغلاق النافذة المنبثقة
                bootstrap.Modal.getInstance(document.getElementById('editDistrictModal')).hide();

                // إعادة تحميل المناطق الفرعية
                fetchDistricts(cityId);

                // إظهار رسالة نجاح
                showAlert('تم تحديث المنطقة الفرعية بنجاح', 'success');
            } else {
                showAlert('حدث خطأ أثناء تحديث المنطقة الفرعية: ' + data.error, 'danger');
            }
        })
        .catch(error => {
            console.error('Error updating district:', error);
            showAlert('حدث خطأ أثناء الاتصال بالخادم', 'danger');
        });
    }

    /**
     * حذف منطقة فرعية
     */
    function deleteDistrict() {
        // الحصول على معرف المنطقة الفرعية
        const districtId = document.getElementById('delete_district_id').value;

        // إرسال طلب حذف المنطقة الفرعية
        fetch(`/api/regions?id=${districtId}`, {
            method: 'DELETE',
            headers: {
                'X-CSRFToken': document.querySelector('input[name="csrf_token"]').value
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // إغلاق النافذة المنبثقة
                bootstrap.Modal.getInstance(document.getElementById('deleteDistrictModal')).hide();

                // إعادة تحميل المناطق الفرعية
                fetchDistricts(currentCityId);

                // إعادة تحميل المدن لتحديث عدد المناطق الفرعية
                fetchCities();

                // إظهار رسالة نجاح
                showAlert('تم حذف المنطقة الفرعية بنجاح', 'success');
            } else {
                showAlert('حدث خطأ أثناء حذف المنطقة الفرعية: ' + data.error, 'danger');
            }
        })
        .catch(error => {
            console.error('Error deleting district:', error);
            showAlert('حدث خطأ أثناء الاتصال بالخادم', 'danger');
        });
    }

    /**
     * إضافة مجموعة مناطق فرعية
     */
    function addDistrictGroup() {
        // جمع بيانات النموذج
        const cityId = document.getElementById('add_district_group_city_id').value;
        const districtsList = document.getElementById('district_group_list').value.trim();
        const useDefaultCoordinates = document.getElementById('use_default_coordinates').checked;

        // التحقق من صحة البيانات
        if (!districtsList) {
            showAlert('يرجى إدخال أسماء المناطق الفرعية', 'warning');
            return;
        }

        // البحث عن المدينة في المصفوفة
        const city = cities.find(c => c.id == cityId);
        if (!city) {
            showAlert('لم يتم العثور على المدينة', 'danger');
            return;
        }

        // تقسيم النص إلى أسطر
        const districtNames = districtsList.split('\n')
            .map(name => name.trim())
            .filter(name => name.length > 0);

        if (districtNames.length === 0) {
            showAlert('لم يتم العثور على أسماء مناطق صالحة', 'warning');
            return;
        }

        // إظهار مؤشر التحميل
        const saveButton = document.getElementById('saveDistrictGroupBtn');
        const originalText = saveButton.innerHTML;
        saveButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> جاري الإضافة...';
        saveButton.disabled = true;

        // إنشاء مصفوفة من كائنات المناطق الفرعية
        const districts = districtNames.map(name => {
            return {
                name: `${city.name} - ${name}`,
                parent_id: cityId,
                region_type: 'sub',
                latitude: useDefaultCoordinates ? city.latitude : 0,
                longitude: useDefaultCoordinates ? city.longitude : 0,
                min_lat: useDefaultCoordinates ? city.min_lat : 0,
                max_lat: useDefaultCoordinates ? city.max_lat : 0,
                min_lng: useDefaultCoordinates ? city.min_lng : 0,
                max_lng: useDefaultCoordinates ? city.max_lng : 0
            };
        });

        // عداد للمناطق التي تمت إضافتها بنجاح
        let successCount = 0;
        let errorCount = 0;
        let totalCount = districts.length;

        // دالة لإضافة منطقة فرعية واحدة
        function addSingleDistrict(index) {
            if (index >= districts.length) {
                // انتهاء العملية
                saveButton.innerHTML = originalText;
                saveButton.disabled = false;

                // إغلاق النافذة المنبثقة
                bootstrap.Modal.getInstance(document.getElementById('addDistrictGroupModal')).hide();

                // إعادة تحميل المناطق الفرعية
                fetchDistricts(currentCityId);

                // إعادة تحميل المدن لتحديث عدد المناطق الفرعية
                fetchCities();

                // إظهار رسالة نجاح
                if (successCount === totalCount) {
                    showAlert(`تم إضافة ${successCount} منطقة فرعية بنجاح`, 'success');
                } else {
                    showAlert(`تم إضافة ${successCount} منطقة فرعية بنجاح، وفشل إضافة ${errorCount} منطقة`, 'warning');
                }

                // إعادة تعيين النموذج
                document.getElementById('addDistrictGroupForm').reset();
                return;
            }

            // إرسال طلب إضافة المنطقة الفرعية
            fetch('/api/regions', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': document.querySelector('input[name="csrf_token"]').value
                },
                body: JSON.stringify(districts[index])
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    successCount++;
                } else {
                    errorCount++;
                    console.error(`Error adding district ${districts[index].name}:`, data.error);
                }

                // إضافة المنطقة التالية
                addSingleDistrict(index + 1);
            })
            .catch(error => {
                errorCount++;
                console.error(`Error adding district ${districts[index].name}:`, error);

                // إضافة المنطقة التالية
                addSingleDistrict(index + 1);
            });
        }

        // بدء عملية إضافة المناطق الفرعية
        addSingleDistrict(0);
    }

    /**
     * عرض رسالة تنبيه
     * @param {string} message - نص الرسالة
     * @param {string} type - نوع الرسالة (success, danger, warning, info)
     */
    function showAlert(message, type = 'info') {
        // إنشاء عنصر التنبيه
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.setAttribute('role', 'alert');
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        `;

        // إضافة التنبيه إلى الصفحة
        const container = document.querySelector('.container');
        container.insertBefore(alertDiv, container.firstChild);

        // إخفاء التنبيه بعد 3 ثوانٍ
        setTimeout(() => {
            const bsAlert = new bootstrap.Alert(alertDiv);
            bsAlert.close();
        }, 3000);
    }
});
