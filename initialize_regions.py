import sqlite3
import json
import os
from datetime import datetime

def initialize_regions_db():
    """تهيئة قاعدة بيانات المناطق وإدخال البيانات الأولية"""
    # التحقق من وجود قاعدة البيانات
    if not os.path.exists('database.db'):
        print("قاعدة البيانات غير موجودة. جاري إنشاء قاعدة بيانات جديدة...")
        # إنشاء قاعدة بيانات جديدة
        conn = sqlite3.connect('database.db')
        conn.close()
        print("تم إنشاء قاعدة بيانات جديدة.")

    # الاتصال بقاعدة البيانات
    conn = sqlite3.connect('database.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()

    # التحقق من وجود جدول المناطق
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='regions'")
    if not cursor.fetchone():
        print("جدول المناطق غير موجود. جاري إنشاء الجدول...")
        # إنشاء جدول المناطق
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS regions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            parent_id INTEGER,
            region_type TEXT NOT NULL,
            latitude REAL,
            longitude REAL,
            min_lat REAL,
            max_lat REAL,
            min_lng REAL,
            max_lng REAL,
            synonyms TEXT,
            created_at TEXT,
            updated_at TEXT,
            FOREIGN KEY (parent_id) REFERENCES regions (id)
        )
        ''')

        # إنشاء جدول سجل تحديثات المناطق
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS region_updates (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            region_id INTEGER NOT NULL,
            user_id TEXT,
            update_type TEXT NOT NULL,
            old_value TEXT,
            new_value TEXT,
            created_at TEXT,
            FOREIGN KEY (region_id) REFERENCES regions (id),
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
        ''')

        # إنشاء فهارس للبحث السريع
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_regions_name ON regions (name)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_regions_parent ON regions (parent_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_regions_type ON regions (region_type)')

        conn.commit()
        print("تم إنشاء جدول المناطق بنجاح.")
    else:
        print("جدول المناطق موجود بالفعل.")

    # التحقق من وجود بيانات في جدول المناطق
    cursor.execute("SELECT COUNT(*) FROM regions")
    count = cursor.fetchone()[0]

    if count > 0:
        print(f"يوجد بالفعل {count} منطقة في قاعدة البيانات. هل ترغب في إعادة تهيئة البيانات؟ (y/n)")
        answer = input().lower()
        if answer != 'y':
            print("تم إلغاء عملية إعادة التهيئة.")
            conn.close()
            return False

        # حذف البيانات الموجودة
        cursor.execute("DELETE FROM region_updates")
        cursor.execute("DELETE FROM regions")
        conn.commit()
        print("تم حذف البيانات الموجودة.")

    # إدخال المناطق الرئيسية
    main_regions = [
        {"name": "طرابلس", "lat": 32.8872, "lng": 13.1913, "min_lat": 32.7, "max_lat": 33.0, "min_lng": 12.9, "max_lng": 13.4},
        {"name": "بنغازي", "lat": 32.1167, "lng": 20.0667, "min_lat": 31.9, "max_lat": 32.3, "min_lng": 19.8, "max_lng": 20.3},
        {"name": "مصراتة", "lat": 32.3754, "lng": 15.0925, "min_lat": 32.2, "max_lat": 32.5, "min_lng": 14.9, "max_lng": 15.3}
    ]

    now = datetime.now().isoformat()

    # إدخال المناطق الرئيسية
    main_region_ids = {}
    for region in main_regions:
        cursor.execute('''
        INSERT INTO regions (
            name, region_type, latitude, longitude, min_lat, max_lat, min_lng, max_lng, created_at, updated_at
        )
        VALUES (?, 'main', ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            region["name"],
            region["lat"],
            region["lng"],
            region["min_lat"],
            region["max_lat"],
            region["min_lng"],
            region["max_lng"],
            now,
            now
        ))

        # الحصول على معرف المنطقة المدرجة
        cursor.execute("SELECT last_insert_rowid()")
        region_id = cursor.fetchone()[0]
        main_region_ids[region["name"]] = region_id

    conn.commit()
    print(f"تم إدخال {len(main_regions)} منطقة رئيسية.")

    # قائمة المناطق الفرعية في طرابلس
    tripoli_districts = [
        {"name": "وسط المدينة", "lat": 32.8925, "lng": 13.1802, "min_lat": 32.880, "max_lat": 32.905, "min_lng": 13.170, "max_lng": 13.190},
        {"name": "باب البحر", "lat": 32.8964, "lng": 13.1823, "min_lat": 32.890, "max_lat": 32.903, "min_lng": 13.175, "max_lng": 13.190},
        {"name": "سوق الجمعة", "lat": 32.9086, "lng": 13.1944, "min_lat": 32.900, "max_lat": 32.920, "min_lng": 13.185, "max_lng": 13.205},
        {"name": "أبو سليم", "lat": 32.8472, "lng": 13.1731, "min_lat": 32.835, "max_lat": 32.860, "min_lng": 13.160, "max_lng": 13.185},
        {"name": "عين زارة", "lat": 32.8167, "lng": 13.2500, "min_lat": 32.800, "max_lat": 32.835, "min_lng": 13.230, "max_lng": 13.270},
        {"name": "تاجوراء", "lat": 32.8828, "lng": 13.3500, "min_lat": 32.865, "max_lat": 32.900, "min_lng": 13.320, "max_lng": 13.380},
        {"name": "جنزور", "lat": 32.8333, "lng": 13.0333, "min_lat": 32.820, "max_lat": 32.850, "min_lng": 13.000, "max_lng": 13.070},
        {"name": "حي الأندلس", "lat": 32.8700, "lng": 13.1400, "min_lat": 32.860, "max_lat": 32.880, "min_lng": 13.130, "max_lng": 13.150},
        {"name": "قرقارش", "lat": 32.8600, "lng": 13.1100, "min_lat": 32.850, "max_lat": 32.870, "min_lng": 13.090, "max_lng": 13.130},
        {"name": "السراج", "lat": 32.8200, "lng": 13.1000, "min_lat": 32.810, "max_lat": 32.830, "min_lng": 13.090, "max_lng": 13.110},
        {"name": "سيدي المصري", "lat": 32.8800, "lng": 13.1600, "min_lat": 32.870, "max_lat": 32.890, "min_lng": 13.150, "max_lng": 13.170},
        {"name": "الهضبة", "lat": 32.8500, "lng": 13.2100, "min_lat": 32.840, "max_lat": 32.860, "min_lng": 13.200, "max_lng": 13.220},
        {"name": "باب بن غشير", "lat": 32.8550, "lng": 13.1850, "min_lat": 32.845, "max_lat": 32.865, "min_lng": 13.175, "max_lng": 13.195},
        {"name": "الفرناج", "lat": 32.8600, "lng": 13.1950, "min_lat": 32.850, "max_lat": 32.870, "min_lng": 13.185, "max_lng": 13.205},
        {"name": "قصر بن غشير", "lat": 32.7900, "lng": 13.1400, "min_lat": 32.780, "max_lat": 32.800, "min_lng": 13.130, "max_lng": 13.150},
        {"name": "الهضبة الخضراء", "lat": 32.8450, "lng": 13.2050, "min_lat": 32.835, "max_lat": 32.855, "min_lng": 13.195, "max_lng": 13.215},
        {"name": "غوط الشعال", "lat": 32.8750, "lng": 13.1700, "min_lat": 32.865, "max_lat": 32.885, "min_lng": 13.160, "max_lng": 13.180},
        {"name": "السياحية", "lat": 32.8650, "lng": 13.1300, "min_lat": 32.855, "max_lat": 32.875, "min_lng": 13.120, "max_lng": 13.140},
        {"name": "زاوية الدهماني", "lat": 32.8850, "lng": 13.1750, "min_lat": 32.875, "max_lat": 32.895, "min_lng": 13.165, "max_lng": 13.185},
        {"name": "فشلوم", "lat": 32.8950, "lng": 13.1850, "min_lat": 32.885, "max_lat": 32.905, "min_lng": 13.175, "max_lng": 13.195},
        {"name": "المدينة القديمة", "lat": 32.8950, "lng": 13.1800, "min_lat": 32.885, "max_lat": 32.905, "min_lng": 13.170, "max_lng": 13.190},
        {"name": "طريق المطار", "lat": 32.8100, "lng": 13.1900, "min_lat": 32.800, "max_lat": 32.820, "min_lng": 13.180, "max_lng": 13.200}
    ]

    # إدخال المناطق الفرعية في طرابلس
    for district in tripoli_districts:
        cursor.execute('''
        INSERT INTO regions (
            name, parent_id, region_type, latitude, longitude, min_lat, max_lat, min_lng, max_lng, created_at, updated_at
        )
        VALUES (?, ?, 'sub', ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            district["name"],
            main_region_ids["طرابلس"],
            district["lat"],
            district["lng"],
            district["min_lat"],
            district["max_lat"],
            district["min_lng"],
            district["max_lng"],
            now,
            now
        ))

    conn.commit()
    print(f"تم إدخال {len(tripoli_districts)} منطقة فرعية في طرابلس.")

    # قائمة المناطق الفرعية في بنغازي
    benghazi_districts = [
        {"name": "وسط المدينة", "lat": 32.1167, "lng": 20.0667, "min_lat": 32.105, "max_lat": 32.125, "min_lng": 20.055, "max_lng": 20.075},
        {"name": "الصابري", "lat": 32.1200, "lng": 20.0800, "min_lat": 32.115, "max_lat": 32.125, "min_lng": 20.075, "max_lng": 20.085},
        {"name": "سيدي حسين", "lat": 32.1100, "lng": 20.0500, "min_lat": 32.105, "max_lat": 32.115, "min_lng": 20.045, "max_lng": 20.055},
        {"name": "الليثي", "lat": 32.0900, "lng": 20.0700, "min_lat": 32.085, "max_lat": 32.095, "min_lng": 20.065, "max_lng": 20.075},
        {"name": "بوهديمة", "lat": 32.0800, "lng": 20.0900, "min_lat": 32.075, "max_lat": 32.085, "min_lng": 20.085, "max_lng": 20.095},
        {"name": "القوارشة", "lat": 32.1300, "lng": 20.0400, "min_lat": 32.125, "max_lat": 32.135, "min_lng": 20.035, "max_lng": 20.045},
        {"name": "بنينا", "lat": 32.0967, "lng": 20.2694, "min_lat": 32.090, "max_lat": 32.100, "min_lng": 20.260, "max_lng": 20.280},
        {"name": "الكيش", "lat": 32.1000, "lng": 20.0600, "min_lat": 32.095, "max_lat": 32.105, "min_lng": 20.055, "max_lng": 20.065},
        {"name": "سيدي خليفة", "lat": 32.0700, "lng": 20.1200, "min_lat": 32.065, "max_lat": 32.075, "min_lng": 20.115, "max_lng": 20.125},
        {"name": "قاريونس", "lat": 32.0800, "lng": 20.1100, "min_lat": 32.075, "max_lat": 32.085, "min_lng": 20.105, "max_lng": 20.115},
        {"name": "طريق المطار", "lat": 32.0800, "lng": 20.2000, "min_lat": 32.075, "max_lat": 32.085, "min_lng": 20.190, "max_lng": 20.210}
    ]

    # إدخال المناطق الفرعية في بنغازي
    for district in benghazi_districts:
        cursor.execute('''
        INSERT INTO regions (
            name, parent_id, region_type, latitude, longitude, min_lat, max_lat, min_lng, max_lng, created_at, updated_at
        )
        VALUES (?, ?, 'sub', ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            district["name"],
            main_region_ids["بنغازي"],
            district["lat"],
            district["lng"],
            district["min_lat"],
            district["max_lat"],
            district["min_lng"],
            district["max_lng"],
            now,
            now
        ))

    conn.commit()
    print(f"تم إدخال {len(benghazi_districts)} منطقة فرعية في بنغازي.")

    # قائمة المناطق الفرعية في مصراتة
    misurata_districts = [
        {"name": "وسط المدينة", "lat": 32.3754, "lng": 15.0925, "min_lat": 32.365, "max_lat": 32.385, "min_lng": 15.080, "max_lng": 15.105},
        {"name": "الدافنية", "lat": 32.3600, "lng": 15.0600, "min_lat": 32.350, "max_lat": 32.370, "min_lng": 15.050, "max_lng": 15.070},
        {"name": "زاوية المحجوب", "lat": 32.3900, "lng": 15.1100, "min_lat": 32.380, "max_lat": 32.400, "min_lng": 15.100, "max_lng": 15.120},
        {"name": "قصر أحمد", "lat": 32.3800, "lng": 15.1000, "min_lat": 32.370, "max_lat": 32.390, "min_lng": 15.090, "max_lng": 15.110},
        {"name": "طمينة", "lat": 32.3500, "lng": 15.0800, "min_lat": 32.340, "max_lat": 32.360, "min_lng": 15.070, "max_lng": 15.090}
    ]

    # إدخال المناطق الفرعية في مصراتة
    for district in misurata_districts:
        cursor.execute('''
        INSERT INTO regions (
            name, parent_id, region_type, latitude, longitude, min_lat, max_lat, min_lng, max_lng, created_at, updated_at
        )
        VALUES (?, ?, 'sub', ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            district["name"],
            main_region_ids["مصراتة"],
            district["lat"],
            district["lng"],
            district["min_lat"],
            district["max_lat"],
            district["min_lng"],
            district["max_lng"],
            now,
            now
        ))

    conn.commit()
    print(f"تم إدخال {len(misurata_districts)} منطقة فرعية في مصراتة.")

    # إغلاق الاتصال بقاعدة البيانات
    conn.close()
    print("تم تهيئة قاعدة بيانات المناطق بنجاح.")
    return True

if __name__ == "__main__":
    initialize_regions_db()
