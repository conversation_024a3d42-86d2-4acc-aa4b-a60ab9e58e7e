/**
 * تحميل بيانات المدن والمناطق من قاعدة البيانات
 * وتحديث حقول المدينة والمنطقة في نموذج إضافة المتجر
 */

// كائن لتخزين بيانات المدن والمناطق
const RegionsLoader = {
    // بيانات المدن والمناطق
    cities: [],
    districts: {},

    // تهيئة تحميل المدن والمناطق
    init: function() {
        console.log('🌍 تهيئة تحميل المدن والمناطق');
        this.loadCities();
    },

    // تحميل المدن من الخادم
    loadCities: function() {
        console.log('🏙️ جاري تحميل المدن...');

        // جلب المدن من الخادم باستخدام API العام
        fetch('/api/regions/public?type=main')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    this.cities = data.regions || [];
                    console.log(`✅ تم تحميل ${this.cities.length} مدينة`);

                    // تحديث قوائم المدن
                    this.updateCitySelects();

                    // تحميل المناطق الفرعية لكل مدينة إذا كانت هناك مدن
                    if (this.cities.length > 0) {
                        this.loadAllDistricts();
                    } else {
                        console.warn('⚠️ لا توجد مدن لتحميل المناطق الفرعية لها');
                    }
                } else {
                    console.error('❌ خطأ في تحميل المدن:', data.error);
                }
            })
            .catch(error => {
                console.error('❌ خطأ في الاتصال بالخادم:', error);
            });
    },

    // تحميل المناطق الفرعية لجميع المدن
    loadAllDistricts: function() {
        console.log('🏘️ جاري تحميل المناطق الفرعية لجميع المدن...');

        // تحميل المناطق الفرعية لكل مدينة
        this.cities.forEach(city => {
            this.loadDistricts(city.id);
        });
    },

    // تحميل المناطق الفرعية لمدينة معينة
    loadDistricts: function(cityId) {
        console.log(`🏘️ جاري تحميل المناطق الفرعية للمدينة ${cityId}...`);

        // جلب المناطق الفرعية من الخادم باستخدام API العام
        fetch(`/api/regions/public?parent_id=${cityId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    this.districts[cityId] = data.regions || [];
                    console.log(`✅ تم تحميل ${this.districts[cityId].length} منطقة فرعية للمدينة ${cityId}`);

                    // تحديث قوائم المناطق الفرعية
                    this.updateDistrictSelects();
                } else {
                    console.error(`❌ خطأ في تحميل المناطق الفرعية للمدينة ${cityId}:`, data.error);
                }
            })
            .catch(error => {
                console.error(`❌ خطأ في الاتصال بالخادم:`, error);
            });
    },

    // تحديث قوائم المدن
    updateCitySelects: function() {
        console.log('🔄 تحديث قوائم المدن...');

        // الحصول على جميع قوائم المدن
        const citySelects = [
            document.getElementById('citySelect'),
            document.getElementById('mobileCitySelect')
        ].filter(select => select !== null); // استبعاد العناصر غير الموجودة

        if (citySelects.length === 0) {
            console.log('⚠️ لم يتم العثور على أي قوائم مدن في الصفحة');
            return;
        }

        // تحديث كل قائمة
        citySelects.forEach(select => {
            // حفظ القيمة الحالية
            const currentValue = select.value;

            // إفراغ القائمة مع الاحتفاظ بالخيار الافتراضي
            const defaultOption = select.querySelector('option[value=""]');
            select.innerHTML = '';

            if (defaultOption) {
                select.appendChild(defaultOption);
            } else {
                // إضافة خيار افتراضي إذا لم يكن موجودًا
                const option = document.createElement('option');
                option.value = '';
                option.textContent = 'اختر المدينة';
                option.selected = true;
                select.appendChild(option);
            }

            // إضافة المدن فقط من قاعدة البيانات
            if (this.cities.length === 0) {
                console.log('⚠️ لا توجد مدن في قاعدة البيانات');
                // إضافة خيار توضيحي
                const option = document.createElement('option');
                option.value = "";
                option.textContent = "لا توجد مدن متاحة - يرجى إضافة المدن من لوحة التحكم";
                option.disabled = true;
                select.appendChild(option);
            } else {
                // إضافة المدن من قاعدة البيانات
                this.cities.forEach(city => {
                    const option = document.createElement('option');
                    option.value = city.name;
                    option.textContent = city.name;
                    option.dataset.cityId = city.id;
                    select.appendChild(option);
                });
            }

            // استعادة القيمة السابقة إذا كانت موجودة
            if (currentValue) {
                select.value = currentValue;
            }

            console.log(`✅ تم تحديث قائمة المدن: ${select.id}`);

            // تشغيل حدث change لتحديث قائمة المناطق
            select.dispatchEvent(new Event('change'));
        });
    },

    // تحديث قوائم المناطق الفرعية
    updateDistrictSelects: function() {
        console.log('🔄 تحديث قوائم المناطق الفرعية...');

        // الحصول على جميع قوائم المناطق الفرعية
        const districtSelects = [
            document.getElementById('districtSelect'),
            document.getElementById('mobileDistrictSelect')
        ].filter(select => select !== null); // استبعاد العناصر غير الموجودة

        if (districtSelects.length === 0) {
            console.log('⚠️ لم يتم العثور على أي قوائم مناطق في الصفحة');
            return;
        }

        // تحديث كل قائمة
        districtSelects.forEach(select => {
            // حفظ القيمة الحالية
            const currentValue = select.value;

            // إفراغ القائمة مع الاحتفاظ بالخيار الافتراضي
            const defaultOption = select.querySelector('option[value=""]');
            select.innerHTML = '';

            if (defaultOption) {
                select.appendChild(defaultOption);
            } else {
                // إضافة خيار افتراضي إذا لم يكن موجودًا
                const option = document.createElement('option');
                option.value = '';
                option.textContent = 'المنطقة غير محددة';
                option.selected = true;
                option.style.color = '#dc3545'; // اللون الأحمر
                select.appendChild(option);
            }

            // إضافة المناطق الفرعية فقط إذا كانت هناك مدن
            if (this.cities.length === 0) {
                console.log('⚠️ لا توجد مدن لإضافة مناطقها الفرعية');
                // لا نضيف أي شيء في هذه الحالة
            } else {
                // إضافة المناطق الفرعية من قاعدة البيانات
                this.cities.forEach(city => {
                    // إنشاء مجموعة خيارات للمدينة
                    const optgroup = document.createElement('optgroup');
                    optgroup.label = `مناطق ${city.name}`;
                    optgroup.className = `${this.getCityClassName(city.name)}-districts`;
                    optgroup.style.display = 'none'; // إخفاء المجموعة افتراضيًا

                    // إضافة المناطق الفرعية للمدينة
                    const cityDistricts = this.districts[city.id] || [];

                    // استخدام المناطق الفرعية من قاعدة البيانات فقط
                    if (cityDistricts.length === 0) {
                        // إضافة خيار توضيحي
                        const option = document.createElement('option');
                        option.value = "";
                        option.textContent = `لا توجد مناطق فرعية لمدينة ${city.name}`;
                        option.disabled = true;
                        optgroup.appendChild(option);
                    } else {
                        // إضافة المناطق الفرعية من قاعدة البيانات
                        cityDistricts.forEach(district => {
                            const option = document.createElement('option');
                            // إزالة اسم المدينة من اسم المنطقة إذا كان موجودًا
                            const districtName = district.name.includes(`${city.name} - `) ?
                                district.name.replace(`${city.name} - `, '') : district.name;
                            option.value = districtName;
                            option.textContent = districtName;
                            option.dataset.districtId = district.id;
                            optgroup.appendChild(option);
                        });
                    }

                    // إضافة المجموعة إلى القائمة فقط إذا كانت تحتوي على خيارات
                    if (optgroup.children.length > 0) {
                        select.appendChild(optgroup);
                    }
                });
            }

            // استعادة القيمة السابقة إذا كانت موجودة
            if (currentValue) {
                select.value = currentValue;
            }

            console.log(`✅ تم تحديث قائمة المناطق الفرعية: ${select.id}`);
        });
    },

    // الحصول على اسم الصف للمدينة
    getCityClassName: function(cityName) {
        switch (cityName) {
            case 'طرابلس':
                return 'tripoli';
            case 'بنغازي':
                return 'benghazi';
            case 'مصراتة':
                return 'misrata';
            default:
                return cityName.toLowerCase().replace(/\s+/g, '-');
        }
    }
};

// تهيئة تحميل المدن والمناطق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // التحقق من وجود عناصر واجهة المستخدم في الصفحة
    const hasStoreForm = document.getElementById('citySelect') || document.getElementById('mobileCitySelect');

    // تهيئة تحميل المدن والمناطق فقط إذا كانت الصفحة تحتوي على نموذج إضافة متجر
    if (hasStoreForm) {
        console.log('🔍 تم العثور على نموذج إضافة متجر، جاري تهيئة تحميل المدن والمناطق...');
        RegionsLoader.init();
    } else {
        console.log('ℹ️ لا يوجد نموذج إضافة متجر في هذه الصفحة، تم تخطي تحميل المدن والمناطق.');
    }
});
