import os
import secrets

class Config:
    """إعدادات التطبيق الأساسية"""
    SECRET_KEY = os.environ.get('SECRET_KEY', secrets.token_hex(16))
    UPLOAD_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'static', 'uploads')
    ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif'}
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16 ميجابايت كحد أقصى
    REMEMBER_COOKIE_DURATION = 60 * 60 * 24 * 30  # 30 يوم
    
    # إعدادات قاعدة البيانات
    DATABASE = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'stores.db')
    
    # إعدادات المستخدم الافتراضي
    ADMIN_USERNAME = 'admin'
    ADMIN_EMAIL = '<EMAIL>'
    ADMIN_PASSWORD = 'admin123'
    
    @staticmethod
    def init_app(app):
        """تهيئة التطبيق"""
        # إنشاء مجلد التحميلات إذا لم يكن موجودًا
        if not os.path.exists(app.config['UPLOAD_FOLDER']):
            os.makedirs(app.config['UPLOAD_FOLDER'])

class DevelopmentConfig(Config):
    """إعدادات بيئة التطوير"""
    DEBUG = True

class ProductionConfig(Config):
    """إعدادات بيئة الإنتاج"""
    DEBUG = False
    
    @classmethod
    def init_app(cls, app):
        Config.init_app(app)
        # إعدادات إضافية لبيئة الإنتاج

config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}
