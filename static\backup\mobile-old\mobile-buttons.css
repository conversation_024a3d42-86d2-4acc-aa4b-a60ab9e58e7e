/**
 * أنماط خاصة بأزرار واجهة الهاتف المحمول
 */

/* أزرار التبويبات */
.mobile-tab-btn {
    background-color: white;
    color: #666;
    border: none;
    border-radius: 0;
    padding: 0.75rem 0;
    font-size: 0.85rem;
    transition: all 0.3s ease;
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 0.25rem;
    position: relative;
    overflow: hidden;
}

.mobile-tab-btn i {
    font-size: 1.25rem;
    transition: all 0.3s ease;
}

.mobile-tab-btn:active {
    background-color: #f5f5f5;
    transform: scale(0.95);
}

.mobile-tab-btn.active {
    color: var(--loacker-red);
}

.mobile-tab-btn.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #6a11cb 0%, #2575fc 100%);
    transform: scaleX(1);
    transition: transform 0.3s ease;
}

.mobile-tab-btn:not(.active)::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #6a11cb 0%, #2575fc 100%);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.mobile-tab-btn:hover::after {
    transform: scaleX(0.5);
}

/* زر الإضافة العائم */
.mobile-fab {
    position: fixed;
    bottom: 80px;
    right: 20px;
    width: 56px;
    height: 56px;
    border-radius: 50%;
    background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
    border: none;
    z-index: 1000;
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.mobile-fab i {
    font-size: 24px;
}

.mobile-fab:active {
    transform: scale(0.9) rotate(45deg);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

/* أزرار بطاقات المتاجر */
.mobile-store-card .btn-group .btn {
    padding: 0.375rem 0.5rem;
    transition: all 0.2s ease;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    border-radius: 0;
    position: relative;
    z-index: 1;
}

.mobile-store-card .btn-group .btn:first-child {
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
}

.mobile-store-card .btn-group .btn:last-child {
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
}

.mobile-store-card .btn-group .btn:active {
    transform: scale(0.95);
    opacity: 0.9;
}

.mobile-store-card .btn-group .btn i {
    margin-right: 2px;
}

/* أزرار النموذج */
.mobile-form .btn {
    padding: 0.75rem 1rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.mobile-form .btn-primary {
    background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
    border: none;
}

.mobile-form .btn-primary:active {
    transform: scale(0.98);
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
}

.mobile-form .btn-outline-primary:hover {
    background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
    border-color: transparent;
}

.mobile-form .btn-outline-secondary:hover {
    background-color: #343a40;
    border-color: #343a40;
    color: white;
}

/* تفاعل الضغط المستمر */
.mobile-store-card .btn:active,
.mobile-form .btn:active,
.mobile-tab-btn:active,
.mobile-fab:active {
    animation: pulse 0.3s forwards;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(0.95);
    }
    100% {
        transform: scale(0.98);
    }
}

/* تأثيرات الموجة عند الضغط */
.mobile-store-card .btn::after,
.mobile-form .btn::after,
.mobile-tab-btn::after,
.mobile-fab::after {
    content: '';
    display: block;
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    pointer-events: none;
    background-image: radial-gradient(circle, #fff 10%, transparent 10.01%);
    background-repeat: no-repeat;
    background-position: 50%;
    transform: scale(10, 10);
    opacity: 0;
    transition: transform 0.5s, opacity 0.5s;
}

.mobile-store-card .btn:active::after,
.mobile-form .btn:active::after,
.mobile-tab-btn:active::after,
.mobile-fab:active::after {
    transform: scale(0, 0);
    opacity: 0.3;
    transition: 0s;
}

/* تحسين ظهور الأزرار في النسخة المحمولة */
.btn {
    position: relative;
    overflow: hidden;
    cursor: pointer;
    min-width: 40px; /* عرض أدنى للأزرار */
    margin: 0 2px; /* مسافة بين الأزرار */
}

/* تنسيقات أزرار الحذف والمشاركة في النسخة المحمولة */
.btn-outline-danger, .btn-outline-success {
    padding: 0.375rem 0.75rem;
}

.btn:after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 5px;
    height: 5px;
    background: rgba(255, 255, 255, 0.5);
    opacity: 0;
    border-radius: 100%;
    transform: scale(1, 1) translate(-50%);
    transform-origin: 50% 50%;
}

.btn:focus:not(:active)::after {
    animation: ripple 1s ease-out;
}

@keyframes ripple {
    0% {
        transform: scale(0, 0);
        opacity: 0.5;
    }
    20% {
        transform: scale(25, 25);
        opacity: 0.3;
    }
    100% {
        opacity: 0;
        transform: scale(40, 40);
    }
}