/* تنسيقات خريطة المدير */

/* تنسيق زر عرض الموقع */
.view-location-btn {
    transition: all 0.3s ease;
    border-radius: 20px;
    padding: 0.25rem 0.75rem;
    background-color: #17a2b8;
    border-color: #17a2b8;
}

.view-location-btn:hover {
    background-color: #138496;
    border-color: #117a8b;
    transform: scale(1.05);
}

.view-location-btn i {
    color: #fff;
    margin-right: 5px;
}

/* تنسيق علامة الموقع على الخريطة */
.store-marker-container {
    background: transparent;
    border: none;
}

.store-marker-container i {
    font-size: 24px;
    text-shadow: 0 2px 5px rgba(0, 0, 0, 0.5);
    transition: all 0.3s ease;
}

.store-marker-container i:hover {
    transform: scale(1.2);
}

/* تنسيق النافذة المنبثقة للعلامة */
.leaflet-popup-content-wrapper {
    border-radius: 10px;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
}

.store-popup {
    padding: 5px;
    min-width: 200px;
}

.store-popup h5 {
    color: #d50000;
    font-weight: bold;
    margin-bottom: 10px;
}

/* تنسيق متحكم الطبقات */
.leaflet-control-layers {
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.leaflet-control-layers-toggle {
    width: 36px;
    height: 36px;
    background-size: 20px 20px;
}

/* تنسيق النافذة المنبثقة للخريطة */
#storeLocationModal .modal-content {
    border-radius: 10px;
    overflow: hidden;
}

#storeLocationModal .modal-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

#storeLocationModal .modal-title {
    color: #d50000;
    font-weight: bold;
}

#storeLocationModal .modal-footer {
    background-color: #f8f9fa;
    border-top: 1px solid #dee2e6;
}

/* تأثير نبض للعلامة */
@keyframes pulse-marker {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.2);
    }
    100% {
        transform: scale(1);
    }
}

.pulse-marker {
    animation: pulse-marker 1.5s infinite;
}
