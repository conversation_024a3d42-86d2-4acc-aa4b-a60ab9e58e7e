<?xml version="1.0" encoding="UTF-8"?>
<svg width="200" height="80" viewBox="0 0 200 80" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#d50000" />
      <stop offset="100%" stop-color="#ff6b6b" />
    </linearGradient>
    <filter id="shadow" x="-10%" y="-10%" width="120%" height="120%">
      <feDropShadow dx="0" dy="2" stdDeviation="2" flood-color="#00000033" />
    </filter>
  </defs>
  <style>
    .text { font-family: Arial, sans-serif; font-weight: bold; }
    .main-text { fill: url(#logoGradient); font-size: 40px; filter: url(#shadow); }
    .sub-text { fill: #333; font-size: 16px; }
    .logo-bg { fill: #fff; stroke: url(#logoGradient); stroke-width: 3; filter: url(#shadow); }
  </style>
  <rect x="10" y="10" width="180" height="60" rx="12" class="logo-bg"/>
  <text x="30" y="50" class="text main-text">LOACKER</text>
  <text x="32" y="68" class="text sub-text">إدارة المتاجر</text>
</svg>
