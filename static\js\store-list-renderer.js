/**
 * وظائف عرض قائمة المتاجر
 */

// دالة إنشاء عنصر متجر في القائمة
function createStoreListItem(store) {
    // التحقق من وجود بيانات المتجر
    if (!store) return null;
    
    // إنشاء عنصر div للمتجر
    const storeItem = document.createElement('div');
    storeItem.className = 'store-item';
    storeItem.dataset.storeId = store.id;
    
    // إنشاء محتوى العنصر
    let storeHTML = `
        <div class="store-item-header">
            <div class="store-checkbox-wrapper">
                <input type="checkbox" class="store-checkbox" id="store-check-${store.id}">
                <label for="store-check-${store.id}" class="store-checkbox-label"></label>
            </div>
            <h5 class="store-name">${store.name}</h5>
        </div>
        <div class="store-item-body">
    `;
    
    // إضافة صورة المتجر إذا كانت متوفرة
    if (store.image_url) {
        storeHTML += `
            <div class="store-image">
                <img src="${store.image_url}" alt="${store.name}" class="img-fluid">
            </div>
        `;
    }
    
    // إضافة معلومات المتجر
    storeHTML += `
            <div class="store-info">
                <div class="store-phone">
                    ${store.phone ? `<a href="tel:${store.phone}" class="store-phone-link"><i class="fas fa-phone-alt"></i> ${store.phone}</a>` : ''}
                </div>
                <div class="store-location">
                    <i class="fas fa-map-marker-alt"></i> 
                    <a href="https://www.google.com/maps?q=${store.latitude},${store.longitude}" target="_blank" class="store-map-link">
                        عرض على الخريطة
                    </a>
                </div>
            </div>
        </div>
        <div class="store-item-footer">
            <button type="button" class="btn btn-sm btn-outline-success share-store-whatsapp" title="مشاركة عبر الواتساب">
                <i class="fab fa-whatsapp"></i>
            </button>
            <button type="button" class="btn btn-sm btn-outline-primary edit-store" title="تعديل المتجر">
                <i class="fas fa-edit"></i>
            </button>
            <button type="button" class="btn btn-sm btn-outline-danger delete-store" title="حذف المتجر">
                <i class="fas fa-trash-alt"></i>
            </button>
        </div>
    `;
    
    // تعيين المحتوى HTML للعنصر
    storeItem.innerHTML = storeHTML;
    
    return storeItem;
}

// دالة عرض قائمة المتاجر
function renderStoreList(stores, containerId = 'storeList') {
    // الحصول على حاوية قائمة المتاجر
    const container = document.getElementById(containerId);
    if (!container) return;
    
    // مسح المحتوى الحالي
    container.innerHTML = '';
    
    // التحقق من وجود متاجر
    if (!stores || stores.length === 0) {
        container.innerHTML = '<div class="no-stores-message">لا توجد متاجر للعرض</div>';
        return;
    }
    
    // إنشاء وإضافة عناصر المتاجر
    stores.forEach(store => {
        const storeItem = createStoreListItem(store);
        if (storeItem) {
            container.appendChild(storeItem);
        }
    });
    
    // تحديث عدد المتاجر
    const storeCountElement = document.getElementById('storeCount');
    if (storeCountElement) {
        storeCountElement.textContent = stores.length;
    }
}

// تصدير الدوال
window.StoreListRenderer = {
    createStoreListItem,
    renderStoreList
};
