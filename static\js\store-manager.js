/**
 * StoreManager class for handling store-related operations
 * Created: 15-04-2024
 */
class StoreManager {
    constructor(mapManager) {
        this.stores = [];
        this.filteredStores = [];
        this.selectedStore = null;
        this.mapManager = mapManager;
        this.storeListElement = document.getElementById('store-list');
        this.searchInput = document.getElementById('search-store');
        this.storeFormElement = document.getElementById('store-form');
        this.loadingElement = document.querySelector('.loading-indicator');

        // Initialize event listeners for store operations
        this.initEventListeners();

        // Fetch initial stores
        this.fetchStores();

        console.log('🏪 Store Manager initialized');
    }

    /**
     * Initialize event listeners for store-related operations
     */
    initEventListeners() {
        // Search input listener
        if (this.searchInput) {
            this.searchInput.addEventListener('input', this.handleSearch.bind(this));
        }

        // Store form submission
        if (this.storeFormElement) {
            this.storeFormElement.addEventListener('submit', this.handleStoreSubmit.bind(this));
        }

        // Add other store-related event listeners
        document.addEventListener('click', (event) => {
            // Delegate click event for store list items
            if (event.target.closest('.store-item')) {
                const storeId = event.target.closest('.store-item').dataset.storeId;
                this.selectStore(storeId);
            }

            // Handle store edit button
            if (event.target.closest('.edit-store-btn')) {
                const storeId = event.target.closest('.store-item').dataset.storeId;
                this.editStore(storeId);
                event.stopPropagation();
            }

            // Handle store delete button
            if (event.target.closest('.delete-store-btn')) {
                const storeId = event.target.closest('.store-item').dataset.storeId;
                this.deleteStore(storeId);
                event.stopPropagation();
            }
        });
    }

    /**
     * Fetch stores from the API
     */
    async fetchStores() {
        try {
            this.showLoading();

            const response = await fetch('/api/stores');
            if (!response.ok) {
                throw new Error(`Failed to fetch stores: ${response.status}`);
            }

            const data = await response.json();
            this.stores = data.stores || [];
            this.filteredStores = [...this.stores];

            // Render stores and update map
            this.renderStores();
            if (this.mapManager) {
                this.mapManager.addStoreMarkers(this.stores);
            }

            this.hideLoading();
            console.log(`📊 Loaded ${this.stores.length} stores`);
        } catch (error) {
            this.hideLoading();
            console.error('Error fetching stores:', error);
            this.showNotification('فشل في تحميل المتاجر. يرجى المحاولة مرة أخرى لاحقًا.', 'error');
        }
    }

    /**
     * Handle search functionality
     * @param {Event} event - Input event
     */
    handleSearch(event) {
        const searchTerm = event.target.value.toLowerCase().trim();

        if (searchTerm === '') {
            this.filteredStores = [...this.stores];
        } else {
            this.filteredStores = this.stores.filter(store =>
                store.name.toLowerCase().includes(searchTerm) ||
                store.address.toLowerCase().includes(searchTerm)
            );
        }

        this.renderStores();

        // Update map markers if map manager is available
        if (this.mapManager) {
            this.mapManager.filterMarkers(this.filteredStores);
        }
    }

    /**
     * Render stores in the store list element
     */
    renderStores() {
        if (!this.storeListElement) return;

        // Clear the store list
        this.storeListElement.innerHTML = '';

        if (this.filteredStores.length === 0) {
            const emptyMessage = document.createElement('div');
            emptyMessage.classList.add('empty-stores-message');
            emptyMessage.textContent = 'لم يتم العثور على متاجر.';
            this.storeListElement.appendChild(emptyMessage);
            return;
        }

        // Create and append store items
        this.filteredStores.forEach(store => {
            const storeItem = document.createElement('div');
            storeItem.classList.add('store-item', 'card', 'mb-2');
            storeItem.dataset.storeId = store.id;

            // Highlight selected store
            if (this.selectedStore && this.selectedStore.id === store.id) {
                storeItem.classList.add('selected');
            }

            storeItem.innerHTML = `
                <div class="card-body">
                    <h5 class="card-title">${store.name}</h5>
                    <p class="card-text">
                        <i class="fas fa-map-marker-alt"></i> ${store.address}
                    </p>
                    <div class="store-actions">
                        <button class="btn btn-sm btn-outline-primary edit-store-btn">
                            <i class="fas fa-edit"></i> تعديل
                        </button>
                        <button class="btn btn-sm btn-outline-danger delete-store-btn">
                            <i class="fas fa-trash"></i> حذف
                        </button>
                    </div>
                </div>
            `;

            this.storeListElement.appendChild(storeItem);
        });
    }

    /**
     * Select a store and show its details
     * @param {string} storeId - ID of the store to select
     */
    selectStore(storeId) {
        // Find the store in our list
        const store = this.stores.find(s => s.id === storeId);
        if (!store) return;

        this.selectedStore = store;

        // Update UI to highlight selected store
        const storeItems = document.querySelectorAll('.store-item');
        storeItems.forEach(item => {
            item.classList.remove('selected');
            if (item.dataset.storeId === storeId) {
                item.classList.add('selected');
            }
        });

        // Center map on selected store if map manager is available
        if (this.mapManager) {
            this.mapManager.focusStoreOnMap(store);
        }

        // Display store details in a modal or details panel
        this.showStoreDetails(store);

        console.log('🏪 Selected store:', store.name);
    }

    /**
     * Show store details in a modal or details panel
     * @param {Object} store - Store object with details
     */
    showStoreDetails(store) {
        // Find or create the store details element
        let detailsElement = document.getElementById('store-details');

        if (!detailsElement) {
            detailsElement = document.createElement('div');
            detailsElement.id = 'store-details';
            detailsElement.classList.add('store-details-panel');
            document.querySelector('.main-container').appendChild(detailsElement);
        }

        // Update details content
        detailsElement.innerHTML = `
            <div class="details-header">
                <h3>${store.name}</h3>
                <button class="close-details-btn">&times;</button>
            </div>
            <div class="details-content">
                <p><strong>العنوان:</strong> ${store.address}</p>
                <p><strong>رقم الهاتف:</strong> ${store.phone || 'غير متوفر'}</p>
                <p><strong>البريد الإلكتروني:</strong> ${store.email || 'غير متوفر'}</p>
                <p><strong>الإحداثيات:</strong> ${store.latitude}, ${store.longitude}</p>
                <p><strong>تم إضافته:</strong> ${new Date(store.created_at).toLocaleDateString('ar-SA')}</p>
                <div class="store-actions mt-3">
                    <button class="btn btn-primary edit-details-btn">
                        <i class="fas fa-edit"></i> تعديل المتجر
                    </button>
                    <button class="btn btn-danger delete-details-btn">
                        <i class="fas fa-trash"></i> حذف المتجر
                    </button>
                </div>
            </div>
        `;

        // Show the details panel
        detailsElement.classList.add('active');

        // Add event listeners to the buttons in the details panel
        const closeBtn = detailsElement.querySelector('.close-details-btn');
        const editBtn = detailsElement.querySelector('.edit-details-btn');
        const deleteBtn = detailsElement.querySelector('.delete-details-btn');

        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                detailsElement.classList.remove('active');
                this.selectedStore = null;

                // Remove selected class from all store items
                const storeItems = document.querySelectorAll('.store-item');
                storeItems.forEach(item => item.classList.remove('selected'));
            });
        }

        if (editBtn) {
            editBtn.addEventListener('click', () => this.editStore(store.id));
        }

        if (deleteBtn) {
            deleteBtn.addEventListener('click', () => this.deleteStore(store.id));
        }
    }

    /**
     * Handle store form submission
     * @param {Event} event - Form submit event
     */
    async handleStoreSubmit(event) {
        event.preventDefault();

        const formData = new FormData(event.target);
        const storeData = {
            name: formData.get('name'),
            address: formData.get('address'),
            latitude: parseFloat(formData.get('latitude')),
            longitude: parseFloat(formData.get('longitude')),
            phone: formData.get('phone'),
            email: formData.get('email')
        };

        // التحقق من وجود موقع محدد
        if (!storeData.latitude || !storeData.longitude || isNaN(storeData.latitude) || isNaN(storeData.longitude)) {
            this.showNotification('⚠️ يجب تحديد موقع المتجر على الخريطة قبل الإضافة', 'error');

            // تسليط الضوء على حقل الإحداثيات
            const locationField = event.target.querySelector('input[name="latitude"]');
            if (locationField) {
                locationField.classList.add('is-invalid');
                setTimeout(() => {
                    locationField.classList.remove('is-invalid');
                }, 3000);
            }

            return;
        }

        // Check if editing or creating new store
        const storeId = formData.get('store_id');

        try {
            this.showLoading();

            let response;
            if (storeId) {
                // Update existing store
                response = await fetch(`/api/stores/${storeId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(storeData)
                });
            } else {
                // Create new store
                response = await fetch('/api/stores', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(storeData)
                });
            }

            if (!response.ok) {
                throw new Error(`Operation failed: ${response.status}`);
            }

            // Refresh stores list
            await this.fetchStores();

            // Close form modal
            const modal = document.getElementById('store-modal');
            if (modal && typeof bootstrap !== 'undefined') {
                const bsModal = bootstrap.Modal.getInstance(modal);
                if (bsModal) bsModal.hide();
            }

            // Show success message
            const action = storeId ? 'تعديل' : 'إضافة';
            this.showNotification(`تم ${action} المتجر بنجاح`, 'success');

            this.hideLoading();
        } catch (error) {
            this.hideLoading();
            console.error('Error saving store:', error);
            this.showNotification('فشلت العملية. يرجى المحاولة مرة أخرى.', 'error');
        }
    }

    /**
     * Edit a store
     * @param {string} storeId - ID of the store to edit
     */
    editStore(storeId) {
        // التحقق من صلاحيات المستخدم
        const userRoleElement = document.querySelector('meta[name="user-role"]');
        const userRole = userRoleElement ? parseInt(userRoleElement.getAttribute('content')) : null;

        // إذا كان المستخدم زائراً (الدور 3)، لا يمكنه تعديل المتاجر
        if (userRole === 3) {
            return;
        }

        const store = this.stores.find(s => s.id === storeId);
        if (!store) return;

        // Find store form
        const form = this.storeFormElement;
        if (!form) return;

        // Populate form with store data
        form.querySelector('input[name="store_id"]').value = store.id;
        form.querySelector('input[name="name"]').value = store.name;
        form.querySelector('input[name="address"]').value = store.address;
        form.querySelector('input[name="latitude"]').value = store.latitude;
        form.querySelector('input[name="longitude"]').value = store.longitude;
        form.querySelector('input[name="phone"]').value = store.phone || '';
        form.querySelector('input[name="email"]').value = store.email || '';

        // Update form title
        const modalTitle = document.querySelector('#store-modal .modal-title');
        if (modalTitle) modalTitle.textContent = 'تعديل متجر';

        // Show modal
        const modal = document.getElementById('store-modal');
        if (modal && typeof bootstrap !== 'undefined') {
            const bsModal = new bootstrap.Modal(modal);
            bsModal.show();
        }

        console.log('✏️ Editing store:', store.name);
    }

    /**
     * Delete a store
     * @param {string} storeId - ID of the store to delete
     */
    async deleteStore(storeId) {
        // التحقق من صلاحيات المستخدم
        const userRoleElement = document.querySelector('meta[name="user-role"]');
        const userRole = userRoleElement ? parseInt(userRoleElement.getAttribute('content')) : null;

        // إذا كان المستخدم زائراً (الدور 3)، لا يمكنه حذف المتاجر
        if (userRole === 3) {
            return;
        }

        // Confirm deletion
        if (!confirm('هل أنت متأكد من حذف هذا المتجر؟')) {
            return;
        }

        try {
            this.showLoading();

            const response = await fetch(`/api/stores/${storeId}`, {
                method: 'DELETE'
            });

            if (!response.ok) {
                throw new Error(`Deletion failed: ${response.status}`);
            }

            // Refresh stores list
            await this.fetchStores();

            // Close details panel if it's open
            const detailsElement = document.getElementById('store-details');
            if (detailsElement) {
                detailsElement.classList.remove('active');
            }

            this.showNotification('تم حذف المتجر بنجاح', 'success');
            this.hideLoading();
        } catch (error) {
            this.hideLoading();
            console.error('Error deleting store:', error);
            this.showNotification('فشل في حذف المتجر. يرجى المحاولة مرة أخرى.', 'error');
        }
    }

    /**
     * Show loading indicator
     */
    showLoading() {
        if (this.loadingElement) {
            this.loadingElement.classList.add('active');
        }
    }

    /**
     * Hide loading indicator
     */
    hideLoading() {
        if (this.loadingElement) {
            this.loadingElement.classList.remove('active');
        }
    }

    /**
     * Show notification
     * @param {string} message - Notification message
     * @param {string} type - Notification type (success, error, info)
     */
    showNotification(message, type = 'info') {
        // Find or create notification container
        let notifContainer = document.querySelector('.notification-container');

        if (!notifContainer) {
            notifContainer = document.createElement('div');
            notifContainer.classList.add('notification-container');
            document.body.appendChild(notifContainer);
        }

        // Create notification element
        const notification = document.createElement('div');
        notification.classList.add('notification', `notification-${type}`);
        notification.innerHTML = `
            <div class="notification-content">
                <span class="notification-message">${message}</span>
                <button class="notification-close">&times;</button>
            </div>
        `;

        // Add to container
        notifContainer.appendChild(notification);

        // Show with animation
        setTimeout(() => {
            notification.classList.add('show');
        }, 10);

        // Set auto-dismiss timer
        const dismissTime = type === 'error' ? 5000 : 3000;
        const dismissTimer = setTimeout(() => {
            this.dismissNotification(notification);
        }, dismissTime);

        // Add close button event
        const closeBtn = notification.querySelector('.notification-close');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                clearTimeout(dismissTimer);
                this.dismissNotification(notification);
            });
        }
    }

    /**
     * Dismiss a notification with animation
     * @param {Element} notification - Notification element to dismiss
     */
    dismissNotification(notification) {
        notification.classList.remove('show');
        notification.classList.add('hide');

        // Remove from DOM after animation
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }
}

// Initialize store manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM loaded, initializing StoreManager');
    const storeManager = new StoreManager();
    storeManager.init();

    // Make storeManager available globally
    window.storeManager = storeManager;

    // Add form submission handler
    const storeForm = document.getElementById('storeForm');
    if (storeForm) {
        storeForm.addEventListener('submit', (e) => {
            e.preventDefault();
            const formData = new FormData(storeForm);
            const storeData = {};

            // Convert form data to object
            for (const [key, value] of formData.entries()) {
                storeData[key] = value;
            }

            // Determine if add or edit mode
            const isEditMode = storeForm.dataset.mode === 'edit';
            const storeId = storeForm.dataset.storeId;

            // Add store
            if (!isEditMode) {
                fetch('/api/stores', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(storeData)
                })
                .then(response => response.json())
                .then(data => {
                    // Hide modal
                    const modal = bootstrap.Modal.getInstance(document.getElementById('storeFormContainer'));
                    modal.hide();

                    // Refresh stores
                    storeManager.fetchStores();

                    // Show success message
                    storeManager.showNotification('تمت إضافة المتجر بنجاح', 'success');
                })
                .catch(error => {
                    console.error('Error adding store:', error);
                    storeManager.showNotification('فشل إضافة المتجر', 'danger');
                });
            }
            // Edit store
            else {
                fetch(`/api/stores/${storeId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(storeData)
                })
                .then(response => response.json())
                .then(data => {
                    // Hide modal
                    const modal = bootstrap.Modal.getInstance(document.getElementById('storeFormContainer'));
                    modal.hide();

                    // Refresh stores
                    storeManager.fetchStores();

                    // Show success message
                    storeManager.showNotification('تم تحديث المتجر بنجاح', 'success');
                })
                .catch(error => {
                    console.error('Error updating store:', error);
                    storeManager.showNotification('فشل تحديث المتجر', 'danger');
                });
            }
        });
    }
});

// Add CSS for notifications
const style = document.createElement('style');
style.textContent = `
.store-item {
    cursor: pointer;
    transition: transform 0.2s, box-shadow 0.2s;
}

.store-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.store-item.selected {
    border-color: var(--primary-color, #007bff);
    background-color: rgba(0, 123, 255, 0.05);
}

.store-details-card {
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.notification {
    position: fixed;
    bottom: 20px;
    right: 20px;
    padding: 10px 20px;
    border-radius: 4px;
    background-color: #333;
    color: white;
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    z-index: 9999;
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.3s, transform 0.3s;
}

.notification.show {
    opacity: 1;
    transform: translateY(0);
}

.notification-info {
    background-color: #17a2b8;
}

.notification-success {
    background-color: #28a745;
}

.notification-warning {
    background-color: #ffc107;
    color: #212529;
}

.notification-danger {
    background-color: #dc3545;
}

.empty-details-message {
    opacity: 0.7;
}

.search-results-info {
    font-size: 0.9rem;
    color: #6c757d;
    padding: 5px 10px;
}

.loading-indicator {
    text-align: center;
    padding: 2rem;
    color: #6c757d;
}
`;
document.head.appendChild(style);