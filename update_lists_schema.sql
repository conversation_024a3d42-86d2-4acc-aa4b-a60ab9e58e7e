-- إنشاء جدول القوائم المخصصة
CREATE TABLE IF NOT EXISTS lists (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    description TEXT,
    created_at TEXT,
    updated_at TEXT
);

-- إنشاء جدول لربط المسوقين بالقوائم
CREATE TABLE IF NOT EXISTS marketer_lists (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id TEXT NOT NULL,
    list_id INTEGER NOT NULL,
    created_at TEXT,
    FOREIGN KEY (user_id) REFERENCES users (id),
    FOREIGN KEY (list_id) REFERENCES lists (id)
);

-- إضافة بعض القوائم الافتراضية
INSERT INTO lists (name, description, created_at, updated_at)
VALUES 
    ('القائمة الافتراضية', 'القائمة الافتراضية للمتاجر', datetime('now'), datetime('now')),
    ('متاجر الرياض', 'متاجر منطقة الرياض', datetime('now'), datetime('now')),
    ('متاجر جدة', 'متاجر منطقة جدة', datetime('now'), datetime('now'));
