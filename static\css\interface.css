/**
 * أنماط واجهة المستخدم
 * هذا الملف يحتوي على الأنماط الخاصة بإدارة واجهة المستخدم
 */

/* المتغيرات */
:root {
    --transition-speed: 0.3s;
    --primary-color: #6a11cb;
    --secondary-color: #2575fc;
    --background-dark: #1a1a1a;
    --background-light: #2a2a2a;
    --text-color: #ffffff;
    --shadow-light: 0 4px 15px rgba(0, 0, 0, 0.2);
    --shadow-medium: 0 6px 20px rgba(0, 0, 0, 0.3);
    --border-radius: 8px;
    --primary-gradient: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
    --bg-dark: #1a1a1a;
    --bg-darker: #121212;
    --text-light: #ffffff;
    --text-muted: #aaaaaa;
    --shadow-sm: 0 2px 5px rgba(0, 0, 0, 0.15);
    --shadow-md: 0 4px 10px rgba(0, 0, 0, 0.2);
    --shadow-lg: 0 8px 20px rgba(0, 0, 0, 0.3);
    --border-radius-sm: 4px;
    --border-radius-md: 8px;
    --border-radius-lg: 15px;
    --border-color: #333;
}

/* الأنماط الأساسية */
body {
    margin: 0;
    padding: 0;
    min-height: 100vh;
    font-family: 'Cairo', 'Tajawal', sans-serif;
    background-color: var(--background-dark);
    color: var(--text-color);
    direction: rtl;
    position: relative;
    overflow-x: hidden;
    transition: background-color var(--transition-speed) ease;
}

/* أنماط وضع الكمبيوتر */
body.desktop-device {
    background-color: var(--bg-dark);
}

body.desktop-device .container-fluid {
    opacity: 1;
    transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
    transform: translateY(0);
}

body.desktop-device .mobile-view {
    display: none !important;
    opacity: 0;
    transform: translateY(20px);
    pointer-events: none;
}

/* أنماط وضع الهاتف */
body.mobile-device {
    background-color: var(--bg-dark);
}

body.mobile-device .mobile-view {
    opacity: 1;
    transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
    transform: translateY(0);
    display: block !important;
    background-color: #f8f9fa !important;
    color: #333 !important;
}

body.mobile-device .container-fluid {
    display: none !important;
    opacity: 0;
    transform: translateY(20px);
    pointer-events: none;
}

/* تحسينات الانتقال */
.container-fluid,
.mobile-view {
    transition:
        opacity var(--transition-speed) ease,
        transform var(--transition-speed) ease;
}

/* تحسينات للأجهزة المحمولة */
@media (max-width: 767.98px) {
    body.mobile-device {
        overflow-x: hidden;
    }

    .mobile-view {
        width: 100%;
        max-width: 100%;
        margin: 0;
        padding: 0;
    }

    .interface-switch-btn {
        bottom: 80px;
        right: 15px;
        width: 45px;
        height: 45px;
    }
}

/* تحسينات للشاشات الكبيرة */
@media (min-width: 768px) {
    body.desktop-device .container-fluid {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }
}

/* تحسينات الأداء */
* {
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
}

/* تحسينات التمرير */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #2a2a2a;
    border-radius: var(--border-radius-sm);
}

::-webkit-scrollbar-thumb {
    background: #444;
    border-radius: var(--border-radius-sm);
}

::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* تنسيقات المحتوى العام */
.card, .panel {
    will-change: transform;
    transition: transform var(--transition-speed) ease, box-shadow var(--transition-speed) ease;
}

.card:hover, .panel:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

/* رسائل إعلامية */
.interface-notification {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%) translateY(-100px);
    padding: 12px 20px;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-medium);
    z-index: 2000;
    display: flex;
    align-items: center;
    gap: 10px;
    opacity: 0;
    transition: all var(--transition-speed) ease;
}

.interface-notification.show {
    transform: translateX(-50%) translateY(0);
    opacity: 1;
}

.interface-notification i {
    font-size: 1.2rem;
}

.interface-notification span {
    font-weight: 500;
}

/* تحسينات تجربة المستخدم للأزرار */
button, .btn {
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

button::after, .btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 5px;
    height: 5px;
    background: rgba(255, 255, 255, 0.3);
    opacity: 0;
    border-radius: 100%;
    transform: translate(-50%, -50%) scale(1);
    transform-origin: center;
}

button:active::after, .btn:active::after {
    animation: ripple 0.8s ease-out;
}

@keyframes ripple {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 0.5;
    }
    100% {
        transform: translate(-50%, -50%) scale(30);
        opacity: 0;
    }
}

/* تحسينات للفورم */
input, textarea, select {
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

/* زر التبديل بين الواجهات */
.interface-switch-btn {
    position: fixed;
    bottom: 140px;
    right: 20px;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-light);
    border: none;
    z-index: 1050;
    cursor: pointer;
    transition: all var(--transition-speed) ease;
}

.interface-switch-btn:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: var(--shadow-medium);
}

.interface-switch-btn:active {
    transform: translateY(0) scale(0.95);
    box-shadow: var(--shadow-light);
}

.interface-switch-btn i {
    font-size: 1.2rem;
}

/* تأثير النبض للزر */
@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(106, 17, 203, 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(106, 17, 203, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(106, 17, 203, 0);
    }
}

.interface-switch-btn:after {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    z-index: -1;
    animation: pulse 2s infinite;
}

/* تحسينات الجداول */
table {
  border-collapse: separate;
  border-spacing: 0;
  width: 100%;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: var(--shadow-standard);
  transition: all var(--animation-speed) ease;
}

table:hover {
  box-shadow: var(--shadow-hover);
}

table th {
  background-color: #f8f9fa;
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.8em;
  letter-spacing: 0.5px;
  border-bottom: 2px solid #dee2e6;
}

table tr:hover {
  background-color: rgba(74, 144, 226, 0.05);
}

table td, table th {
  padding: 12px 15px;
  text-align: right;
  border-bottom: 1px solid #dee2e6;
}

/* تحميل الصفحة وتأثيرات الانتقال */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideInFromRight {
  from {
    transform: translateX(30px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.slide-in {
  animation: slideInFromRight 0.3s forwards;
}

/* تحسينات مدخلات النماذج */
.form-control {
  transition: border-color var(--animation-speed) ease, box-shadow var(--animation-speed) ease;
}

.form-control:focus {
  border-color: var(--accent-color);
  box-shadow: 0 0 0 0.2rem rgba(74, 144, 226, 0.25);
}

/* قائمة المتاجر */
.store-list-item {
  transition: all var(--animation-speed) ease;
  border-right: 3px solid transparent;
}

.store-list-item:hover {
  border-right-color: var(--accent-color);
  background-color: rgba(74, 144, 226, 0.05);
}

.store-list-item.active {
  border-right-color: var(--accent-color);
  background-color: rgba(74, 144, 226, 0.1);
}

/* التنبيهات والرسائل */
.alert {
  animation: slideInFromRight 0.5s forwards;
  box-shadow: var(--shadow-standard);
}

/* شاشات متجاوبة مع الأجهزة */
@media (max-width: 768px) {
  table {
    font-size: 0.9em;
  }

  table th {
    font-size: 0.75em;
  }

  .form-control, .btn {
    font-size: 1em;
    height: auto;
    padding: 10px 15px;
  }
}

/* مؤثرات التمرير */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: #bbb;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background: #999;
}

/* ===== وسائط الاستعلام ===== */
/* للهواتف المحمولة */
@media (max-width: 1200px) {
    .interface-switch-btn {
        bottom: 30px;
        right: 30px;
        width: 55px;
        height: 55px;
    }

    .interface-notification {
        padding: 15px 25px;
    }
}