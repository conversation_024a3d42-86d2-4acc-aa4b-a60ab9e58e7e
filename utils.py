"""
ملف الوظائف المساعدة لتطبيق Loacker

يحتوي هذا الملف على وظائف مساعدة مختلفة تستخدم في أنحاء التطبيق
مثل معالجة الملفات وغيرها من الوظائف المشتركة.
"""

import os
from flask import current_app
from werkzeug.utils import secure_filename

def allowed_file(filename):
    """
    التحقق من أن الملف له امتداد مسموح به
    
    Args:
        filename (str): اسم الملف للتحقق منه
        
    Returns:
        bool: True إذا كان الملف مسموح به، False خلاف ذلك
    """
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in current_app.config['ALLOWED_EXTENSIONS']

def save_uploaded_file(file, folder=None, custom_filename=None):
    """
    حفظ ملف مرفوع في المجلد المحدد
    
    Args:
        file: كائن الملف المرفوع
        folder (str, optional): المجلد الفرعي داخل مجلد التحميلات. الافتراضي None.
        custom_filename (str, optional): اسم مخصص للملف. الافتراضي None.
        
    Returns:
        str: مسار الملف المحفوظ
    """
    if not file:
        return None
        
    # الحصول على مجلد التحميلات من إعدادات التطبيق
    upload_folder = current_app.config['UPLOAD_FOLDER']
    
    # إضافة المجلد الفرعي إذا تم تحديده
    if folder:
        target_folder = os.path.join(upload_folder, folder)
        # إنشاء المجلد إذا لم يكن موجودًا
        if not os.path.exists(target_folder):
            os.makedirs(target_folder)
    else:
        target_folder = upload_folder
    
    # تأمين اسم الملف
    if custom_filename:
        filename = custom_filename
    else:
        filename = secure_filename(file.filename)
    
    # حفظ الملف
    file_path = os.path.join(target_folder, filename)
    file.save(file_path)
    
    # إرجاع المسار النسبي للملف
    relative_path = os.path.join(os.path.relpath(target_folder, os.path.dirname(os.path.abspath(__file__))), filename)
    return relative_path

def delete_file(file_path):
    """
    حذف ملف من نظام الملفات
    
    Args:
        file_path (str): مسار الملف المراد حذفه
        
    Returns:
        bool: True إذا تم الحذف بنجاح، False خلاف ذلك
    """
    if not file_path:
        return False
    
    # التحقق من وجود الملف
    if os.path.exists(file_path):
        try:
            os.remove(file_path)
            return True
        except Exception:
            return False
    
    return False
