-- إنشاء جدول المناطق الرئيسية والفرعية
CREATE TABLE IF NOT EXISTS regions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,                -- اسم المنطقة
    parent_id INTEGER,                 -- معرف المنطقة الأم (NULL للمناطق الرئيسية)
    region_type TEXT NOT NULL,         -- نوع المنطقة (main = رئيسية، sub = فرعية)
    latitude REAL,                     -- خط العرض
    longitude REAL,                    -- خط الطول
    min_lat REAL,                      -- الحد الأدنى لخط العرض
    max_lat REAL,                      -- الحد الأقصى لخط العرض
    min_lng REAL,                      -- الحد الأدنى لخط الطول
    max_lng REAL,                      -- الحد الأقصى لخط الطول
    synonyms TEXT,                     -- مرادفات أو أسماء بديلة للمنطقة (مفصولة بفواصل)
    created_at TEXT,
    updated_at TEXT,
    FOREIGN KEY (parent_id) REFERENCES regions (id)
);

-- إنشاء فهرس للبحث السريع
CREATE INDEX IF NOT EXISTS idx_regions_name ON regions (name);
CREATE INDEX IF NOT EXISTS idx_regions_parent ON regions (parent_id);
CREATE INDEX IF NOT EXISTS idx_regions_type ON regions (region_type);

-- إنشاء جدول لتخزين سجل تحديثات المناطق
CREATE TABLE IF NOT EXISTS region_updates (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    region_id INTEGER NOT NULL,
    user_id TEXT,
    update_type TEXT NOT NULL,         -- نوع التحديث (add, update, delete)
    old_value TEXT,                    -- القيمة القديمة (JSON)
    new_value TEXT,                    -- القيمة الجديدة (JSON)
    created_at TEXT,
    FOREIGN KEY (region_id) REFERENCES regions (id),
    FOREIGN KEY (user_id) REFERENCES users (id)
);
