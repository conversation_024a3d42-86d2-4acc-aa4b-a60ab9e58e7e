"""
ملف تهيئة تطبيق Loacker للمتاجر

تم تصميم هذا الملف لتهيئة تطبيق Flask بطريقة أفضل ويمكن استخدامه
في بيئات مختلفة مثل التطوير والإنتاج.
"""

import os
from flask import Flask
from flask_login import LoginManager
from flask_wtf.csrf import CSRFProtect
from werkzeug.middleware.proxy_fix import ProxyFix
from config import config
from db import init_db, close_db
from models import User
from logger import setup_logger

login_manager = LoginManager()
csrf = CSRFProtect()

def create_app(config_name=None):
    """إنشاء وتهيئة تطبيق Flask"""
    
    # تحديد بيئة التشغيل
    if config_name is None:
        config_name = os.environ.get('FLASK_ENV', 'development')
    
    # إنشاء تطبيق Flask
    app = Flask(__name__)
    
    # تحميل التكوين المناسب
    app.config.from_object(config[config_name])
    config[config_name].init_app(app)
    
    # دعم العمل خلف بروكسي
    app.wsgi_app = ProxyFix(app.wsgi_app, x_for=1, x_proto=1, x_host=1, x_prefix=1)
    
    # إعداد حماية CSRF
    csrf.init_app(app)
    
    # إعداد نظام تسجيل الدخول
    login_manager.init_app(app)
    login_manager.login_view = 'login'
    login_manager.session_protection = 'strong'
    login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة'
    login_manager.login_message_category = 'warning'
    
    # إعداد السجلات
    setup_logger(app)
    
    # إغلاق قاعدة البيانات عند انتهاء الطلب
    app.teardown_appcontext(close_db)
    
    # تهيئة قاعدة البيانات
    init_db(app)
    
    # استيراد المسارات (يتم هنا لتجنب التدوير في الاستيرادات)
    from app import routes
    
    # تسجيل المسارات
    app.register_blueprint(routes.main_bp)
    app.register_blueprint(routes.api_bp, url_prefix='/api')
    app.register_blueprint(routes.admin_bp, url_prefix='/admin')
    
    @login_manager.user_loader
    def load_user(user_id):
        """تحميل المستخدم لنظام تسجيل الدخول"""
        user_data = User.get_by_id(user_id)
        if user_data:
            user = User(
                id=user_data['id'],
                username=user_data['username'],
                email=user_data['email'],
                role_id=user_data['role_id'],
                is_active=user_data['is_active'] == 1
            )
            return user
        return None
    
    return app 