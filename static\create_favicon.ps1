Add-Type -AssemblyName System.Drawing

# Create bitmap with appropriate size for favicon
$bmp = New-Object System.Drawing.Bitmap 32, 32
$g = [System.Drawing.Graphics]::FromImage($bmp)

# Fill the background
$g.Clear([System.Drawing.Color]::FromArgb(106, 17, 203)) # #6a11cb color

# Draw a circular gradient background (approximated)
$brush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::FromArgb(37, 117, 252)) # #2575fc color
$g.FillEllipse($brush, 4, 4, 24, 24)

# Draw inner circles in white
$whiteBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::White)
$g.Fill<PERSON>llipse($whiteBrush, 8, 8, 16, 16)
$g.Fill<PERSON>llipse($brush, 12, 12, 8, 8)

# Save as ICO file
$bmp.Save("favicon.ico", [System.Drawing.Imaging.ImageFormat]::Icon)

$g.Dispose()
$bmp.Dispose()

Write-Host "Favicon.ico created successfully!" 