/**
 * NotificationManager - Manages toast notifications for the application
 * Created: 15-04-2024
 */
class NotificationManager {
    constructor(options = {}) {
        // Default options
        this.options = {
            container: document.body,
            position: 'top-right', // top-right, top-left, bottom-right, bottom-left, top-center, bottom-center
            maxNotifications: 5,
            duration: 5000, // milliseconds
            animationDuration: 300, // milliseconds
            rtl: document.dir === 'rtl' || document.body.classList.contains('rtl'),
            ...options
        };
        
        // Active notifications
        this.notifications = [];
        
        // Create container
        this.createContainer();
        
        console.log('📢 Notification Manager initialized');
    }
    
    /**
     * Create notification container
     */
    createContainer() {
        // Check if container already exists
        let container = document.getElementById('notification-container');
        if (container) {
            this.container = container;
            return;
        }
        
        // Create container element
        this.container = document.createElement('div');
        this.container.id = 'notification-container';
        this.container.className = `notification-container ${this.options.position} ${this.options.rtl ? 'rtl' : 'ltr'}`;
        
        // Add container to DOM
        this.options.container.appendChild(this.container);
    }
    
    /**
     * Show a notification
     * @param {Object} options - Notification options
     * @param {string} options.message - The notification message
     * @param {string} options.type - Type of notification (info, success, warning, error)
     * @param {number} options.duration - Duration in milliseconds
     * @param {boolean} options.dismissible - Whether notification can be dismissed
     * @param {string} options.icon - Icon class or URL
     * @returns {HTMLElement} - The notification element
     */
    show(options) {
        const config = {
            message: '',
            type: 'info',
            duration: this.options.duration,
            dismissible: true,
            icon: null,
            ...options
        };
        
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification-${config.type} ${this.options.rtl ? 'rtl' : 'ltr'}`;
        notification.setAttribute('role', 'alert');
        
        // Add icon if provided
        if (config.icon) {
            const iconElement = document.createElement('div');
            iconElement.className = 'notification-icon';
            
            // Check if icon is a URL or class
            if (config.icon.startsWith('http') || config.icon.startsWith('data:')) {
                const img = document.createElement('img');
                img.src = config.icon;
                img.alt = config.type;
                iconElement.appendChild(img);
            } else {
                iconElement.innerHTML = `<i class="${config.icon}"></i>`;
            }
            
            notification.appendChild(iconElement);
        }
        
        // Create content element
        const content = document.createElement('div');
        content.className = 'notification-content';
        content.textContent = config.message;
        notification.appendChild(content);
        
        // Add close button if dismissible
        if (config.dismissible) {
            const closeButton = document.createElement('button');
            closeButton.className = 'notification-close';
            closeButton.innerHTML = '×';
            closeButton.setAttribute('aria-label', 'Close notification');
            closeButton.addEventListener('click', () => this.dismiss(notification));
            notification.appendChild(closeButton);
        }
        
        // Add notification to container
        this.container.appendChild(notification);
        
        // Apply enter animation
        setTimeout(() => {
            notification.classList.add('show');
        }, 10);
        
        // Add to active notifications
        this.notifications.push(notification);
        
        // Check for max notifications
        this.checkMaxNotifications();
        
        // Auto dismiss after duration
        if (config.duration > 0) {
            notification.timer = setTimeout(() => {
                this.dismiss(notification);
            }, config.duration);
        }
        
        // Add event listeners for pause on hover
        notification.addEventListener('mouseenter', () => {
            if (notification.timer) {
                clearTimeout(notification.timer);
            }
        });
        
        notification.addEventListener('mouseleave', () => {
            if (config.duration > 0) {
                notification.timer = setTimeout(() => {
                    this.dismiss(notification);
                }, config.duration);
            }
        });
        
        // Return notification element
        return notification;
    }
    
    /**
     * Show success notification
     * @param {string} message - Notification message
     * @param {Object} options - Additional options
     * @returns {HTMLElement} - The notification element
     */
    success(message, options = {}) {
        return this.show({
            message,
            type: 'success',
            icon: 'notification-icon-success',
            ...options
        });
    }
    
    /**
     * Show error notification
     * @param {string} message - Notification message
     * @param {Object} options - Additional options
     * @returns {HTMLElement} - The notification element
     */
    error(message, options = {}) {
        return this.show({
            message,
            type: 'error',
            icon: 'notification-icon-error',
            ...options
        });
    }
    
    /**
     * Show warning notification
     * @param {string} message - Notification message
     * @param {Object} options - Additional options
     * @returns {HTMLElement} - The notification element
     */
    warning(message, options = {}) {
        return this.show({
            message,
            type: 'warning',
            icon: 'notification-icon-warning',
            ...options
        });
    }
    
    /**
     * Show info notification
     * @param {string} message - Notification message
     * @param {Object} options - Additional options
     * @returns {HTMLElement} - The notification element
     */
    info(message, options = {}) {
        return this.show({
            message,
            type: 'info',
            icon: 'notification-icon-info',
            ...options
        });
    }
    
    /**
     * Dismiss a notification
     * @param {HTMLElement} notification - The notification to dismiss
     */
    dismiss(notification) {
        // Clear timer if exists
        if (notification.timer) {
            clearTimeout(notification.timer);
        }
        
        // Apply exit animation
        notification.classList.remove('show');
        notification.classList.add('hide');
        
        // Remove notification after animation
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
            
            // Remove from active notifications
            this.notifications = this.notifications.filter(n => n !== notification);
        }, this.options.animationDuration);
    }
    
    /**
     * Dismiss all notifications
     */
    dismissAll() {
        // Copy array to avoid modification during iteration
        const notificationsCopy = [...this.notifications];
        
        // Dismiss each notification
        notificationsCopy.forEach(notification => {
            this.dismiss(notification);
        });
    }
    
    /**
     * Check for maximum notifications and remove oldest if needed
     */
    checkMaxNotifications() {
        while (this.notifications.length > this.options.maxNotifications) {
            const oldest = this.notifications[0];
            this.dismiss(oldest);
        }
    }
    
    /**
     * Update notification manager options
     * @param {Object} options - New options
     */
    updateOptions(options = {}) {
        // Update options
        this.options = {
            ...this.options,
            ...options
        };
        
        // Update container position
        if (this.container) {
            // Remove all position classes
            this.container.classList.remove(
                'top-right', 'top-left', 'bottom-right', 
                'bottom-left', 'top-center', 'bottom-center',
                'rtl', 'ltr'
            );
            
            // Add new position class
            this.container.classList.add(this.options.position);
            
            // Update RTL setting
            if (this.options.rtl) {
                this.container.classList.add('rtl');
                this.container.classList.remove('ltr');
            } else {
                this.container.classList.add('ltr');
                this.container.classList.remove('rtl');
            }
        }
    }
    
    /**
     * Clean up resources when destroying the instance
     */
    destroy() {
        // Dismiss all notifications
        this.dismissAll();
        
        // Remove container from DOM
        if (this.container && this.container.parentNode) {
            this.container.parentNode.removeChild(this.container);
        }
        
        console.log('📢 Notification Manager destroyed');
    }
}

// Create and export instance
window.notificationManager = new NotificationManager(); 