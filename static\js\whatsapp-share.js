/**
 * وظائف مشاركة المتاجر عبر الواتساب
 */

// متغيرات عامة
let userPhone = '';

// الحصول على رقم هاتف المستخدم الحالي
async function getUserPhone() {
    try {
        const response = await fetch('/api/user/phone');
        const data = await response.json();

        if (data.success && data.phone) {
            userPhone = data.phone;
            console.log('تم الحصول على رقم هاتف المستخدم:', userPhone);
            return data.phone;
        } else {
            console.warn('لم يتم العثور على رقم هاتف للمستخدم');
            return '';
        }
    } catch (error) {
        console.error('خطأ في الحصول على رقم هاتف المستخدم:', error);
        return '';
    }
}

// إنشاء رابط مشاركة واتساب للمتجر
function createWhatsAppShareLink(store, userPhone = '') {
    // التحقق من وجود بيانات المتجر
    if (!store) return '';

    // إنشاء نص المشاركة
    let shareText = `🏪 *${store.name}*\n\n`;

    // إضافة عنوان المتجر
    let storeAddress = '';
    if (store.city_name && store.region_name) {
        storeAddress = `${store.city_name} - ${store.region_name}`;
    } else if (store.address) {
        storeAddress = store.address;
    }

    if (storeAddress) {
        shareText += `🏠 *العنوان:* ${storeAddress}\n`;
    }

    // إضافة رقم الهاتف إذا كان متوفرًا
    if (store.phone) {
        shareText += `📞 *رقم الهاتف:* ${store.phone}\n`;
    }

    // إضافة الموقع
    shareText += `📍 *الموقع:* https://www.google.com/maps?q=${store.latitude},${store.longitude}\n\n`;

    // إضافة معلومات إضافية
    shareText += `تم مشاركة هذا المتجر بواسطة تطبيق Loacker 🌟`;

    // إنشاء رابط الواتساب
    const encodedText = encodeURIComponent(shareText);

    // إذا تم تمرير رقم هاتف المستخدم، نستخدمه للمشاركة المباشرة
    if (userPhone) {
        return `https://wa.me/${userPhone}?text=${encodedText}`;
    } else {
        // مشاركة عامة بدون رقم هاتف محدد
        return `https://wa.me/?text=${encodedText}`;
    }
}

// مشاركة متجر واحد عبر الواتساب
function shareStoreViaWhatsApp(store) {
    // التحقق من وجود بيانات المتجر
    if (!store) {
        showAlert('لم يتم العثور على بيانات المتجر للمشاركة', 'warning');
        return;
    }

    // الحصول على رقم هاتف المستخدم إذا لم يكن متوفرًا
    if (!userPhone) {
        getUserPhone().then(phone => {
            const shareLink = createWhatsAppShareLink(store, phone);
            window.open(shareLink, '_blank');
        });
    } else {
        const shareLink = createWhatsAppShareLink(store, userPhone);
        window.open(shareLink, '_blank');
    }
}

// مشاركة مجموعة متاجر عبر الواتساب
function shareStoresViaWhatsApp(stores) {
    // التحقق من وجود متاجر للمشاركة
    if (!stores || stores.length === 0) {
        showAlert('لم يتم تحديد متاجر للمشاركة', 'warning');
        return;
    }

    // إنشاء نص المشاركة
    let shareText = `🏪 *قائمة المتاجر (${stores.length})*\n\n`;

    // إضافة معلومات كل متجر
    stores.forEach((store, index) => {
        shareText += `*${index + 1}. ${store.name}*\n`;

        // إضافة عنوان المتجر
        let storeAddress = '';
        if (store.city_name && store.region_name) {
            storeAddress = `${store.city_name} - ${store.region_name}`;
        } else if (store.address) {
            storeAddress = store.address;
        }

        if (storeAddress) {
            shareText += `🏠 ${storeAddress}\n`;
        }

        // إضافة رقم الهاتف إذا كان متوفرًا
        if (store.phone) {
            shareText += `📞 ${store.phone}\n`;
        }

        // إضافة الموقع
        shareText += `📍 https://www.google.com/maps?q=${store.latitude},${store.longitude}\n\n`;
    });

    // إضافة معلومات إضافية
    shareText += `تم مشاركة هذه المتاجر بواسطة تطبيق Loacker 🌟`;

    // إنشاء رابط الواتساب
    const encodedText = encodeURIComponent(shareText);

    // الحصول على رقم هاتف المستخدم إذا لم يكن متوفرًا
    if (!userPhone) {
        getUserPhone().then(phone => {
            const shareLink = `https://wa.me/${phone || ''}?text=${encodedText}`;
            window.open(shareLink, '_blank');
        });
    } else {
        const shareLink = `https://wa.me/${userPhone}?text=${encodedText}`;
        window.open(shareLink, '_blank');
    }
}

// دالة للعثور على متجر بواسطة المعرف
function findStoreById(storeId) {
    // محاولة الحصول على المتجر من storeManager إذا كان متوفراً
    if (window.storeManager && typeof window.storeManager.findStoreById === 'function') {
        return window.storeManager.findStoreById(storeId);
    }

    // محاولة الحصول على المتجر من stores المحفوظة في window
    if (window.stores && Array.isArray(window.stores)) {
        return window.stores.find(store => store.id === storeId);
    }

    // محاولة الحصول على المتجر من عنصر DOM
    const storeElement = document.querySelector(`[data-store-id="${storeId}"]`);
    if (storeElement) {
        // استخراج بيانات المتجر من عنصر DOM
        return {
            id: storeId,
            name: storeElement.dataset.storeName || storeElement.querySelector('.store-name')?.textContent || 'متجر غير معروف',
            phone: storeElement.dataset.storePhone || storeElement.querySelector('.store-phone')?.textContent || '',
            latitude: storeElement.dataset.storeLat || storeElement.dataset.latitude || '',
            longitude: storeElement.dataset.storeLng || storeElement.dataset.longitude || ''
        };
    }

    return null;
}

// دالة للحصول على المتاجر المحددة
function getSelectedStores() {
    // محاولة الحصول على المتاجر المحددة من storeManager إذا كان متوفراً
    if (window.storeManager && typeof window.storeManager.getSelectedStores === 'function') {
        return window.storeManager.getSelectedStores();
    }

    // محاولة الحصول على المتاجر المحددة من خانات الاختيار
    const selectedStores = [];
    const checkboxes = document.querySelectorAll('.store-checkbox:checked, input[type="checkbox"]:checked[data-store-id]');

    checkboxes.forEach(checkbox => {
        const storeId = checkbox.value || checkbox.dataset.storeId;
        if (storeId) {
            const store = findStoreById(storeId);
            if (store) {
                selectedStores.push(store);
            }
        }
    });

    return selectedStores;
}

// دالة لعرض التنبيهات
function showAlert(message, type = 'info') {
    // محاولة استخدام دالة showAlert الموجودة
    if (typeof window.showAlert === 'function') {
        return window.showAlert(message, type);
    }

    // محاولة استخدام دالة showNotification الموجودة
    if (window.storeManager && typeof window.storeManager.showNotification === 'function') {
        return window.storeManager.showNotification(message, type);
    }

    // استخدام alert عادي كبديل
    alert(message);
}

// تهيئة وظائف مشاركة الواتساب
function initWhatsAppSharing() {
    // الحصول على رقم هاتف المستخدم عند تحميل الصفحة
    getUserPhone();

    // إضافة مستمعي الأحداث لأزرار المشاركة
    document.addEventListener('click', (event) => {
        // مشاركة متجر واحد
        if (event.target.closest('.share-store-whatsapp')) {
            const storeElement = event.target.closest('.store-item');
            if (storeElement && storeElement.dataset.storeId) {
                const storeId = storeElement.dataset.storeId;
                const store = findStoreById(storeId);
                if (store) {
                    shareStoreViaWhatsApp(store);
                } else {
                    showAlert('لم يتم العثور على بيانات المتجر', 'warning');
                }
            }
        }

        // مشاركة المتاجر المحددة
        if (event.target.closest('#shareSelectedStores')) {
            const selectedStores = getSelectedStores();
            if (selectedStores.length > 0) {
                shareStoresViaWhatsApp(selectedStores);
            } else {
                showAlert('يرجى تحديد متاجر للمشاركة أولاً', 'info');
            }
        }
    });
}

// استدعاء دالة التهيئة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', initWhatsAppSharing);
