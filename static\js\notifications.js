/**
 * نظام الإشعارات المتكامل - LOACKER Application
 * إدارة عرض وإخفاء الإشعارات بشكل متقدم 
 */
class NotificationManager {
    constructor() {
        this.container = null;
        this.toggleButton = null;
        this.notifications = [];
        this.hasNewNotifications = false;
        this.iconClasses = {
            success: 'fas fa-check-circle',
            error: 'fas fa-times-circle',
            warning: 'fas fa-exclamation-triangle',
            info: 'fas fa-info-circle'
        };
    }

    /**
     * تهيئة نظام الإشعارات
     */
    init() {
        // إنشاء حاوية الإشعارات إذا لم تكن موجودة
        if (!document.querySelector('.notification-container')) {
            this.container = document.createElement('div');
            this.container.className = 'notification-container';
            document.body.appendChild(this.container);
        } else {
            this.container = document.querySelector('.notification-container');
        }

        // إنشاء زر التبديل
        this.createToggleButton();
        
        // تهيئة الأحداث
        this.setupEventListeners();
        
        console.log('🔔 تم تهيئة نظام الإشعارات');
        
        // عرض إشعار ترحيبي بعد ثانية واحدة
        setTimeout(() => {
            this.show('مرحباً بك', 'تم تهيئة نظام الإشعارات بنجاح', 'success');
        }, 1000);
    }

    /**
     * إنشاء زر تبديل الإشعارات
     */
    createToggleButton() {
        if (!document.querySelector('.toggle-notifications')) {
            this.toggleButton = document.createElement('button');
            this.toggleButton.className = 'toggle-notifications';
            this.toggleButton.innerHTML = '<i class="fas fa-bell"></i>';
            document.body.appendChild(this.toggleButton);
            
            this.toggleButton.addEventListener('click', () => {
                this.showDemoNotifications();
                this.toggleButton.classList.remove('pulse');
                this.hasNewNotifications = false;
            });
        } else {
            this.toggleButton = document.querySelector('.toggle-notifications');
        }
    }

    /**
     * تهيئة مستمعات الأحداث
     */
    setupEventListeners() {
        // يمكن إضافة أحداث إضافية هنا مثل استقبال الإشعارات من الخادم
        document.addEventListener('notification', (event) => {
            if (event.detail) {
                this.show(
                    event.detail.title || 'إشعار', 
                    event.detail.message, 
                    event.detail.type || 'info', 
                    event.detail.duration
                );
            }
        });
    }

    /**
     * عرض إشعارات تجريبية
     */
    showDemoNotifications() {
        this.show('تم بنجاح', 'تم حفظ التغييرات بنجاح', 'success');
        setTimeout(() => {
            this.show('معلومات', 'هناك تحديث جديد متاح للتطبيق', 'info');
        }, 500);
        setTimeout(() => {
            this.show('تنبيه', 'يرجى التحقق من الاتصال بالإنترنت', 'warning');
        }, 1000);
    }

    /**
     * إنشاء إشعار جديد
     * @param {string} title - عنوان الإشعار
     * @param {string} message - نص الإشعار
     * @param {string} type - نوع الإشعار (success, error, warning, info)
     * @param {number} duration - مدة الإشعار بالميلي ثانية (الافتراضي 5000)
     */
    show(title, message, type = 'info', duration = 5000) {
        // إنشاء عنصر الإشعار
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        
        // إنشاء المحتوى
        notification.innerHTML = `
            <div class="notification-title">
                <i class="${this.iconClasses[type]}"></i>
                ${title}
            </div>
            <div class="notification-message">${message}</div>
            <button class="notification-close">
                <i class="fas fa-times"></i>
            </button>
            <div class="notification-progress">
                <div class="notification-progress-bar"></div>
            </div>
        `;
        
        // إضافة الإشعار إلى الحاوية
        this.container.prepend(notification);
        
        // تعيين أنماط الإشعار
        notification.style.animationDuration = `${duration}ms`;
        
        // تحريك زر الإشعارات
        if (!this.hasNewNotifications) {
            this.toggleButton.classList.add('pulse');
            this.hasNewNotifications = true;
        }
        
        // تخزين مرجع الإشعار
        this.notifications.push(notification);
        
        // إظهار الإشعار بعد إضافته للـ DOM
        setTimeout(() => {
            notification.classList.add('show');
        }, 10);
        
        // إعداد معالج إغلاق الإشعار
        const closeButton = notification.querySelector('.notification-close');
        closeButton.addEventListener('click', () => {
            this.dismiss(notification);
        });
        
        // إغلاق الإشعار تلقائياً بعد المدة المحددة
        setTimeout(() => {
            this.dismiss(notification);
        }, duration);
        
        // إعادة مرجع الإشعار
        return notification;
    }

    /**
     * إخفاء إشعار معين
     * @param {Element} notification - عنصر الإشعار المراد إخفاؤه
     */
    dismiss(notification) {
        if (!notification.classList.contains('exit')) {
            notification.classList.remove('show');
            notification.classList.add('exit');
            
            // إزالة الإشعار من DOM بعد انتهاء الحركة
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
                // إزالة الإشعار من المصفوفة
                this.notifications = this.notifications.filter(n => n !== notification);
            }, 400);
        }
    }

    /**
     * إخفاء جميع الإشعارات
     */
    dismissAll() {
        [...this.notifications].forEach(notification => {
            this.dismiss(notification);
        });
    }
    
    /**
     * عرض إشعار نجاح
     * @param {string} title - عنوان الإشعار
     * @param {string} message - نص الإشعار
     * @param {number} duration - مدة الإشعار بالميلي ثانية
     */
    success(title, message, duration) {
        return this.show(title, message, 'success', duration);
    }
    
    /**
     * عرض إشعار خطأ
     * @param {string} title - عنوان الإشعار
     * @param {string} message - نص الإشعار
     * @param {number} duration - مدة الإشعار بالميلي ثانية
     */
    error(title, message, duration) {
        return this.show(title, message, 'error', duration);
    }
    
    /**
     * عرض إشعار تحذير
     * @param {string} title - عنوان الإشعار
     * @param {string} message - نص الإشعار
     * @param {number} duration - مدة الإشعار بالميلي ثانية
     */
    warning(title, message, duration) {
        return this.show(title, message, 'warning', duration);
    }
    
    /**
     * عرض إشعار معلومات
     * @param {string} title - عنوان الإشعار
     * @param {string} message - نص الإشعار
     * @param {number} duration - مدة الإشعار بالميلي ثانية
     */
    info(title, message, duration) {
        return this.show(title, message, 'info', duration);
    }
}

// إنشاء كائن عام من مدير الإشعارات
const notificationManager = new NotificationManager();

// تصدير الكائن ليكون متاحاً للاستخدام في الملفات الأخرى
window.notificationManager = notificationManager;

// تهيئة نظام الإشعارات عند اكتمال تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    notificationManager.init();
});
