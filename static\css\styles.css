/* Custom styles for store location mapper */

/* Map container */
#map {
    height: 400px;
    border-radius: 0.25rem;
}

/* Selected location marker */
.selected-marker {
    color: var(--bs-danger);
}

/* Store card hover effect */
.store-card {
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.store-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
    border-left: 3px solid #6a11cb;
}

/* Selected store card */
.store-card.selected-store {
    border-left: 3px solid var(--bs-success);
    background-color: rgba(var(--bs-success-rgb), 0.05);
}

/* Checkbox styling */
.form-check-input.store-checkbox {
    cursor: pointer;
    width: 1.2rem;
    height: 1.2rem;
}

.form-check-input.store-checkbox:checked {
    background-color: var(--bs-success);
    border-color: var(--bs-success);
    animation: pulse 0.3s ease-in-out;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

/* Share button styling */
#shareSelectedStores {
    position: relative;
    transition: all 0.3s ease;
}

#shareSelectedStores:not(:disabled):hover {
    background-color: var(--bs-success);
    color: white;
}

#selectedStoresCount {
    display: inline-block;
    min-width: 1.5rem;
    text-align: center;
}

/* Search input styling */
.search-container {
    position: relative;
    margin-bottom: 20px;
}

.search-container .form-control {
    padding-right: 40px;
    background-color: #2a2a2a;
    border: 1px solid #333;
    color: #ffffff;
    border-radius: 25px;
}

.search-container .search-icon {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #6a11cb;
}

/* Clear search button */
.clear-search-btn {
    position: absolute;
    right: 0.5rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--bs-secondary);
    padding: 0.25rem;
    border: none;
    background: transparent;
    cursor: pointer;
    opacity: 0.7;
    transition: opacity 0.2s;
}

.clear-search-btn:hover {
    opacity: 1;
    color: var(--bs-danger);
}

/* Highlighted text in search results */
mark {
    background-color: rgba(var(--bs-info-rgb), 0.3);
    color: var(--bs-info);
    border-radius: 2px;
    padding: 0 2px;
    font-weight: bold;
}

/* No results message */
#no-results-message {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Mobile responsiveness */
@media (max-width: 768px) {
    #map {
        height: 300px;
    }

    .container {
        padding-left: 10px;
        padding-right: 10px;
    }

    .card-body {
        padding: 1rem;
    }
}

/* Ensure buttons don't get too small on mobile */
.btn-group .btn-sm {
    padding: 0.25rem 0.4rem;
    font-size: 0.75rem;
}

/* Custom scrollbar for store list */
#storeList {
    max-height: 400px;
    overflow-y: auto;
    scrollbar-width: thin;
}

#storeList::-webkit-scrollbar {
    width: 6px;
}

#storeList::-webkit-scrollbar-track {
    background: var(--bs-secondary-bg);
}

#storeList::-webkit-scrollbar-thumb {
    background-color: var(--bs-secondary);
    border-radius: 3px;
}

/* Store popup styling */
.leaflet-popup-content {
    min-width: 200px;
}

.store-popup img {
    display: block;
    margin: 0 auto;
    max-width: 100%;
}

/* Image preview styling */
#imagePreviewContainer {
    text-align: center;
    margin-top: 10px;
    padding: 10px;
    border-radius: 5px;
    background-color: rgba(var(--bs-dark-rgb), 0.05);
    transition: all 0.3s ease;
}

#imagePreview {
    max-width: 100%;
    max-height: 150px;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s;
}

#imagePreview:hover {
    transform: scale(1.05);
}

#removeImage {
    margin-top: 8px;
}

/* Alert positioning */
#alertContainer {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1050;
    max-width: 300px;
}

.alert {
    border-radius: 10px;
    border: none;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    padding: 15px 20px;
}

.alert-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
}

.alert-danger {
    background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
    color: white;
}

.alert-warning {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
    color: white;
}

.alert-info {
    background: linear-gradient(135deg, #17a2b8 0%, #0dcaf0 100%);
    color: white;
}

/* تحسينات إضافية للواجهة */

/* تحسينات للخرائط */
.map-highlight {
    animation: mapHighlight 1.5s infinite;
    border: 2px solid #6a11cb;
    border-radius: 15px;
}

@keyframes mapHighlight {
    0% { box-shadow: 0 0 0 0 rgba(106, 17, 203, 0.4); }
    70% { box-shadow: 0 0 0 10px rgba(106, 17, 203, 0); }
    100% { box-shadow: 0 0 0 0 rgba(106, 17, 203, 0); }
}

/* تحسينات للأزرار */
.btn-primary {
    background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
    border: none;
    color: white;
    padding: 10px 20px;
    border-radius: 25px;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    opacity: 0.9;
}

/* تحسينات للبطاقات */
.store-card {
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.store-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
    border-left: 3px solid #6a11cb;
}

/* تحسينات للبحث */
.search-container {
    position: relative;
    margin-bottom: 20px;
}

.search-container .form-control {
    padding-right: 40px;
    background-color: #2a2a2a;
    border: 1px solid #333;
    color: #ffffff;
    border-radius: 25px;
}

.search-container .search-icon {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #6a11cb;
}

/* تحسينات للتنبيهات */
.alert {
    border-radius: 10px;
    border: none;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    padding: 15px 20px;
}

.alert-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
}

.alert-danger {
    background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
    color: white;
}

.alert-warning {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
    color: white;
}

.alert-info {
    background: linear-gradient(135deg, #17a2b8 0%, #0dcaf0 100%);
    color: white;
}

/* تحسينات للجداول */
.table {
    color: #ffffff;
    border-radius: 10px;
    overflow: hidden;
}

.table thead th {
    background: linear-gradient(135deg, #2a2a2a 0%, #1a1a1a 100%);
    border-bottom: 2px solid #333;
    padding: 15px;
}

.table td, .table th {
    border-top: 1px solid #333;
    padding: 12px;
}

/* تحسينات للصور */
.image-preview {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.image-preview img {
    transition: all 0.3s ease;
}

.image-preview img:hover {
    transform: scale(1.05);
}

/* تحسينات للتنقل */
.navbar {
    background: linear-gradient(135deg, #2a2a2a 0%, #1a1a1a 100%);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    padding: 15px 0;
}

.navbar-brand {
    font-family: 'Playfair Display', serif;
    font-weight: 700;
    font-size: 1.5rem;
}

/* تحسينات للعناوين الفرعية */
.card-header {
    background: linear-gradient(135deg, #2a2a2a 0%, #1a1a1a 100%);
    border-bottom: 1px solid #333;
    padding: 15px 20px;
    border-radius: 15px 15px 0 0;
}

/* تحسينات للأيقونات */
.fas, .fab {
    transition: all 0.3s ease;
    color: #6a11cb;
}

.fas:hover, .fab:hover {
    transform: scale(1.1);
    color: #2575fc;
}

/* تحسينات للتنقل في الصفحات */
.pagination {
    margin-top: 20px;
}

.page-link {
    background-color: #2a2a2a;
    border: 1px solid #333;
    color: #ffffff;
    transition: all 0.3s ease;
    padding: 8px 16px;
    border-radius: 5px;
}

.page-link:hover {
    background-color: #6a11cb;
    border-color: #6a11cb;
    color: white;
}

.page-item.active .page-link {
    background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
    border-color: #6a11cb;
}

/* تحسينات للتنبيهات المخصصة */
.custom-alert {
    border-radius: 10px;
    padding: 15px 20px;
    margin-bottom: 20px;
    background: linear-gradient(135deg, #2a2a2a 0%, #1a1a1a 100%);
    border: 1px solid #333;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

/* تحسينات للقوائم المنسدلة */
.dropdown-menu {
    background: linear-gradient(135deg, #2a2a2a 0%, #1a1a1a 100%);
    border: 1px solid #333;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    padding: 10px 0;
}

.dropdown-item {
    color: #ffffff;
    transition: all 0.3s ease;
    padding: 8px 20px;
}

.dropdown-item:hover {
    background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
    color: white;
}

/* تحسينات للشريط التقدم */
.progress {
    background-color: #2a2a2a;
    border-radius: 10px;
    height: 10px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.progress-bar {
    background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
    border-radius: 10px;
}

/* تحسينات للوسوم */
.badge {
    padding: 5px 10px;
    border-radius: 20px;
    font-weight: 500;
    font-size: 0.8rem;
}

.badge-primary {
    background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
}

/* تحسينات للعناوين */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Playfair Display', serif;
    font-weight: 700;
    margin-bottom: 20px;
    color: #ffffff;
}

/* تحسينات للروابط */
a {
    color: #6a11cb;
    text-decoration: none;
    transition: all 0.3s ease;
}

a:hover {
    color: #2575fc;
    text-decoration: none;
}

/* تحسينات للقوائم */
.list-group {
    background: linear-gradient(135deg, #2a2a2a 0%, #1a1a1a 100%);
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border: 1px solid #333;
}

.list-group-item {
    background-color: transparent;
    border: 1px solid #333;
    color: #ffffff;
    transition: all 0.3s ease;
    padding: 12px 20px;
}

.list-group-item:hover {
    background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
    color: white;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 576px) {
    .container {
        padding-left: 15px;
        padding-right: 15px;
    }
    
    .card {
        margin-bottom: 15px;
    }
    
    .btn {
        width: 100%;
        margin-bottom: 10px;
    }
    
    .navbar-brand {
        font-size: 1.2rem;
    }
    
    h1 { font-size: 1.8rem; }
    h2 { font-size: 1.5rem; }
    h3 { font-size: 1.3rem; }
}

/* تحسينات للشاشات المتوسطة */
@media (min-width: 768px) and (max-width: 991.98px) {
    .container {
        max-width: 720px;
    }
    
    .card {
        margin-bottom: 20px;
    }
}

/* تحسينات للشاشات الكبيرة */
@media (min-width: 992px) {
    .container {
        max-width: 960px;
    }
    
    .card {
        margin-bottom: 25px;
    }
}
