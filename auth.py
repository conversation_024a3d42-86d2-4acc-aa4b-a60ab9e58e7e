from functools import wraps
from flask import redirect, url_for, flash, request
from flask_login import current_user

def role_required(role_ids):
    """مزين للتحقق من أدوار المستخدم"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not current_user.is_authenticated:
                flash('يرجى تسجيل الدخول للوصول إلى هذه الصفحة', 'warning')
                return redirect(url_for('login', next=request.url))

            if current_user.role_id not in role_ids:
                flash('', 'danger')
                return redirect(url_for('index'))

            return f(*args, **kwargs)
        return decorated_function
    return decorator

def admin_required(f):
    """مزين للتحقق من دور المدير"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        from flask import jsonify

        if not current_user.is_authenticated:
            # إذا كان الطلب API، إرجاع JSON
            if request.is_json or request.path.startswith('/api/'):
                return jsonify({'error': 'يرجى تسجيل الدخول للوصول إلى هذه الوظيفة', 'success': False}), 401
            # إذا كان طلب صفحة عادي، إعادة توجيه
            flash('يرجى تسجيل الدخول للوصول إلى هذه الصفحة', 'warning')
            return redirect(url_for('login', next=request.url))

        if current_user.role_id != 1:  # 1 = admin
            # إذا كان الطلب API، إرجاع JSON
            if request.is_json or request.path.startswith('/api/'):
                return jsonify({'error': 'ليس لديك صلاحية للوصول إلى هذه الوظيفة', 'success': False}), 403
            # إذا كان طلب صفحة عادي، إعادة توجيه
            flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger')
            return redirect(url_for('index'))
        return f(*args, **kwargs)
    return decorated_function

def marketer_required(f):
    """مزين للتحقق من دور المسوق"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated:
            flash('يرجى تسجيل الدخول للوصول إلى هذه الصفحة', 'warning')
            return redirect(url_for('login', next=request.url))

        if current_user.role_id not in [1, 2]:  # 1 = admin, 2 = marketer
            flash('', 'danger')
            return redirect(url_for('index'))

        return f(*args, **kwargs)
    return decorated_function
