import os
import logging
from logging.handlers import RotatingFileHandler
from flask import request, has_request_context

class RequestFormatter(logging.Formatter):
    """منسق سجلات مخصص يضيف معلومات الطلب"""
    
    def format(self, record):
        if has_request_context():
            record.url = request.url
            record.remote_addr = request.remote_addr
            record.method = request.method
        else:
            record.url = None
            record.remote_addr = None
            record.method = None
            
        return super().format(record)

def setup_logger(app):
    """إعداد نظام تسجيل الأخطاء"""
    
    # التأكد من وجود مجلد السجلات
    if not os.path.exists('logs'):
        os.makedirs('logs')
    
    # تعيين منسق السجلات
    formatter = RequestFormatter(
        '[%(asctime)s] %(remote_addr)s - %(method)s %(url)s\n'
        '%(levelname)s في %(module)s: %(message)s'
    )
    
    # إعداد ملف السجلات
    file_handler = RotatingFileHandler('logs/app.log', maxBytes=1024 * 1024, backupCount=10)
    file_handler.setFormatter(formatter)
    file_handler.setLevel(logging.INFO)
    
    # إعداد مخرج السجلات للوحدة الطرفية
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    console_handler.setLevel(logging.DEBUG if app.debug else logging.INFO)
    
    # تطبيق المعالجات
    app.logger.addHandler(file_handler)
    app.logger.addHandler(console_handler)
    app.logger.setLevel(logging.DEBUG if app.debug else logging.INFO)
    
    # تعيين مستوى التسجيل لمكتبة werkzeug
    logging.getLogger('werkzeug').setLevel(logging.WARNING)
    
    return app.logger

def log_error(app, error, user_id=None, additional_info=None):
    """تسجيل خطأ مع معلومات إضافية"""
    error_info = {
        'error': str(error),
        'user_id': user_id,
        'additional_info': additional_info
    }
    app.logger.error(f"حدث خطأ: {error_info}")

def log_info(app, message, user_id=None, additional_info=None):
    """تسجيل معلومات مع بيانات إضافية"""
    info = {
        'message': message,
        'user_id': user_id,
        'additional_info': additional_info
    }
    app.logger.info(f"معلومات: {info}")

def log_warning(app, message, user_id=None, additional_info=None):
    """تسجيل تحذير مع بيانات إضافية"""
    warning_info = {
        'message': message,
        'user_id': user_id,
        'additional_info': additional_info
    }
    app.logger.warning(f"تحذير: {warning_info}")

def log_security(app, event, user_id=None, ip=None, success=False):
    """تسجيل حدث أمني"""
    security_info = {
        'event': event,
        'user_id': user_id,
        'ip': ip or (request.remote_addr if has_request_context() else None),
        'success': success
    }
    app.logger.warning(f"حدث أمني: {security_info}") 